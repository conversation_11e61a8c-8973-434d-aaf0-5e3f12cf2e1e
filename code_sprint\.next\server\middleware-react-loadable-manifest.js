self.__REACT_LOADABLE_MANIFEST="{\"node_modules/@splinetool/runtime/build/runtime.js -> ./boolean.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./boolean.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_boolean_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./gaussian-splat-compression.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./gaussian-splat-compression.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_gaussian-splat-compression_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./howler.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./howler.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_howler_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./navmesh.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./navmesh.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_navmesh_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./opentype.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./opentype.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_opentype_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./physics.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./physics.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_physics_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./process.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./process.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_process_js.js\"]},\"node_modules/@splinetool/runtime/build/runtime.js -> ./ui.js\":{\"id\":\"node_modules/@splinetool/runtime/build/runtime.js -> ./ui.js\",\"files\":[\"static/chunks/_pages-dir-browser_node_modules_splinetool_runtime_build_ui_js.js\"]}}"