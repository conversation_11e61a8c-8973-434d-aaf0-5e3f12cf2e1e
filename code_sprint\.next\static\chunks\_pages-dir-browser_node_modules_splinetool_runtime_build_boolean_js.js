"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_boolean_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/boolean.js":
/*!***********************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/boolean.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (() => {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(moduleArg = {}) {\n\nvar Module=moduleArg;var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram=\"./this.program\";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];if(typeof WebAssembly!=\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module[\"HEAP8\"]=HEAP8=new Int8Array(b);Module[\"HEAP16\"]=HEAP16=new Int16Array(b);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(b);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(b);Module[\"HEAP32\"]=HEAP32=new Int32Array(b);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(b);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(b);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(!Module[\"noFSInit\"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -sASSERTIONS for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";var isDataURI=filename=>filename.startsWith(dataURIPrefix);var wasmBinaryFile;wasmBinaryFile=\"boolean.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw\"both async and sync fetching of the wasm failed\"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+binaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(()=>getBinarySync(binaryFile))}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(instance=>instance).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming==\"function\"&&!isDataURI(binaryFile)&&typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={\"a\":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports[\"A\"];updateMemoryViews();wasmTable=wasmExports[\"C\"];addOnInit(wasmExports[\"B\"]);removeRunDependency(\"wasm-instantiate\");return wasmExports}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}if(Module[\"instantiateWasm\"]){try{return Module[\"instantiateWasm\"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);readyPromiseReject(e)}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return{}}var tempDouble;var tempI64;var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};var noExitRuntime=Module[\"noExitRuntime\"]||true;function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var tupleRegistrations={};var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function simpleReadValueFromPointer(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var InternalError;var throwInternalError=message=>{throw new InternalError(message)};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError(\"Mismatched type converter count\")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};var __embind_finalize_value_array=rawTupleType=>{var reg=tupleRegistrations[rawTupleType];delete tupleRegistrations[rawTupleType];var elements=reg.elements;var elementsLength=elements.length;var elementTypes=elements.map(elt=>elt.getterReturnType).concat(elements.map(elt=>elt.setterArgumentType));var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;whenDependentTypesAreResolved([rawTupleType],elementTypes,function(elementTypes){elements.forEach((elt,i)=>{var getterReturnType=elementTypes[i];var getter=elt.getter;var getterContext=elt.getterContext;var setterArgumentType=elementTypes[i+elementsLength];var setter=elt.setter;var setterContext=elt.setterContext;elt.read=ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr));elt.write=(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv=new Array(elementsLength);for(var i=0;i<elementsLength;++i){rv[i]=elements[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{if(elementsLength!==o.length){throw new TypeError(`Incorrect number of tuple elements for ${reg.name}: expected=${elementsLength}, actual=${o.length}`)}var ptr=rawConstructor();for(var i=0;i<elementsLength;++i){elements[i].write(ptr,o[i])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret=\"\";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type \"${name}\" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!(\"argPackAdvance\"in registeredInstance)){throw new TypeError(\"registerType registeredInstance requires argPackAdvance\")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(wt){return!!wt},\"toWireType\":function(destructors,o){return o?trueValue:falseValue},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":function(pointer){return this[\"fromWireType\"](HEAPU8[pointer])},destructorFunction:null})};function handleAllocatorInit(){Object.assign(HandleAllocator.prototype,{get(id){return this.allocated[id]},has(id){return this.allocated[id]!==undefined},allocate(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id},free(id){this.allocated[id]=undefined;this.freelist.push(id)}})}function HandleAllocator(){this.allocated=[undefined];this.freelist=[]}var emval_handles=new HandleAllocator;var __emval_decref=handle=>{if(handle>=emval_handles.reserved&&0===--emval_handles.get(handle).refcount){emval_handles.free(handle)}};var count_emval_handles=()=>{var count=0;for(var i=emval_handles.reserved;i<emval_handles.allocated.length;++i){if(emval_handles.allocated[i]!==undefined){++count}}return count};var init_emval=()=>{emval_handles.allocated.push({value:undefined},{value:null},{value:true},{value:false});emval_handles.reserved=emval_handles.allocated.length;Module[\"count_emval_handles\"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError(\"Cannot use deleted val. handle = \"+handle)}return emval_handles.get(handle).value},toHandle:value=>{switch(value){case undefined:return 1;case null:return 2;case true:return 3;case false:return 4;default:{return emval_handles.allocate({refcount:1,value:value})}}}};var __embind_register_emval=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},\"toWireType\":(destructors,value)=>Emval.toHandle(value),\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:null})};var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}};var enumReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?function(pointer){return this[\"fromWireType\"](HEAP8[pointer>>0])}:function(pointer){return this[\"fromWireType\"](HEAPU8[pointer>>0])};case 2:return signed?function(pointer){return this[\"fromWireType\"](HEAP16[pointer>>1])}:function(pointer){return this[\"fromWireType\"](HEAPU16[pointer>>1])};case 4:return signed?function(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}:function(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])};default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_enum=(rawType,name,size,isSigned)=>{name=readLatin1String(name);function ctor(){}ctor.values={};registerType(rawType,{name:name,constructor:ctor,\"fromWireType\":function(c){return this.constructor.values[c]},\"toWireType\":(destructors,c)=>c.value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":enumReadValueFromPointer(name,size,isSigned),destructorFunction:null});exposePublicSymbol(name,ctor)};var createNamedFunction=(name,body)=>Object.defineProperty(body,\"name\",{value:name});var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+\" has unknown type \"+getTypeName(rawType))}return impl};var __embind_register_enum_value=(rawEnumType,name,enumValue)=>{var enumType=requireRegisteredType(rawEnumType,\"enum\");name=readLatin1String(name);var Enum=enumType.constructor;var Value=Object.create(enumType.constructor.prototype,{value:{value:enumValue},constructor:{value:createNamedFunction(`${enumType.name}_${name}`,function(){})}});Enum.values[enumValue]=Value;Enum[name]=Value};var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this[\"fromWireType\"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this[\"fromWireType\"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":value=>value,\"toWireType\":(destructors,value)=>value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":floatReadValueFromPointer(name,size),destructorFunction:null})};function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||\"unknownFunctionName\",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError(\"argTypes array size mismatch! Must at least get return value and 'this' types!\")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!==\"void\";var argsList=\"\";var argsListWired=\"\";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?\", \":\"\")+\"arg\"+i;argsListWired+=(i!==0?\", \":\"\")+\"arg\"+i+\"Wired\"}var invokerFnBody=`\\n        return function (${argsList}) {\\n        if (arguments.length !== ${argCount-2}) {\\n          throwBindingError('function ${humanName} called with ' + arguments.length + ' arguments, expected ${argCount-2}');\\n        }`;if(needsDestructorStack){invokerFnBody+=\"var destructors = [];\\n\"}var dtorStack=needsDestructorStack?\"destructors\":\"null\";var args1=[\"throwBindingError\",\"invoker\",\"fn\",\"runDestructors\",\"retType\",\"classParam\"];var args2=[throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];if(isClassMethodFunc){invokerFnBody+=\"var thisWired = classParam.toWireType(\"+dtorStack+\", this);\\n\"}for(var i=0;i<argCount-2;++i){invokerFnBody+=\"var arg\"+i+\"Wired = argType\"+i+\".toWireType(\"+dtorStack+\", arg\"+i+\"); // \"+argTypes[i+2].name+\"\\n\";args1.push(\"argType\"+i);args2.push(argTypes[i+2])}if(isClassMethodFunc){argsListWired=\"thisWired\"+(argsListWired.length>0?\", \":\"\")+argsListWired}invokerFnBody+=(returns||isAsync?\"var rv = \":\"\")+\"invoker(fn\"+(argsListWired.length>0?\", \":\"\")+argsListWired+\");\\n\";if(needsDestructorStack){invokerFnBody+=\"runDestructors(destructors);\\n\"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?\"thisWired\":\"arg\"+(i-2)+\"Wired\";if(argTypes[i].destructorFunction!==null){invokerFnBody+=paramName+\"_dtor(\"+paramName+\"); // \"+argTypes[i].name+\"\\n\";args1.push(paramName+\"_dtor\");args2.push(argTypes[i].destructorFunction)}}}if(returns){invokerFnBody+=\"var ret = retType.fromWireType(rv);\\n\"+\"return ret;\\n\"}else{}invokerFnBody+=\"}\\n\";args1.push(invokerFnBody);var invokerFn=newFunc(Function,args1).apply(null,args2);return createNamedFunction(humanName,invokerFn)}var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError(\"Replacing nonexistant public symbol\")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var dynCallLegacy=(sig,ptr,args)=>{var f=Module[\"dynCall_\"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var dynCall=(sig,ptr,args)=>{if(sig.includes(\"j\")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn};var getDynCaller=(sig,ptr)=>{var argCache=[];return function(){argCache.length=0;Object.assign(argCache,arguments);return dynCall(sig,ptr,argCache)}};var embind__requireFunction=(signature,rawFunction)=>{signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes(\"j\")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!=\"function\"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};var extendError=(baseErrorType,errorName)=>{var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+\"\\n\"+stack.replace(/^Error(:[^\\n]*)?\\n/,\"\")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return`${this.name}: ${this.message}`}};return errorClass};var UnboundTypeError;var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([\", \"]))};var getFunctionName=signature=>{signature=signature.trim();const argsIndex=signature.indexOf(\"(\");if(argsIndex!==-1){return signature.substr(0,argsIndex)}else{return signature}};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);name=getFunctionName(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer>>0]:pointer=>HEAPU8[pointer>>0];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes(\"unsigned\");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,\"fromWireType\":fromWireType,\"toWireType\":toWireType,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":decodeMemoryView,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":decodeMemoryView},{ignoreDuplicateRegistrations:true})};function readPointer(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])}var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var UTF8Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf8\"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str=\"\";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\";var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name===\"std::string\";registerType(rawType,{name:name,\"fromWireType\"(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join(\"\")}_free(value);return str},\"toWireType\"(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value==\"string\";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError(\"Cannot pass non-string to std::string\")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError(\"String has UTF-16 code units that do not fit in 8 bits\")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf-16le\"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str=\"\";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str=\"\";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=()=>HEAPU16;shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=()=>HEAPU32;shift=2}registerType(rawType,{name:name,\"fromWireType\":value=>{var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},\"toWireType\":(destructors,value)=>{if(!(typeof value==\"string\")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_value_array=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{tupleRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),elements:[]}};var __embind_register_value_array_element=(rawTupleType,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{tupleRegistrations[rawTupleType].elements.push({getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,\"argPackAdvance\":0,\"fromWireType\":()=>undefined,\"toWireType\":(destructors,o)=>undefined})};var _abort=()=>{abort(\"\")};var _emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||\"./this.program\";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var PATH={isAbs:path=>path.charAt(0)===\"/\",splitPath:filename=>{var splitPathRe=/^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last===\".\"){parts.splice(i,1)}else if(last===\"..\"){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift(\"..\")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)===\"/\";path=PATH.normalizeArray(path.split(\"/\").filter(p=>!!p),!isAbsolute).join(\"/\");if(!path&&!isAbsolute){path=\".\"}if(path&&trailingSlash){path+=\"/\"}return(isAbsolute?\"/\":\"\")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return\".\"}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path===\"/\")return\"/\";path=PATH.normalize(path);path=path.replace(/\\/$/,\"\");var lastSlash=path.lastIndexOf(\"/\");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join(\"/\"))},join2:(l,r)=>PATH.normalize(l+\"/\"+r)};var initRandomFill=()=>{if(typeof crypto==\"object\"&&typeof crypto[\"getRandomValues\"]==\"function\"){return view=>crypto.getRandomValues(view)}else abort(\"initRandomDevice\")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:function(){var resolvedPath=\"\",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=\"string\"){throw new TypeError(\"Arguments to path.resolve must be strings\")}else if(!path){return\"\"}resolvedPath=path+\"/\"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split(\"/\").filter(p=>!!p),!resolvedAbsolute).join(\"/\");return(resolvedAbsolute?\"/\":\"\")+resolvedPath||\".\"},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!==\"\")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!==\"\")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split(\"/\"));var toParts=trim(to.split(\"/\"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push(\"..\")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join(\"/\")}};var FS_stdin_getChar_buffer=[];function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(typeof window!=\"undefined\"&&typeof window.prompt==\"function\"){result=window.prompt(\"Input: \");if(result!==null){result+=\"\\n\"}}else if(typeof readline==\"function\"){result=readline();if(result!==null){result+=\"\\n\"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,\"/\",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[\".\",\"..\"];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):\"\";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file \"${url}\" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file \"${url}\" failed.`}});if(dep)addRunDependency(dep)};var FS_createDataFile=(parent,name,fileData,canRead,canWrite,canOwn)=>{FS.createDataFile(parent,name,fileData,canRead,canWrite,canOwn)};var preloadPlugins=Module[\"preloadPlugins\"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!=\"undefined\")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin[\"canHandle\"](fullname)){plugin[\"handle\"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url==\"string\"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={\"r\":0,\"r+\":2,\"w\":512|64|1,\"w+\":512|64|2,\"a\":1024|64|1,\"a+\":1024|64|2};var flags=flagModes[str];if(typeof flags==\"undefined\"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:\"/\",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:\"\",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split(\"/\").filter(p=>!!p);var current=FS.root;var current_path=\"/\";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!==\"/\"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=[\"r\",\"w\",\"rw\"][flag&3];if(flag&512){perms+=\"w\"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes(\"r\")&&!(node.mode&292)){return 2}else if(perms.includes(\"w\")&&!(node.mode&146)){return 2}else if(perms.includes(\"x\")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){var errCode=FS.nodePermissions(dir,\"x\");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,\"wx\")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,\"wx\");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!==\"r\"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get(){return this.node},set(val){this.node=val}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}},flags:{get(){return this.shared.flags},set(val){this.shared.flags=val}},position:{get(){return this.shared.position},set(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate==\"function\"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint===\"/\";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name===\".\"||name===\"..\"){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split(\"/\");var d=\"\";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+=\"/\"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev==\"undefined\"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===\"\"){throw new FS.ErrnoError(44)}flags=typeof flags==\"string\"?FS_modeStringToFlags(flags):flags;mode=typeof mode==\"undefined\"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==\"object\"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module[\"logReadFiles\"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||\"binary\";if(opts.encoding!==\"utf8\"&&opts.encoding!==\"binary\"){throw new Error(`Invalid encoding type \"${opts.encoding}\"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding===\"utf8\"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding===\"binary\"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==\"string\"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error(\"Unsupported data type\")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,\"x\");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir(\"/tmp\");FS.mkdir(\"/home\");FS.mkdir(\"/home/<USER>")},createDefaultDevices(){FS.mkdir(\"/dev\");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev(\"/dev/null\",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev(\"/dev/tty\",FS.makedev(5,0));FS.mkdev(\"/dev/tty1\",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice(\"/dev\",\"random\",randomByte);FS.createDevice(\"/dev\",\"urandom\",randomByte);FS.mkdir(\"/dev/shm\");FS.mkdir(\"/dev/shm/tmp\")},createSpecialDirectories(){FS.mkdir(\"/proc\");var proc_self=FS.mkdir(\"/proc/self\");FS.mkdir(\"/proc/self/fd\");FS.mount({mount(){var node=FS.createNode(proc_self,\"fd\",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:\"fake\"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},\"/proc/self/fd\")},createStandardStreams(){if(Module[\"stdin\"]){FS.createDevice(\"/dev\",\"stdin\",Module[\"stdin\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdin\")}if(Module[\"stdout\"]){FS.createDevice(\"/dev\",\"stdout\",null,Module[\"stdout\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdout\")}if(Module[\"stderr\"]){FS.createDevice(\"/dev\",\"stderr\",null,Module[\"stderr\"])}else{FS.symlink(\"/dev/tty1\",\"/dev/stderr\")}var stdin=FS.open(\"/dev/stdin\",0);var stdout=FS.open(\"/dev/stdout\",1);var stderr=FS.open(\"/dev/stderr\",1)},ensureErrnoError(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name=\"ErrnoError\";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message=\"FS error\"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack=\"<generic error, no stack>\"})},staticInit(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},\"/\");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={\"MEMFS\":MEMFS}},init(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module[\"stdin\"]=input||Module[\"stdin\"];Module[\"stdout\"]=output||Module[\"stdout\"];Module[\"stderr\"]=error||Module[\"stderr\"];FS.createStandardStreams()},quit(){FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path===\"/\"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent==\"string\"?parent:FS.getPath(parent);var parts=path.split(\"/\").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent==\"string\"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==\"string\"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!=\"undefined\"){throw new Error(\"Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.\")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error(\"Cannot load without read() or XMLHttpRequest.\")}},createLazyFile(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open(\"HEAD\",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);var datalength=Number(xhr.getResponseHeader(\"Content-length\"));var header;var hasByteServing=(header=xhr.getResponseHeader(\"Accept-Ranges\"))&&header===\"bytes\";var usesGzip=(header=xhr.getResponseHeader(\"Content-Encoding\"))&&header===\"gzip\";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error(\"invalid range (\"+from+\", \"+to+\") or no bytes requested!\");if(to>datalength-1)throw new Error(\"only \"+datalength+\" bytes available! programmer error!\");var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);if(datalength!==chunkSize)xhr.setRequestHeader(\"Range\",\"bytes=\"+from+\"-\"+to);xhr.responseType=\"arraybuffer\";if(xhr.overrideMimeType){xhr.overrideMimeType(\"text/plain; charset=x-user-defined\")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||\"\",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==\"undefined\"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==\"undefined\")throw new Error(\"doXHR failed!\");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out(\"LazyFiles on gzip forces download of the whole file when length is accessed\")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=\"undefined\"){if(!ENVIRONMENT_IS_WORKER)throw\"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc\";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var isLeapYear=year=>year%4===0&&(year%100!==0||year%400===0);var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value==\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={\"%a\":date=>WEEKDAYS[date.tm_wday].substring(0,3),\"%A\":date=>WEEKDAYS[date.tm_wday],\"%b\":date=>MONTHS[date.tm_mon].substring(0,3),\"%B\":date=>MONTHS[date.tm_mon],\"%C\":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":date=>leadingNulls(date.tm_mday,2),\"%e\":date=>leadingSomething(date.tm_mday,2,\" \"),\"%g\":date=>getWeekBasedYear(date).toString().substring(2),\"%G\":date=>getWeekBasedYear(date),\"%H\":date=>leadingNulls(date.tm_hour,2),\"%I\":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":date=>leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3),\"%m\":date=>leadingNulls(date.tm_mon+1,2),\"%M\":date=>leadingNulls(date.tm_min,2),\"%n\":()=>\"\\n\",\"%p\":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}return\"PM\"},\"%S\":date=>leadingNulls(date.tm_sec,2),\"%t\":()=>\"\\t\",\"%u\":date=>date.tm_wday||7,\"%U\":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},\"%V\":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},\"%w\":date=>date.tm_wday,\"%W\":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},\"%y\":date=>(date.tm_year+1900).toString().substring(2),\"%Y\":date=>date.tm_year+1900,\"%z\":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":date=>date.tm_zone,\"%%\":()=>\"%\"};pattern=pattern.replace(/%%/g,\"\\0\\0\");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\\0\\0/g,\"%\");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>_strftime(s,maxsize,format,tm);var getCFunc=ident=>{var func=Module[\"_\"+ident];return func};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={\"string\":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},\"array\":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType===\"string\"){return UTF8ToString(ret)}if(returnType===\"boolean\")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};InternalError=Module[\"InternalError\"]=class InternalError extends Error{constructor(message){super(message);this.name=\"InternalError\"}};embind_init_charCodes();BindingError=Module[\"BindingError\"]=class BindingError extends Error{constructor(message){super(message);this.name=\"BindingError\"}};handleAllocatorInit();init_emval();UnboundTypeError=Module[\"UnboundTypeError\"]=extendError(Error,\"UnboundTypeError\");var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();var wasmImports={a:___cxa_throw,m:__embind_finalize_value_array,p:__embind_register_bigint,k:__embind_register_bool,y:__embind_register_emval,z:__embind_register_enum,f:__embind_register_enum_value,j:__embind_register_float,d:__embind_register_function,e:__embind_register_integer,b:__embind_register_memory_view,h:__embind_register_std_string,g:__embind_register_std_wstring,n:__embind_register_value_array,c:__embind_register_value_array_element,l:__embind_register_void,i:_abort,x:_emscripten_memcpy_js,u:_emscripten_resize_heap,r:_environ_get,s:_environ_sizes_get,w:_fd_close,t:_fd_read,o:_fd_seek,v:_fd_write,q:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports[\"B\"])();var ___getTypeName=a0=>(___getTypeName=wasmExports[\"D\"])(a0);var ___errno_location=()=>(___errno_location=wasmExports[\"__errno_location\"])();var _malloc=Module[\"_malloc\"]=a0=>(_malloc=Module[\"_malloc\"]=wasmExports[\"E\"])(a0);var _free=Module[\"_free\"]=a0=>(_free=Module[\"_free\"]=wasmExports[\"F\"])(a0);var stackSave=()=>(stackSave=wasmExports[\"G\"])();var stackRestore=a0=>(stackRestore=wasmExports[\"H\"])(a0);var stackAlloc=a0=>(stackAlloc=wasmExports[\"I\"])(a0);var ___cxa_increment_exception_refcount=a0=>(___cxa_increment_exception_refcount=wasmExports[\"__cxa_increment_exception_refcount\"])(a0);var ___cxa_is_pointer_type=a0=>(___cxa_is_pointer_type=wasmExports[\"J\"])(a0);var dynCall_jiji=Module[\"dynCall_jiji\"]=(a0,a1,a2,a3,a4)=>(dynCall_jiji=Module[\"dynCall_jiji\"]=wasmExports[\"K\"])(a0,a1,a2,a3,a4);var dynCall_viijii=Module[\"dynCall_viijii\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_viijii=Module[\"dynCall_viijii\"]=wasmExports[\"L\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=wasmExports[\"M\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=wasmExports[\"N\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=wasmExports[\"O\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);Module[\"ccall\"]=ccall;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();\n\n\n  return moduleArg.ready\n}\n);\n})();\n;\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/boolean.js\n"));

/***/ })

}]);