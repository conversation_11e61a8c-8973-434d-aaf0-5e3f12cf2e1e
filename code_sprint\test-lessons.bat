@echo off
echo 🧪 Testing SPRINT - AI Lessons
echo ==============================

echo 📁 Starting frontend...
cd frontend

echo 🔧 Installing dependencies...
npm install --legacy-peer-deps --silent

echo 🚀 Starting development server...
echo.
echo 🌐 Frontend starting at: http://localhost:3000
echo.
echo 📋 Test these lesson URLs:
echo    ✅ Level 1 Main: http://localhost:3000/lessons/level-1
echo    ✅ What is Computer: http://localhost:3000/lessons/level1/1.1WhatIsComputer
echo    ✅ How Computers Work: http://localhost:3000/lessons/level1/1.2HowComputersWork
echo    ✅ Level 2 Main: http://localhost:3000/lessons/level-2
echo    ✅ Level 3 Main: http://localhost:3000/lessons/level-3
echo.
echo 🎨 Check for SPRINT - AI logos on:
echo    ✅ Home: http://localhost:3000
echo    ✅ Login: http://localhost:3000/login
echo    ✅ Dashboard: http://localhost:3000/dashboard
echo.
echo 🔍 Progress tracking now uses local storage fallback
echo    (No backend required for testing)
echo.

npm run dev
