const fs = require('fs');
const path = require('path');

console.log('🔧 Fixing SPRINT - AI Logo File Names');
console.log('=====================================\n');

// Define the directories to check
const directories = [
  'public',
  'frontend/public'
];

// Define the file renames needed
const renames = [
  {
    from: 'sprint-ai-logo-main.png.png',
    to: 'sprint-ai-logo-main.png'
  },
  {
    from: 'sprint-ai-logo-compact.png.png', 
    to: 'sprint-ai-logo-compact.png'
  },
  {
    from: 'sprint-ai-logo-icon.png.png',
    to: 'sprint-ai-logo-icon.png'
  },
  {
    from: 'sprint-ai-logo-icon.png.jpg',
    to: 'sprint-ai-logo-icon.png'
  }
];

function fixFileNames(directory) {
  const fullDir = path.join(__dirname, directory);
  
  if (!fs.existsSync(fullDir)) {
    console.log(`❌ Directory not found: ${directory}`);
    return;
  }
  
  console.log(`📁 Checking ${directory}:`);
  
  renames.forEach(rename => {
    const fromPath = path.join(fullDir, rename.from);
    const toPath = path.join(fullDir, rename.to);
    
    if (fs.existsSync(fromPath)) {
      try {
        // If target already exists, remove it first
        if (fs.existsSync(toPath)) {
          fs.unlinkSync(toPath);
        }
        
        fs.renameSync(fromPath, toPath);
        console.log(`✅ Renamed: ${rename.from} → ${rename.to}`);
      } catch (error) {
        console.log(`❌ Error renaming ${rename.from}: ${error.message}`);
      }
    } else {
      console.log(`ℹ️  File not found: ${rename.from}`);
    }
  });
  
  console.log('');
}

function copyLogosToRoot() {
  const frontendDir = path.join(__dirname, 'frontend/public');
  const rootDir = path.join(__dirname, 'public');
  
  const logoFiles = [
    'sprint-ai-logo-main.png',
    'sprint-ai-logo-compact.png',
    'sprint-ai-logo-icon.png',
    'sprint-ai-logo-white.png'
  ];
  
  console.log('📋 Copying logos from frontend to root public directory:');
  
  logoFiles.forEach(logoFile => {
    const sourcePath = path.join(frontendDir, logoFile);
    const targetPath = path.join(rootDir, logoFile);
    
    if (fs.existsSync(sourcePath)) {
      try {
        fs.copyFileSync(sourcePath, targetPath);
        console.log(`✅ Copied: ${logoFile}`);
      } catch (error) {
        console.log(`❌ Error copying ${logoFile}: ${error.message}`);
      }
    } else {
      console.log(`⚠️  Source not found: ${logoFile}`);
    }
  });
  
  console.log('');
}

// Main execution
function main() {
  // Step 1: Fix file names in both directories
  directories.forEach(dir => {
    fixFileNames(dir);
  });
  
  // Step 2: Copy logos from frontend to root
  copyLogosToRoot();
  
  // Step 3: List final logo files
  console.log('📊 Final Logo Files:');
  console.log('===================');
  
  directories.forEach(dir => {
    const fullDir = path.join(__dirname, dir);
    console.log(`\n${dir}/:`);
    
    if (fs.existsSync(fullDir)) {
      const files = fs.readdirSync(fullDir);
      const logoFiles = files.filter(file => file.startsWith('sprint-ai-logo'));
      
      if (logoFiles.length > 0) {
        logoFiles.forEach(file => {
          console.log(`  ✅ ${file}`);
        });
      } else {
        console.log('  ❌ No SPRINT - AI logo files found');
      }
    }
  });
  
  console.log('\n🎉 Logo file names fixed!');
  console.log('\n🚀 Next steps:');
  console.log('1. Start your application: npm run dev');
  console.log('2. Check pages for updated logos');
  console.log('3. Verify no 404 errors in browser console');
}

// Run the script
main();
