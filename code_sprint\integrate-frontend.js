const fs = require('fs');
const path = require('path');

console.log('🔄 SPRINT - AI Frontend Integration Script');
console.log('==========================================\n');

// Define source and destination paths
const rootPages = 'pages';
const frontendPages = 'frontend/src/pages';
const rootPublic = 'public';
const frontendPublic = 'frontend/public';

// Files and directories to copy
const itemsToCopy = [
  // Lesson pages
  { from: `${rootPages}/lessons`, to: `${frontendPages}/lessons`, type: 'directory' },
  
  // Data files
  { from: 'data', to: 'frontend/src/data', type: 'directory' },
  
  // Utils
  { from: 'utils', to: 'frontend/src/utils', type: 'directory' },
  
  // Styles
  { from: 'styles', to: 'frontend/src/styles', type: 'directory' },
  
  // Logo files
  { from: `${rootPublic}/sprint-ai-logo-main.svg`, to: `${frontendPublic}/sprint-ai-logo-main.svg`, type: 'file' },
  { from: `${rootPublic}/level1-icon.svg`, to: `${frontendPublic}/level1-icon.svg`, type: 'file' },
  { from: `${rootPublic}/level2-icon.svg`, to: `${frontendPublic}/level2-icon.svg`, type: 'file' },
  { from: `${rootPublic}/level3-icon.svg`, to: `${frontendPublic}/level3-icon.svg`, type: 'file' },
  { from: `${rootPublic}/level4-icon.svg`, to: `${frontendPublic}/level4-icon.svg`, type: 'file' },
  { from: `${rootPublic}/level5-icon.svg`, to: `${frontendPublic}/level5-icon.svg`, type: 'file' },
];

function copyFile(source, destination) {
  try {
    // Create destination directory if it doesn't exist
    const destDir = path.dirname(destination);
    if (!fs.existsSync(destDir)) {
      fs.mkdirSync(destDir, { recursive: true });
    }
    
    fs.copyFileSync(source, destination);
    return true;
  } catch (error) {
    console.log(`❌ Error copying file ${source}: ${error.message}`);
    return false;
  }
}

function copyDirectory(source, destination) {
  try {
    if (!fs.existsSync(source)) {
      console.log(`⚠️  Source directory not found: ${source}`);
      return false;
    }
    
    // Create destination directory
    if (!fs.existsSync(destination)) {
      fs.mkdirSync(destination, { recursive: true });
    }
    
    // Copy all files and subdirectories
    const items = fs.readdirSync(source);
    let copiedCount = 0;
    
    items.forEach(item => {
      const sourcePath = path.join(source, item);
      const destPath = path.join(destination, item);
      
      const stat = fs.statSync(sourcePath);
      
      if (stat.isDirectory()) {
        copyDirectory(sourcePath, destPath);
        copiedCount++;
      } else {
        if (copyFile(sourcePath, destPath)) {
          copiedCount++;
        }
      }
    });
    
    console.log(`✅ Copied ${copiedCount} items from ${source} to ${destination}`);
    return true;
  } catch (error) {
    console.log(`❌ Error copying directory ${source}: ${error.message}`);
    return false;
  }
}

function updateImportPaths() {
  console.log('\n📝 Updating import paths in copied files...');
  
  const filesToUpdate = [
    'frontend/src/data/level1Data.js',
    'frontend/src/pages/lessons/level-1.js',
    'frontend/src/pages/lessons/level-2.js',
    'frontend/src/pages/lessons/level-3.js',
  ];
  
  filesToUpdate.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    
    if (fs.existsSync(fullPath)) {
      try {
        let content = fs.readFileSync(fullPath, 'utf8');
        
        // Update import paths
        content = content.replace(/from "\.\.\/pages\/lessons/g, 'from "../pages/lessons');
        content = content.replace(/from "\.\.\/\.\.\/data/g, 'from "../../data');
        content = content.replace(/from "\.\.\/\.\.\/utils/g, 'from "../../utils');
        content = content.replace(/from "\.\.\/data/g, 'from "../../data');
        
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Updated imports in ${filePath}`);
      } catch (error) {
        console.log(`❌ Error updating ${filePath}: ${error.message}`);
      }
    } else {
      console.log(`⚠️  File not found: ${filePath}`);
    }
  });
}

function createLessonRoutes() {
  console.log('\n🛣️  Creating lesson route files...');
  
  // Create level-1 route
  const level1Route = `import React from 'react';
import Level1Lessons from './level1/Level1Lessons';

export default function Level1() {
  return <Level1Lessons />;
}`;

  const level1Path = path.join(__dirname, 'frontend/src/pages/lessons/level-1.js');
  
  try {
    fs.writeFileSync(level1Path, level1Route, 'utf8');
    console.log('✅ Created /lessons/level-1 route');
  } catch (error) {
    console.log(`❌ Error creating level-1 route: ${error.message}`);
  }
}

function fixLogoReferences() {
  console.log('\n🎨 Fixing logo references in frontend...');
  
  const filesToFix = [
    'frontend/src/pages/index.jsx',
    'frontend/src/pages/dashboard.jsx',
    'frontend/src/pages/login.jsx',
    'frontend/src/pages/register.jsx',
    'frontend/src/pages/courses.js'
  ];
  
  filesToFix.forEach(filePath => {
    const fullPath = path.join(__dirname, filePath);
    
    if (fs.existsSync(fullPath)) {
      try {
        let content = fs.readFileSync(fullPath, 'utf8');
        
        // Update logo references
        content = content.replace(/\/codesprint-logo\.(png|svg)/g, '/sprint-ai-logo-main.svg');
        content = content.replace(/CodeSprint Logo/g, 'SPRINT - AI Logo');
        
        fs.writeFileSync(fullPath, content, 'utf8');
        console.log(`✅ Fixed logo references in ${filePath}`);
      } catch (error) {
        console.log(`❌ Error fixing ${filePath}: ${error.message}`);
      }
    }
  });
}

// Main execution
function main() {
  console.log('🚀 Starting frontend integration...\n');
  
  // Step 1: Copy all necessary files
  console.log('📁 Copying files and directories...');
  itemsToCopy.forEach(item => {
    const sourcePath = path.join(__dirname, item.from);
    const destPath = path.join(__dirname, item.to);
    
    if (item.type === 'directory') {
      copyDirectory(sourcePath, destPath);
    } else {
      if (fs.existsSync(sourcePath)) {
        if (copyFile(sourcePath, destPath)) {
          console.log(`✅ Copied ${item.from} to ${item.to}`);
        }
      } else {
        console.log(`⚠️  Source not found: ${item.from}`);
      }
    }
  });
  
  // Step 2: Update import paths
  updateImportPaths();
  
  // Step 3: Create lesson routes
  createLessonRoutes();
  
  // Step 4: Fix logo references
  fixLogoReferences();
  
  // Step 5: Summary
  console.log('\n🎉 Frontend integration completed!');
  console.log('\n🚀 Next steps:');
  console.log('1. cd frontend');
  console.log('2. npm run dev');
  console.log('3. Test: http://localhost:3000/lessons/level-1');
  console.log('4. Check logos on all pages');
  
  console.log('\n📋 Available lesson routes:');
  console.log('- http://localhost:3000/lessons/level-1');
  console.log('- http://localhost:3000/lessons/level-2');
  console.log('- http://localhost:3000/lessons/level-3');
  console.log('- http://localhost:3000/lessons/level-4');
  console.log('- http://localhost:3000/lessons/level-5');
}

// Run the script
main();
