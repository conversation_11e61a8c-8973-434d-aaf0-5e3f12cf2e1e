# CodeSprint - Educational Platform

A comprehensive educational platform built with Next.js frontend and Node.js backend, featuring interactive lessons, progress tracking, and payment integration.

## 🚀 Project Structure

```
code_sprint/
├── frontend/           # Next.js frontend application
├── backend/           # Node.js/Express backend API
├── public/           # Static assets (images, icons)
├── pages/           # Next.js pages (root level)
├── styles/          # CSS modules and global styles
├── utils/           # Utility functions
├── models/          # Database models
└── data/           # Static data files
```

## 📋 Prerequisites

Before running this project, make sure you have:

- **Node.js** (v16 or higher)
- **npm** or **yarn**
- **MongoDB** (local or MongoDB Atlas)
- **Git**

## 🛠️ Installation & Setup

### 1. Clone the Repository
```bash
git clone <repository-url>
cd code_sprint
```

### 2. Install Dependencies

#### For Root Application:
```bash
npm install --legacy-peer-deps
```

#### For Frontend (if running separately):
```bash
cd frontend
npm install --legacy-peer-deps
cd ..
```

#### For Backend:
```bash
cd backend
npm install
cd ..
```

### 3. Environment Configuration

Create a `.env` file in the root directory:

```env
# MongoDB Connection String (REQUIRED)
MONGODB_URI=mongodb://localhost:27017/codesprint
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/database_name

# JWT Secret for authentication (REQUIRED)
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random

# Replicate API for AI features (optional)
REPLICATE_API_TOKEN=your_replicate_token_here

# PayCaps Payment Integration (optional)
PAYCAPS_APP_ID=your_paycaps_app_id
PAYCAPS_SECRET=your_paycaps_secret
PAYCAPS_MERCHANT_NAME=your_merchant_name
NEXT_PUBLIC_PAYCAPS_PAYMENT_REQUEST_URL=https://api.paycaps.com/payment-request
```

### 4. Database Setup

#### Option A: Local MongoDB
1. Install MongoDB locally
2. Start MongoDB service:
   ```bash
   mongod
   ```
3. Use connection string: `mongodb://localhost:27017/codesprint`

#### Option B: MongoDB Atlas
1. Create account at [MongoDB Atlas](https://www.mongodb.com/atlas)
2. Create a cluster and database
3. Get connection string and update `.env`

## 🚀 Running the Application

### Method 1: Run Full Stack (Recommended)

1. **Start Backend Server:**
   ```bash
   cd backend
   npm run dev
   # Backend runs on http://localhost:5000
   ```

2. **Start Frontend (in new terminal):**
   ```bash
   # From root directory
   npm run dev
   # Frontend runs on http://localhost:3000
   ```

### Method 2: Run Frontend Only
```bash
cd frontend
npm run dev
# Runs on http://localhost:3000
```

### Method 3: Production Build
```bash
npm run build
npm start
```

## 🔧 Available Scripts

### Root Level Scripts:
- `npm run dev` - Start development server
- `npm run build` - Build for production
- `npm start` - Start production server
- `npm run lint` - Run ESLint
- `npm run setup` - Install all dependencies (root, backend, frontend)
- `npm run check-images` - Verify all images are present
- `npm run dev:full` - Start both backend and frontend together
- `npm run dev:backend` - Start only backend
- `npm run dev:frontend` - Start only frontend

### Backend Scripts:
- `npm run dev` - Start backend with nodemon
- `npm start` - Start backend server

## 🖼️ Image Assets

The project uses images stored in the `public/` folder:

### Key Image Directories:
- `/public/lvl1_img/` - Level 1 lesson images
- `/public/` - General assets (logos, charts, game images)

### Recently Fixed Images:
- ✅ Level icons (`level1-icon.svg` to `level5-icon.svg`) - Created custom SVG icons
- ✅ Chart images - Updated paths to use existing chart images:
  - `/trends_chart.jpg` → `/line_charts.png`
  - `/comparison_chart.jpg` → `/bar_charts.png`
  - `/insight_pie_chart.jpg` → `/pie_charts.png`
- ✅ Celebration image - Updated to use `/glitch-mascot.png`
- ✅ All lesson images in Level 1 (`/lvl1_img/` folder)

## 🌐 Application URLs

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000
- **API Routes:** http://localhost:3000/api/* (proxied to backend)

### Key Pages:
- Home: http://localhost:3000
- Login: http://localhost:3000/login
- Register: http://localhost:3000/register
- Dashboard: http://localhost:3000/dashboard
- Courses: http://localhost:3000/courses
- Lessons: http://localhost:3000/lessons/level-1

## 🔍 Testing Image Loading

To verify all images are loading correctly:

1. **Start the application** (see Running section above)
2. **Check these pages:**
   - Home page (logo should appear)
   - Courses page (level icons should appear)
   - Dashboard (logo and charts)
   - Level 1 lessons (lesson images)
   - Level 3 lessons (chart visualizations)

3. **Open browser developer tools** (F12) and check Console for any 404 errors

## 🐛 Troubleshooting

### Common Issues:

#### 1. Images Not Loading
- Ensure you're running from the correct directory
- Check that images exist in `/public/` folder
- Verify image paths in components match actual file names

#### 2. Dependency Conflicts
```bash
npm install --legacy-peer-deps --force
```

#### 3. MongoDB Connection Issues
- Check MongoDB is running
- Verify connection string in `.env`
- Check network connectivity for Atlas

#### 4. Port Already in Use
```bash
# Kill process on port 3000
npx kill-port 3000

# Kill process on port 5000
npx kill-port 5000
```

#### 5. Build Errors
```bash
# Clear Next.js cache
rm -rf .next
npm run build
```

## 📁 Project Features

- **Multi-level Learning System** (5 levels)
- **Interactive Lessons** with progress tracking
- **User Authentication** (login/register)
- **Payment Integration** with PayCaps
- **Responsive Design** with Tailwind CSS
- **Data Visualization** with Recharts
- **AI Integration** with Replicate API

## 🔐 Authentication

The app includes:
- User registration and login
- JWT-based authentication
- Protected routes
- Progress tracking per user

## 💳 Payment Integration

Optional PayCaps integration for premium features:
- Configure PayCaps credentials in `.env`
- Premium course access
- Payment success/failure handling

## 📱 Responsive Design

The application is fully responsive and works on:
- Desktop computers
- Tablets
- Mobile phones

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

If you encounter issues:
1. Check this README
2. Look for error messages in browser console
3. Check server logs
4. Verify environment configuration

---

**Happy Learning! 🚀**
