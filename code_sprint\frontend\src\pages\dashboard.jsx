'use client'

import { useEffect, useState } from 'react'
import { useRouter } from 'next/navigation'
import { BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, Legend, LineChart, Line, ResponsiveContainer } from 'recharts'
import {
  Rocket, LogOut, Code, Brain, Zap, BookOpen, Trophy, Target,
  Calendar, Clock, Star, Award, TrendingUp, Play, ChevronRight,
  User, Settings, Bell, Search, Filter
} from 'lucide-react'
import Image from 'next/image'
import ProgressTracker from '../components/ProgressTracker'

const achievementsData = [
  { name: 'AI', value: 5, color: '#8B5CF6' },
  { name: 'Coding', value: 10, color: '#06B6D4' },
  { name: 'Blockchain', value: 7, color: '#F59E0B' },
  { name: 'Quantum', value: 3, color: '#EF4444' },
  { name: 'IoT', value: 8, color: '#10B981' },
]

const weeklyProgressData = [
  { day: 'Mon', lessons: 2, time: 45 },
  { day: 'Tue', lessons: 3, time: 60 },
  { day: 'Wed', lessons: 1, time: 30 },
  { day: 'Thu', lessons: 4, time: 80 },
  { day: 'Fri', lessons: 2, time: 50 },
  { day: 'Sat', lessons: 5, time: 90 },
  { day: 'Sun', lessons: 3, time: 65 },
]

const recentAchievements = [
  { id: 1, title: 'First Steps', description: 'Completed your first lesson', icon: '🎯', date: '2 days ago' },
  { id: 2, title: 'Code Explorer', description: 'Finished 5 coding lessons', icon: '💻', date: '1 week ago' },
  { id: 3, title: 'AI Pioneer', description: 'Mastered AI fundamentals', icon: '🤖', date: '2 weeks ago' },
]

const upcomingLessons = [
  { id: 1, title: 'Machine Learning Basics', level: 'Level 2', duration: '45 min', difficulty: 'Intermediate' },
  { id: 2, title: 'Neural Networks', level: 'Level 2', duration: '60 min', difficulty: 'Advanced' },
  { id: 3, title: 'Computer Vision', level: 'Level 3', duration: '50 min', difficulty: 'Intermediate' },
]

export default function Dashboard() {
  const router = useRouter()
  const [isPremium, setIsPremium] = useState(false)
  const [premiumLevel, setPremiumLevel] = useState('')
  const [loading, setLoading] = useState(true)
  const [userName, setUserName] = useState('')
  const [userGrade, setUserGrade] = useState('')

  useEffect(() => {
    const token = localStorage.getItem('token')
    if (!token) {
      router.push('/login')
    } else {
      fetch('/api/verify-token', {
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      })
        .then((res) => res.json())
        .then((data) => {
          if (data.isPremium) {
            setIsPremium(true)
            setPremiumLevel(data.premiumLevel)
          }
          setUserName(data.name)
          setUserGrade(data.grade)
          setLoading(false)
        })
        .catch(() => {
          localStorage.removeItem('token')
          router.push('/login')
        })
    }
  }, [router])

  const handleLogout = () => {
    localStorage.removeItem('token')
    router.push('/login')
  }

  const handleSubscribe = () => {
    router.push('/go-premium')
  }

  const renderPremiumContent = () => {
    return (
      <div className="bg-[#003366] rounded-xl p-4 shadow-md text-white">
        <h2 className="text-xl font-bold mb-2 text-[#FFA500]">Premium Content: {premiumLevel}</h2>
        <ul className="list-disc pl-5 text-sm">
          {premiumLevel === 'Level 1' && (
            <>
              <li>Advanced AI techniques</li>
              <li>Blockchain fundamentals</li>
              <li>Quantum computing introduction</li>
              <li>Cybersecurity essentials</li>
            </>
          )}
          {premiumLevel === 'Level 2' && (
            <>
              <li>Machine Learning projects</li>
              <li>Smart contract development</li>
              <li>Quantum algorithms</li>
              <li>Ethical hacking workshops</li>
            </>
          )}
        </ul>
      </div>
    )
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center">
        <div className="text-center">
          <div className="w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-white text-lg font-medium">Loading your dashboard...</p>
          <p className="text-white/60 text-sm mt-2">Preparing your learning journey</p>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900">
      {/* Header */}
      <header className="bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-6 py-4">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-4">
              <Image src="/sprint-ai-logo-main.svg" alt="SPRINT - AI Logo" width={150} height={50} />
              <div className="hidden md:flex items-center space-x-6 ml-8">
                <button className="text-white/80 hover:text-white transition-colors flex items-center space-x-2">
                  <Search className="w-4 h-4" />
                  <span>Search</span>
                </button>
                <button className="text-white/80 hover:text-white transition-colors">
                  <Bell className="w-5 h-5" />
                </button>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              <div className="hidden md:flex items-center space-x-3 bg-white/10 rounded-full px-4 py-2">
                <div className="w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center">
                  <User className="w-4 h-4 text-white" />
                </div>
                <div className="text-sm">
                  <p className="text-white font-medium">{userName}</p>
                  <p className="text-white/60 text-xs">{isPremium ? premiumLevel : 'Free Plan'}</p>
                </div>
              </div>

              <button className="text-white/80 hover:text-white transition-colors">
                <Settings className="w-5 h-5" />
              </button>

              <button
                onClick={handleLogout}
                className="bg-red-500/20 hover:bg-red-500/30 text-red-300 hover:text-red-200 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 flex items-center space-x-2"
              >
                <LogOut className="w-4 h-4" />
                <span>Logout</span>
              </button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-6 py-8">
        {/* Welcome Section */}
        <div className="mb-8">
          <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <h1 className="text-3xl font-bold text-white mb-2">
                  Welcome back, {userName}! 🚀
                </h1>
                <p className="text-white/70 text-lg">
                  Ready to continue your coding adventure?
                </p>
              </div>
              <div className="hidden md:block">
                <div className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center">
                  <Rocket className="w-10 h-10 text-white" />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Overview */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm font-medium">Total Lessons</p>
                <p className="text-2xl font-bold text-white">24</p>
              </div>
              <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                <BookOpen className="w-6 h-6 text-blue-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm font-medium">Completed</p>
                <p className="text-2xl font-bold text-white">18</p>
              </div>
              <div className="w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center">
                <Trophy className="w-6 h-6 text-green-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm font-medium">Study Time</p>
                <p className="text-2xl font-bold text-white">12h</p>
              </div>
              <div className="w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center">
                <Clock className="w-6 h-6 text-purple-400" />
              </div>
            </div>
          </div>

          <div className="bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-white/70 text-sm font-medium">Achievements</p>
                <p className="text-2xl font-bold text-white">7</p>
              </div>
              <div className="w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center">
                <Award className="w-6 h-6 text-yellow-400" />
              </div>
            </div>
          </div>
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Progress & Learning */}
          <div className="lg:col-span-2 space-y-6">
            {/* Progress Tracker Component */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center">
                <TrendingUp className="w-5 h-5 mr-2 text-blue-400" />
                Learning Progress
              </h2>
              <ProgressTracker />
            </div>

            {/* Quick Actions */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center">
                <Zap className="w-5 h-5 mr-2 text-yellow-400" />
                Quick Actions
              </h2>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button
                  onClick={() => {
                    if (isPremium) {
                      router.push(`/lessons/${premiumLevel.toLowerCase().replace(' ', '-')}`)
                    } else {
                      alert('Upgrade to premium to access lessons');
                    }
                  }}
                  className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group"
                >
                  <Code className="w-5 h-5 group-hover:scale-110 transition-transform" />
                  <span className="font-medium">Continue Learning</span>
                </button>

                <button className="bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group">
                  <Brain className="w-5 h-5 group-hover:scale-110 transition-transform" />
                  <span className="font-medium">Take Quiz</span>
                </button>

                <button
                  onClick={handleSubscribe}
                  className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group"
                >
                  <Rocket className="w-5 h-5 group-hover:scale-110 transition-transform" />
                  <span className="font-medium">Go Premium</span>
                </button>
              </div>
            </div>

            {/* Weekly Progress Chart */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h2 className="text-xl font-bold text-white mb-6 flex items-center">
                <Calendar className="w-5 h-5 mr-2 text-green-400" />
                Weekly Activity
              </h2>
              <div className="h-64">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={weeklyProgressData}>
                    <CartesianGrid strokeDasharray="3 3" stroke="rgba(255,255,255,0.1)" />
                    <XAxis dataKey="day" stroke="rgba(255,255,255,0.7)" />
                    <YAxis stroke="rgba(255,255,255,0.7)" />
                    <Tooltip
                      contentStyle={{
                        backgroundColor: 'rgba(0,0,0,0.8)',
                        border: '1px solid rgba(255,255,255,0.2)',
                        borderRadius: '8px'
                      }}
                    />
                    <Line
                      type="monotone"
                      dataKey="lessons"
                      stroke="#3B82F6"
                      strokeWidth={3}
                      dot={{ fill: '#3B82F6', strokeWidth: 2, r: 4 }}
                    />
                    <Line
                      type="monotone"
                      dataKey="time"
                      stroke="#10B981"
                      strokeWidth={3}
                      dot={{ fill: '#10B981', strokeWidth: 2, r: 4 }}
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </div>
          </div>

          {/* Right Sidebar */}
          <div className="space-y-6">
            {/* Profile Card */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <div className="text-center">
                <div className="w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center">
                  <User className="w-10 h-10 text-white" />
                </div>
                <h3 className="text-xl font-bold text-white mb-1">{userName}</h3>
                <p className="text-white/60 text-sm mb-2">Grade {userGrade}</p>
                <div className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400/20 to-orange-500/20 text-yellow-300 border border-yellow-400/30">
                  <Star className="w-3 h-3 mr-1" />
                  {isPremium ? premiumLevel : 'Free Plan'}
                </div>
              </div>
            </div>

            {/* Recent Achievements */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <Trophy className="w-5 h-5 mr-2 text-yellow-400" />
                Recent Achievements
              </h3>
              <div className="space-y-3">
                {recentAchievements.map((achievement) => (
                  <div key={achievement.id} className="flex items-center space-x-3 p-3 bg-white/5 rounded-lg">
                    <div className="text-2xl">{achievement.icon}</div>
                    <div className="flex-1">
                      <p className="text-white font-medium text-sm">{achievement.title}</p>
                      <p className="text-white/60 text-xs">{achievement.description}</p>
                      <p className="text-white/40 text-xs mt-1">{achievement.date}</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Upcoming Lessons */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <Target className="w-5 h-5 mr-2 text-blue-400" />
                Upcoming Lessons
              </h3>
              <div className="space-y-3">
                {upcomingLessons.map((lesson) => (
                  <div key={lesson.id} className="p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer group">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="text-white font-medium text-sm group-hover:text-blue-300 transition-colors">
                        {lesson.title}
                      </h4>
                      <ChevronRight className="w-4 h-4 text-white/40 group-hover:text-white/60 transition-colors" />
                    </div>
                    <div className="flex items-center justify-between text-xs">
                      <span className="text-blue-300">{lesson.level}</span>
                      <span className="text-white/60">{lesson.duration}</span>
                    </div>
                    <div className="mt-2">
                      <span className={`inline-block px-2 py-1 rounded text-xs font-medium ${
                        lesson.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-300' :
                        lesson.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-300' :
                        'bg-red-500/20 text-red-300'
                      }`}>
                        {lesson.difficulty}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Skills Progress */}
            <div className="bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20">
              <h3 className="text-lg font-bold text-white mb-4 flex items-center">
                <Brain className="w-5 h-5 mr-2 text-purple-400" />
                Skills Progress
              </h3>
              <div className="space-y-4">
                {achievementsData.map((skill) => (
                  <div key={skill.name}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-white text-sm font-medium">{skill.name}</span>
                      <span className="text-white/60 text-sm">{skill.value}/10</span>
                    </div>
                    <div className="w-full bg-white/10 rounded-full h-2">
                      <div
                        className="h-2 rounded-full transition-all duration-500"
                        style={{
                          width: `${(skill.value / 10) * 100}%`,
                          backgroundColor: skill.color
                        }}
                      ></div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Action Button */}
      {!isPremium && (
        <div className="fixed bottom-6 right-6 z-50">
          <button
            onClick={handleSubscribe}
            className="bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full px-6 py-4 font-bold shadow-2xl transition-all duration-300 ease-in-out flex items-center space-x-2 hover:scale-105 group"
          >
            <Rocket className="w-5 h-5 group-hover:scale-110 transition-transform" />
            <span>Go Premium</span>
          </button>
        </div>
      )}
    </div>
  )
}