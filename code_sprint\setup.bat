@echo off
echo 🚀 CodeSprint Setup Script
echo ==========================

:: Check if Node.js is installed
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js first.
    pause
    exit /b 1
)

echo ✅ Node.js version:
node --version

:: Check if npm is installed
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm is not installed. Please install npm first.
    pause
    exit /b 1
)

echo ✅ npm version:
npm --version

:: Install root dependencies
echo 📦 Installing root dependencies...
npm install --legacy-peer-deps

:: Install backend dependencies
echo 📦 Installing backend dependencies...
cd backend
npm install
cd ..

:: Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd frontend
npm install --legacy-peer-deps
cd ..

:: Check if .env exists
if not exist .env (
    echo ⚠️  .env file not found. Please create it manually using the README guide.
)

echo.
echo ✅ Setup complete!
echo.
echo 🚀 To start the application:
echo 1. Start backend:  cd backend ^&^& npm run dev
echo 2. Start frontend: npm run dev (in new terminal)
echo.
echo 🌐 Application will be available at:
echo    Frontend: http://localhost:3000
echo    Backend:  http://localhost:5000
echo.
echo 📖 See README.md for detailed instructions
pause
