/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/dashboard";
exports.ids = ["pages/dashboard"];
exports.modules = {

/***/ "(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fdashboard.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fdashboard.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./src/pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.js\");\n/* harmony import */ var _src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/dashboard.jsx */ \"(pages-dir-node)/./src/pages/dashboard.jsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__]);\n_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/dashboard\",\n        pathname: \"/dashboard\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_dashboard_jsx__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LXJvdXRlLWxvYWRlci9pbmRleC5qcz9raW5kPVBBR0VTJnBhZ2U9JTJGZGFzaGJvYXJkJnByZWZlcnJlZFJlZ2lvbj0mYWJzb2x1dGVQYWdlUGF0aD0uJTJGc3JjJTJGcGFnZXMlMkZkYXNoYm9hcmQuanN4JmFic29sdXRlQXBwUGF0aD1wcml2YXRlLW5leHQtcGFnZXMlMkZfYXBwJmFic29sdXRlRG9jdW1lbnRQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9kb2N1bWVudCZtaWRkbGV3YXJlQ29uZmlnQmFzZTY0PWUzMCUzRCEiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUF3RjtBQUNoQztBQUNFO0FBQzFEO0FBQ3lEO0FBQ1Y7QUFDL0M7QUFDc0Q7QUFDdEQ7QUFDQSxpRUFBZSx3RUFBSyxDQUFDLHFEQUFRLFlBQVksRUFBQztBQUMxQztBQUNPLHVCQUF1Qix3RUFBSyxDQUFDLHFEQUFRO0FBQ3JDLHVCQUF1Qix3RUFBSyxDQUFDLHFEQUFRO0FBQ3JDLDJCQUEyQix3RUFBSyxDQUFDLHFEQUFRO0FBQ3pDLGVBQWUsd0VBQUssQ0FBQyxxREFBUTtBQUM3Qix3QkFBd0Isd0VBQUssQ0FBQyxxREFBUTtBQUM3QztBQUNPLGdDQUFnQyx3RUFBSyxDQUFDLHFEQUFRO0FBQzlDLGdDQUFnQyx3RUFBSyxDQUFDLHFEQUFRO0FBQzlDLGlDQUFpQyx3RUFBSyxDQUFDLHFEQUFRO0FBQy9DLGdDQUFnQyx3RUFBSyxDQUFDLHFEQUFRO0FBQzlDLG9DQUFvQyx3RUFBSyxDQUFDLHFEQUFRO0FBQ3pEO0FBQ08sd0JBQXdCLGtHQUFnQjtBQUMvQztBQUNBLGNBQWMsa0VBQVM7QUFDdkI7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0EsYUFBYSw4REFBVztBQUN4QixrQkFBa0IsbUVBQWdCO0FBQ2xDLEtBQUs7QUFDTCxZQUFZO0FBQ1osQ0FBQzs7QUFFRCxpQyIsInNvdXJjZXMiOlsiIl0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCB7IFBhZ2VzUm91dGVNb2R1bGUgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1tb2R1bGVzL3BhZ2VzL21vZHVsZS5jb21waWxlZFwiO1xuaW1wb3J0IHsgUm91dGVLaW5kIH0gZnJvbSBcIm5leHQvZGlzdC9zZXJ2ZXIvcm91dGUta2luZFwiO1xuaW1wb3J0IHsgaG9pc3QgfSBmcm9tIFwibmV4dC9kaXN0L2J1aWxkL3RlbXBsYXRlcy9oZWxwZXJzXCI7XG4vLyBJbXBvcnQgdGhlIGFwcCBhbmQgZG9jdW1lbnQgbW9kdWxlcy5cbmltcG9ydCAqIGFzIGRvY3VtZW50IGZyb20gXCJwcml2YXRlLW5leHQtcGFnZXMvX2RvY3VtZW50XCI7XG5pbXBvcnQgKiBhcyBhcHAgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fYXBwXCI7XG4vLyBJbXBvcnQgdGhlIHVzZXJsYW5kIGNvZGUuXG5pbXBvcnQgKiBhcyB1c2VybGFuZCBmcm9tIFwiLi9zcmMvcGFnZXMvZGFzaGJvYXJkLmpzeFwiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ2dldFNlcnZlclNpZGVQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsICdyZXBvcnRXZWJWaXRhbHMnKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFN0YXRpY1BhcmFtcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvZGFzaGJvYXJkXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9kYXNoYm9hcmRcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIC8vIGRlZmF1bHQgZXhwb3J0IG1pZ2h0IG5vdCBleGlzdCB3aGVuIG9wdGltaXplZCBmb3IgZGF0YSBvbmx5XG4gICAgICAgIEFwcDogYXBwLmRlZmF1bHQsXG4gICAgICAgIERvY3VtZW50OiBkb2N1bWVudC5kZWZhdWx0XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fdashboard.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/ProgressTracker.jsx":
/*!********************************************!*\
  !*** ./src/components/ProgressTracker.jsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/../node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../utils/progressTracker */ \"(pages-dir-node)/./src/utils/progressTracker.js\");\n\n\n\n\nconst ProgressTracker = ()=>{\n    const [progress, setProgress] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"ProgressTracker.useEffect\": ()=>{\n            const fetchProgress = {\n                \"ProgressTracker.useEffect.fetchProgress\": async ()=>{\n                    try {\n                        const userProgress = await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__.getUserProgress)();\n                        setProgress(userProgress);\n                    } catch (error) {\n                        console.error('Error fetching progress:', error);\n                    } finally{\n                        setLoading(false);\n                    }\n                }\n            }[\"ProgressTracker.useEffect.fetchProgress\"];\n            fetchProgress();\n        }\n    }[\"ProgressTracker.useEffect\"], []);\n    const handleContinueLearning = ()=>{\n        if (!progress) return;\n        const nextLesson = (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__.getNextLesson)(progress.currentLevel, progress.currentLesson);\n        if (nextLesson) {\n            // Navigate to the next lesson\n            router.push(`/lessons/${nextLesson.level}/${nextLesson.lesson}`);\n        } else {\n            // Navigate to current lesson if no next lesson\n            router.push(`/lessons/${progress.currentLevel}/${progress.currentLesson}`);\n        }\n    };\n    const handleResumeFromLastPosition = ()=>{\n        if (!progress) return;\n        // Navigate to the current lesson (where they left off)\n        router.push(`/lessons/${progress.currentLevel}/${progress.currentLesson}`);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4 text-[#FFA500]\",\n                    children: \"PROGRESS\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex justify-center items-center h-48\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-8 h-8 border-4 border-[#FFA500] border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 51,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n            lineNumber: 48,\n            columnNumber: 7\n        }, undefined);\n    }\n    if (!progress) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-2xl font-bold mb-4 text-[#FFA500]\",\n                    children: \"PROGRESS\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                    lineNumber: 60,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-white text-center\",\n                    children: \"Unable to load progress\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                    lineNumber: 61,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n            lineNumber: 59,\n            columnNumber: 7\n        }, undefined);\n    }\n    const progressPercentage = progress.progressPercentage || 0;\n    const nextLesson = (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__.getNextLesson)(progress.currentLevel, progress.currentLesson);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-4 border-[#FFA500]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4 text-[#FFA500]\",\n                        children: \"PROGRESS\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-48 h-48 mx-auto relative\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                className: \"w-full h-full transform -rotate-90\",\n                                viewBox: \"0 0 100 100\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"50\",\n                                        cy: \"50\",\n                                        r: \"40\",\n                                        stroke: \"#FFA500\",\n                                        strokeWidth: \"8\",\n                                        fill: \"none\",\n                                        opacity: \"0.3\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                        lineNumber: 79,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                                        cx: \"50\",\n                                        cy: \"50\",\n                                        r: \"40\",\n                                        stroke: \"#FFA500\",\n                                        strokeWidth: \"8\",\n                                        fill: \"none\",\n                                        strokeDasharray: `${2 * Math.PI * 40}`,\n                                        strokeDashoffset: `${2 * Math.PI * 40 * (1 - progressPercentage / 100)}`,\n                                        className: \"transition-all duration-500 ease-out\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 flex items-center justify-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-center\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-3xl font-bold text-[#FFA500]\",\n                                            children: [\n                                                progressPercentage,\n                                                \"%\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-sm text-white\",\n                                            children: \"Complete\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                            lineNumber: 106,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 103,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-sm\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"font-semibold text-[#FFA500]\",\n                                        children: progress.totalLessonsCompleted\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" lessons completed\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white text-xs mt-1\",\n                                children: [\n                                    \"Last accessed: \",\n                                    new Date(progress.lastAccessedAt).toLocaleDateString()\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#FFA500]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-3 text-[#FFA500]\",\n                        children: \"CURRENT LESSON\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#00264D] rounded-xl p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white font-medium\",\n                                children: (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__.getLessonDisplayName)(progress.currentLesson)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#B0C4DE] text-sm mt-1\",\n                                children: progress.currentLevel.toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 129,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 125,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleResumeFromLastPosition,\n                        className: \"w-full mt-4 bg-[#FFA500] text-[#003366] py-2 px-4 rounded-lg font-semibold hover:bg-[#FF8C00] transition-colors duration-200\",\n                        children: \"Resume Learning\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                lineNumber: 123,\n                columnNumber: 7\n            }, undefined),\n            nextLesson && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#00FF00]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-3 text-[#00FF00]\",\n                        children: \"NEXT LESSON\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 145,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-[#00264D] rounded-xl p-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-white font-medium\",\n                                children: (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_3__.getLessonDisplayName)(nextLesson.lesson)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 147,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-[#B0C4DE] text-sm mt-1\",\n                                children: nextLesson.level.toUpperCase()\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 146,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleContinueLearning,\n                        className: \"w-full mt-4 bg-[#00FF00] text-[#003366] py-2 px-4 rounded-lg font-semibold hover:bg-[#00CC00] transition-colors duration-200\",\n                        children: \"Continue Learning\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, undefined),\n            !nextLesson && progressPercentage === 100 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-[#003366] rounded-2xl p-6 shadow-md border-2 border-[#FFD700]\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-xl font-bold mb-3 text-[#FFD700]\",\n                        children: \"\\uD83C\\uDF89 CONGRATULATIONS!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 167,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-center\",\n                        children: \"You've completed all available lessons!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                        lineNumber: 168,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/ProgressTracker.jsx\",\n        lineNumber: 70,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ProgressTracker);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/ProgressTracker.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjs7QUFDYyxDQUFDLGdCQUFnQjtBQUNXLENBQUMsaUJBQWlCO0FBQ3pDO0FBRXBCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsd0JBQXdCO0FBQ3hCRixJQUFJRyxTQUFTLEdBQUc7SUFDZEYsV0FBV0YsK0RBQXFCLENBQUNNLFVBQVU7SUFDM0NILFdBQVdILDBEQUFnQixDQUFDTSxVQUFVO0FBQ3hDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLmpzXG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgXCIuL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3NcIjsgLy8gQW5pbWF0aW9ucyBDU1NcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSBcInByb3AtdHlwZXNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuLy8gQWRkIFByb3BUeXBlcyBmb3IgQXBwXG5BcHAucHJvcFR5cGVzID0ge1xuICBDb21wb25lbnQ6IFByb3BUeXBlcy5lbGVtZW50VHlwZS5pc1JlcXVpcmVkLCAvLyBDb21wb25lbnQgbXVzdCBiZSBhIHZhbGlkIFJlYWN0IGNvbXBvbmVudFxuICBwYWdlUHJvcHM6IFByb3BUeXBlcy5vYmplY3QuaXNSZXF1aXJlZCwgLy8gcGFnZVByb3BzIGlzIGFuIG9iamVjdFxufTtcbiJdLCJuYW1lcyI6WyJQcm9wVHlwZXMiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwcm9wVHlwZXMiLCJlbGVtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJvYmplY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZCAvPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/dashboard.jsx":
/*!*********************************!*\
  !*** ./src/pages/dashboard.jsx ***!
  \*********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Dashboard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(pages-dir-node)/../node_modules/next/navigation.js\");\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_navigation__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!recharts */ \"(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../node_modules/recharts/es6/index.js\");\n/* harmony import */ var _barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=Award,Bell,BookOpen,Brain,Calendar,ChevronRight,Clock,Code,Filter,LogOut,Play,Rocket,Search,Settings,Star,Target,TrendingUp,Trophy,User,Zap!=!lucide-react */ \"(pages-dir-node)/__barrel_optimize__?names=Award,Bell,BookOpen,Brain,Calendar,ChevronRight,Clock,Code,Filter,LogOut,Play,Rocket,Search,Settings,Star,Target,TrendingUp,Trophy,User,Zap!=!../node_modules/lucide-react/dist/esm/lucide-react.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _components_ProgressTracker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../components/ProgressTracker */ \"(pages-dir-node)/./src/components/ProgressTracker.jsx\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__]);\n_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\n\n\nconst achievementsData = [\n    {\n        name: 'AI',\n        value: 5,\n        color: '#8B5CF6'\n    },\n    {\n        name: 'Coding',\n        value: 10,\n        color: '#06B6D4'\n    },\n    {\n        name: 'Blockchain',\n        value: 7,\n        color: '#F59E0B'\n    },\n    {\n        name: 'Quantum',\n        value: 3,\n        color: '#EF4444'\n    },\n    {\n        name: 'IoT',\n        value: 8,\n        color: '#10B981'\n    }\n];\nconst weeklyProgressData = [\n    {\n        day: 'Mon',\n        lessons: 2,\n        time: 45\n    },\n    {\n        day: 'Tue',\n        lessons: 3,\n        time: 60\n    },\n    {\n        day: 'Wed',\n        lessons: 1,\n        time: 30\n    },\n    {\n        day: 'Thu',\n        lessons: 4,\n        time: 80\n    },\n    {\n        day: 'Fri',\n        lessons: 2,\n        time: 50\n    },\n    {\n        day: 'Sat',\n        lessons: 5,\n        time: 90\n    },\n    {\n        day: 'Sun',\n        lessons: 3,\n        time: 65\n    }\n];\nconst recentAchievements = [\n    {\n        id: 1,\n        title: 'First Steps',\n        description: 'Completed your first lesson',\n        icon: '🎯',\n        date: '2 days ago'\n    },\n    {\n        id: 2,\n        title: 'Code Explorer',\n        description: 'Finished 5 coding lessons',\n        icon: '💻',\n        date: '1 week ago'\n    },\n    {\n        id: 3,\n        title: 'AI Pioneer',\n        description: 'Mastered AI fundamentals',\n        icon: '🤖',\n        date: '2 weeks ago'\n    }\n];\nconst upcomingLessons = [\n    {\n        id: 1,\n        title: 'Machine Learning Basics',\n        level: 'Level 2',\n        duration: '45 min',\n        difficulty: 'Intermediate'\n    },\n    {\n        id: 2,\n        title: 'Neural Networks',\n        level: 'Level 2',\n        duration: '60 min',\n        difficulty: 'Advanced'\n    },\n    {\n        id: 3,\n        title: 'Computer Vision',\n        level: 'Level 3',\n        duration: '50 min',\n        difficulty: 'Intermediate'\n    }\n];\nfunction Dashboard() {\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const [isPremium, setIsPremium] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [premiumLevel, setPremiumLevel] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [userName, setUserName] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [userGrade, setUserGrade] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Dashboard.useEffect\": ()=>{\n            const token = localStorage.getItem('token');\n            if (!token) {\n                router.push('/login');\n            } else {\n                fetch('/api/verify-token', {\n                    headers: {\n                        'Authorization': `Bearer ${token}`\n                    }\n                }).then({\n                    \"Dashboard.useEffect\": (res)=>res.json()\n                }[\"Dashboard.useEffect\"]).then({\n                    \"Dashboard.useEffect\": (data)=>{\n                        if (data.isPremium) {\n                            setIsPremium(true);\n                            setPremiumLevel(data.premiumLevel);\n                        }\n                        setUserName(data.name);\n                        setUserGrade(data.grade);\n                        setLoading(false);\n                    }\n                }[\"Dashboard.useEffect\"]).catch({\n                    \"Dashboard.useEffect\": ()=>{\n                        localStorage.removeItem('token');\n                        router.push('/login');\n                    }\n                }[\"Dashboard.useEffect\"]);\n            }\n        }\n    }[\"Dashboard.useEffect\"], [\n        router\n    ]);\n    const handleLogout = ()=>{\n        localStorage.removeItem('token');\n        router.push('/login');\n    };\n    const handleSubscribe = ()=>{\n        router.push('/go-premium');\n    };\n    const renderPremiumContent = ()=>{\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-[#003366] rounded-xl p-4 shadow-md text-white\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                    className: \"text-xl font-bold mb-2 text-[#FFA500]\",\n                    children: [\n                        \"Premium Content: \",\n                        premiumLevel\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                    lineNumber: 91,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc pl-5 text-sm\",\n                    children: [\n                        premiumLevel === 'Level 1' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Advanced AI techniques\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Blockchain fundamentals\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 96,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Quantum computing introduction\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Cybersecurity essentials\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        premiumLevel === 'Level 2' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Machine Learning projects\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 103,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Smart contract development\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Quantum algorithms\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 105,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                    children: \"Ethical hacking workshops\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 106,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                    lineNumber: 92,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n            lineNumber: 90,\n            columnNumber: 7\n        }, this);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 flex items-center justify-center\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-16 h-16 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 118,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white text-lg font-medium\",\n                        children: \"Loading your dashboard...\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-white/60 text-sm mt-2\",\n                        children: \"Preparing your learning journey\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 120,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                lineNumber: 117,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n            lineNumber: 116,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white/10 backdrop-blur-md border-b border-white/20 sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-6 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_3___default()), {\n                                        src: \"/sprint-ai-logo-main.svg\",\n                                        alt: \"SPRINT - AI Logo\",\n                                        width: 150,\n                                        height: 50\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 133,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-6 ml-8\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-white/80 hover:text-white transition-colors flex items-center space-x-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Search, {\n                                                        className: \"w-4 h-4\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 136,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        children: \"Search\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 135,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                className: \"text-white/80 hover:text-white transition-colors\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Bell, {\n                                                    className: \"w-5 h-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 140,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 139,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 132,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:flex items-center space-x-3 bg-white/10 rounded-full px-4 py-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"w-8 h-8 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full flex items-center justify-center\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                                    className: \"w-4 h-4 text-white\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 147,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"text-sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white font-medium\",\n                                                        children: userName\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 151,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-white/60 text-xs\",\n                                                        children: isPremium ? premiumLevel : 'Free Plan'\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 152,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 150,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 146,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        className: \"text-white/80 hover:text-white transition-colors\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Settings, {\n                                            className: \"w-5 h-5\"\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 157,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 156,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"bg-red-500/20 hover:bg-red-500/30 text-red-300 hover:text-red-200 rounded-lg px-4 py-2 text-sm font-medium transition-all duration-200 flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.LogOut, {\n                                                className: \"w-4 h-4\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 164,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: \"Logout\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 165,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 145,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 131,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                    lineNumber: 130,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                lineNumber: 129,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-6 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-8\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                className: \"text-3xl font-bold text-white mb-2\",\n                                                children: [\n                                                    \"Welcome back, \",\n                                                    userName,\n                                                    \"! \\uD83D\\uDE80\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 179,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-white/70 text-lg\",\n                                                children: \"Ready to continue your coding adventure?\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 178,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"hidden md:block\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Rocket, {\n                                                className: \"w-10 h-10 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 177,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                            lineNumber: 176,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 175,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/70 text-sm font-medium\",\n                                                    children: \"Total Lessons\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"24\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 199,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.BookOpen, {\n                                                className: \"w-6 h-6 text-blue-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 204,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 203,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 198,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 197,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/70 text-sm font-medium\",\n                                                    children: \"Completed\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 212,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"18\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 213,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 211,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-green-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trophy, {\n                                                className: \"w-6 h-6 text-green-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 215,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 210,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 209,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/70 text-sm font-medium\",\n                                                    children: \"Study Time\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 224,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"12h\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 225,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 223,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-purple-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Clock, {\n                                                className: \"w-6 h-6 text-purple-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 227,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 222,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 221,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white/10 backdrop-blur-md rounded-xl p-6 border border-white/20\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/70 text-sm font-medium\",\n                                                    children: \"Achievements\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-2xl font-bold text-white\",\n                                                    children: \"7\"\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 237,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 235,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-12 h-12 bg-yellow-500/20 rounded-lg flex items-center justify-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Award, {\n                                                className: \"w-6 h-6 text-yellow-400\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                    lineNumber: 234,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 233,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 196,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-3 gap-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:col-span-2 space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.TrendingUp, {\n                                                        className: \"w-5 h-5 mr-2 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 253,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Learning Progress\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 252,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProgressTracker__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 256,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 251,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Zap, {\n                                                        className: \"w-5 h-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 262,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Quick Actions\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 261,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: ()=>{\n                                                            if (isPremium) {\n                                                                router.push(`/lessons/${premiumLevel.toLowerCase().replace(' ', '-')}`);\n                                                            } else {\n                                                                alert('Upgrade to premium to access lessons');\n                                                            }\n                                                        },\n                                                        className: \"bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Code, {\n                                                                className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Continue Learning\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 277,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 266,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        className: \"bg-gradient-to-r from-purple-500 to-purple-600 hover:from-purple-600 hover:to-purple-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Brain, {\n                                                                className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 281,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Take Quiz\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 280,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                        onClick: handleSubscribe,\n                                                        className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-xl p-4 transition-all duration-200 flex items-center justify-center space-x-2 group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Rocket, {\n                                                                className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                className: \"font-medium\",\n                                                                children: \"Go Premium\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 285,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 265,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 260,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-xl font-bold text-white mb-6 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Calendar, {\n                                                        className: \"w-5 h-5 mr-2 text-green-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 298,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Weekly Activity\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 297,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"h-64\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer, {\n                                                    width: \"100%\",\n                                                    height: \"100%\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.LineChart, {\n                                                        data: weeklyProgressData,\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.CartesianGrid, {\n                                                                strokeDasharray: \"3 3\",\n                                                                stroke: \"rgba(255,255,255,0.1)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 304,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.XAxis, {\n                                                                dataKey: \"day\",\n                                                                stroke: \"rgba(255,255,255,0.7)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.YAxis, {\n                                                                stroke: \"rgba(255,255,255,0.7)\"\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Tooltip, {\n                                                                contentStyle: {\n                                                                    backgroundColor: 'rgba(0,0,0,0.8)',\n                                                                    border: '1px solid rgba(255,255,255,0.2)',\n                                                                    borderRadius: '8px'\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"lessons\",\n                                                                stroke: \"#3B82F6\",\n                                                                strokeWidth: 3,\n                                                                dot: {\n                                                                    fill: '#3B82F6',\n                                                                    strokeWidth: 2,\n                                                                    r: 4\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bar_BarChart_CartesianGrid_Legend_Line_LineChart_ResponsiveContainer_Tooltip_XAxis_YAxis_recharts__WEBPACK_IMPORTED_MODULE_6__.Line, {\n                                                                type: \"monotone\",\n                                                                dataKey: \"time\",\n                                                                stroke: \"#10B981\",\n                                                                strokeWidth: 3,\n                                                                dot: {\n                                                                    fill: '#10B981',\n                                                                    strokeWidth: 2,\n                                                                    r: 4\n                                                                }\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 321,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 303,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 302,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 301,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 249,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-20 h-20 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full mx-auto mb-4 flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.User, {\n                                                        className: \"w-10 h-10 text-white\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 340,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 339,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-bold text-white mb-1\",\n                                                    children: userName\n                                                }, void 0, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 342,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-white/60 text-sm mb-2\",\n                                                    children: [\n                                                        \"Grade \",\n                                                        userGrade\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 343,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-gradient-to-r from-yellow-400/20 to-orange-500/20 text-yellow-300 border border-yellow-400/30\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Star, {\n                                                            className: \"w-3 h-3 mr-1\"\n                                                        }, void 0, false, {\n                                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                            lineNumber: 345,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        isPremium ? premiumLevel : 'Free Plan'\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                    lineNumber: 344,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                            lineNumber: 338,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 337,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Trophy, {\n                                                        className: \"w-5 h-5 mr-2 text-yellow-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 354,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Recent Achievements\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 353,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: recentAchievements.map((achievement)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center space-x-3 p-3 bg-white/5 rounded-lg\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"text-2xl\",\n                                                                children: achievement.icon\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 360,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex-1\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white font-medium text-sm\",\n                                                                        children: achievement.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 362,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/60 text-xs\",\n                                                                        children: achievement.description\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 363,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                        className: \"text-white/40 text-xs mt-1\",\n                                                                        children: achievement.date\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 364,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 361,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, achievement.id, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 359,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 357,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Target, {\n                                                        className: \"w-5 h-5 mr-2 text-blue-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 374,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Upcoming Lessons\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 373,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-3\",\n                                                children: upcomingLessons.map((lesson)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"p-3 bg-white/5 rounded-lg hover:bg-white/10 transition-colors cursor-pointer group\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                                                        className: \"text-white font-medium text-sm group-hover:text-blue-300 transition-colors\",\n                                                                        children: lesson.title\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 381,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.ChevronRight, {\n                                                                        className: \"w-4 h-4 text-white/40 group-hover:text-white/60 transition-colors\"\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 384,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 380,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex items-center justify-between text-xs\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-blue-300\",\n                                                                        children: lesson.level\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 387,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/60\",\n                                                                        children: lesson.duration\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 388,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 386,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"mt-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                    className: `inline-block px-2 py-1 rounded text-xs font-medium ${lesson.difficulty === 'Beginner' ? 'bg-green-500/20 text-green-300' : lesson.difficulty === 'Intermediate' ? 'bg-yellow-500/20 text-yellow-300' : 'bg-red-500/20 text-red-300'}`,\n                                                                    children: lesson.difficulty\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                    lineNumber: 391,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 390,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, lesson.id, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 379,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 377,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"bg-white/10 backdrop-blur-md rounded-2xl p-6 border border-white/20\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-lg font-bold text-white mb-4 flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Brain, {\n                                                        className: \"w-5 h-5 mr-2 text-purple-400\"\n                                                    }, void 0, false, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    \"Skills Progress\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: achievementsData.map((skill)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"flex justify-between items-center mb-2\",\n                                                                children: [\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white text-sm font-medium\",\n                                                                        children: skill.name\n                                                                    }, void 0, false, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 414,\n                                                                        columnNumber: 23\n                                                                    }, this),\n                                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                        className: \"text-white/60 text-sm\",\n                                                                        children: [\n                                                                            skill.value,\n                                                                            \"/10\"\n                                                                        ]\n                                                                    }, void 0, true, {\n                                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                        lineNumber: 415,\n                                                                        columnNumber: 23\n                                                                    }, this)\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 413,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                className: \"w-full bg-white/10 rounded-full h-2\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                                    className: \"h-2 rounded-full transition-all duration-500\",\n                                                                    style: {\n                                                                        width: `${skill.value / 10 * 100}%`,\n                                                                        backgroundColor: skill.color\n                                                                    }\n                                                                }, void 0, false, {\n                                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                    lineNumber: 418,\n                                                                    columnNumber: 23\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                                lineNumber: 417,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, skill.name, true, {\n                                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                                lineNumber: 410,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                                lineNumber: 335,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                        lineNumber: 247,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                lineNumber: 173,\n                columnNumber: 7\n            }, this),\n            !isPremium && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed bottom-6 right-6 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleSubscribe,\n                    className: \"bg-gradient-to-r from-orange-500 to-orange-600 hover:from-orange-600 hover:to-orange-700 text-white rounded-full px-6 py-4 font-bold shadow-2xl transition-all duration-300 ease-in-out flex items-center space-x-2 hover:scale-105 group\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Award_Bell_BookOpen_Brain_Calendar_ChevronRight_Clock_Code_Filter_LogOut_Play_Rocket_Search_Settings_Star_Target_TrendingUp_Trophy_User_Zap_lucide_react__WEBPACK_IMPORTED_MODULE_5__.Rocket, {\n                            className: \"w-5 h-5 group-hover:scale-110 transition-transform\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                            lineNumber: 441,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: \"Go Premium\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                            lineNumber: 442,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                    lineNumber: 437,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n                lineNumber: 436,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/dashboard.jsx\",\n        lineNumber: 127,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/dashboard.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css":
/*!******************************************************************!*\
  !*** ./src/pages/lessons/level-4/Section1/animations.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__VOjD6\",\n\t\"fadeInUp\": \"animations_fadeInUp__wib6A\",\n\t\"hidden\": \"animations_hidden__KLYnx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29uZmV0dGlcIjogXCJhbmltYXRpb25zX2NvbmZldHRpX19WT2pENlwiLFxuXHRcImZhZGVJblVwXCI6IFwiYW5pbWF0aW9uc19mYWRlSW5VcF9fd2liNkFcIixcblx0XCJoaWRkZW5cIjogXCJhbmltYXRpb25zX2hpZGRlbl9fS0xZbnhcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/progressTracker.js":
/*!**************************************!*\
  !*** ./src/utils/progressTracker.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallProgress: () => (/* binding */ calculateOverallProgress),\n/* harmony export */   getLessonDisplayName: () => (/* binding */ getLessonDisplayName),\n/* harmony export */   getNextLesson: () => (/* binding */ getNextLesson),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   isLessonCompleted: () => (/* binding */ isLessonCompleted),\n/* harmony export */   markLessonComplete: () => (/* binding */ markLessonComplete),\n/* harmony export */   trackLessonAccess: () => (/* binding */ trackLessonAccess),\n/* harmony export */   updateProgress: () => (/* binding */ updateProgress)\n/* harmony export */ });\n// Progress tracking utility for LMS\nconst API_BASE_URL =  false ? 0 : 'http://localhost:5000/api';\n// Get user's progress from backend\nconst getUserProgress = async ()=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage\n        if (!token) {\n            console.log('No token found, using local storage for progress');\n            return getProgressLocally();\n        }\n        const response = await fetch(`${API_BASE_URL}/progress`, {\n            method: 'GET',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return getProgressLocally();\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error fetching progress, using local storage fallback:', error.message);\n        return getProgressLocally();\n    }\n};\n// Get progress from local storage\nconst getProgressLocally = ()=>{\n    try {\n        const progress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        return {\n            currentLevel: progress.currentLevel || 'level1',\n            currentLesson: progress.currentLesson || '1.1WhatIsComputer',\n            completedLessons: progress.completedLessons || [],\n            accessedLessons: progress.accessedLessons || [],\n            lastAccessedAt: progress.lastAccessedAt || new Date().toISOString(),\n            totalLessonsCompleted: progress.totalLessonsCompleted || 0,\n            progressPercentage: progress.progressPercentage || 0\n        };\n    } catch (error) {\n        console.error('Error reading local progress:', error);\n        return {\n            currentLevel: 'level1',\n            currentLesson: '1.1WhatIsComputer',\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Local storage fallback for progress tracking\nconst updateProgressLocally = (lessonId, level, action)=>{\n    try {\n        // Get existing progress from localStorage\n        const existingProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        // Initialize if empty\n        if (!existingProgress.completedLessons) {\n            existingProgress.completedLessons = [];\n        }\n        if (!existingProgress.accessedLessons) {\n            existingProgress.accessedLessons = [];\n        }\n        // Update progress based on action\n        if (action === 'access' && !existingProgress.accessedLessons.includes(lessonId)) {\n            existingProgress.accessedLessons.push(lessonId);\n        }\n        if (action === 'complete' && !existingProgress.completedLessons.includes(lessonId)) {\n            existingProgress.completedLessons.push(lessonId);\n        }\n        // Update other fields\n        existingProgress.currentLevel = level;\n        existingProgress.currentLesson = lessonId;\n        existingProgress.lastAccessedAt = new Date().toISOString();\n        existingProgress.totalLessonsCompleted = existingProgress.completedLessons.length;\n        existingProgress.progressPercentage = calculateOverallProgress(existingProgress.completedLessons);\n        // Save back to localStorage\n        localStorage.setItem('userProgress', JSON.stringify(existingProgress));\n        console.log(`Progress updated locally: ${action} for ${lessonId} in ${level}`);\n        return existingProgress;\n    } catch (error) {\n        console.error('Error updating progress locally:', error);\n        return {\n            currentLevel: level,\n            currentLesson: lessonId,\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Update user's progress\nconst updateProgress = async (lessonId, level, action = 'access')=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage fallback\n        if (!token) {\n            console.log('No token found, using local storage for progress tracking');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const response = await fetch(`${API_BASE_URL}/progress/update`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                lessonId,\n                level,\n                action\n            })\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error updating progress, using local storage fallback:', error.message);\n        return updateProgressLocally(lessonId, level, action);\n    }\n};\n// Mark lesson as completed\nconst markLessonComplete = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'complete');\n};\n// Track lesson access\nconst trackLessonAccess = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'access');\n};\n// Check if lesson is completed\nconst isLessonCompleted = (lessonId, completedLessons)=>{\n    return completedLessons.includes(lessonId);\n};\n// Get next lesson based on current progress\nconst getNextLesson = (currentLevel, currentLesson)=>{\n    // Define lesson sequences for each level\n    const lessonSequences = {\n        level1: [\n            '1.1WhatIsComputer',\n            '1.2HowComputersWork',\n            '1.3WhatIsAProgram',\n            '1.4TypesOfComputers',\n            '1.5HardwareAndSoftware',\n            '1.6OperatingSystem',\n            '2.1WhatIsScratch',\n            '3.1WhatIsIoT',\n            '3.2HowIoTWorks',\n            '3.3IoTExamples',\n            '3.4IoTBenefits',\n            '4.1WhatIsComputerVision',\n            '4.2HowComputerVisionWorks',\n            '4.3ComputerVisionApplications',\n            '5.1Summary',\n            '5.3CompletionMessage'\n        ],\n        level2: [\n            '0.0CourseContent',\n            '0.1DisplayLevel2',\n            '01.0MetaverseAndAugmentedReality',\n            '01.1CoolTechnologyBehindMetaverse',\n            '02.01WhatIsAugmentedReality',\n            '02.0AugmentedReality',\n            '02.1MarkerBasedAR',\n            '02.2MarkerlessAR',\n            '02.3UsesOfAugmentedReality',\n            '03.0VirtualReality',\n            '03.2ApplicationsOfVirtualReality',\n            '04.1UnderstandingDNA',\n            '04.2CoolAIToolsForGenomics',\n            '04.3ApplicationsInMedicine',\n            '04.4FunFactsGenomicAI',\n            '2.0CyberSecurity',\n            '2.1WhatIsCybersecurity',\n            '2.2ThreatsAndAttacks',\n            '2.3ProtectingYourselfOnline',\n            '2.4FutureOfCybersecurity',\n            '3.0QuantumComputing',\n            '3.1WhatIsQuantumComputing',\n            '3.2QuantumBitsAndEntanglement',\n            '4.0GenomicAI'\n        ],\n        level3: [\n            '0.0CourseContent',\n            '1.0IntroductionToDataScience',\n            '1.1GlitchSaysHi',\n            '1.2WhatIsDataScience',\n            '1.3UnderstandingDataScience',\n            '2.1HowDataIsCollected',\n            '2.2UnderstandingMeanMedianMode',\n            '2.3ExploringDataTypes',\n            '3.0DataVisualization',\n            '3.1WhyVisualizeData',\n            '3.2CreateYourOwnBarGraph',\n            '3.3IntroductionToCharts',\n            '3.4DataStorytelling',\n            '3.5AdvancedVisualization',\n            '4.0Quiz',\n            '4.1Conclusion'\n        ],\n        level4: [\n            // Add level 4 lessons here\n            '1.1HelloWorld',\n            '1.2Pyramid',\n            '1.3Ascii',\n            '1.4Letter',\n            '2.1DataTypes',\n            '2.2DataTypes',\n            '2.3whew',\n            '3.1ControlFlow',\n            '3.2AreYouACatPerson',\n            '3.3TheGoodTheBadAndTheElif',\n            '3.4BookOfAnswers',\n            '3.5GodOfThunder',\n            '4.1DontMakeMeGuess',\n            '4.2OhYouAreActuallyMakingMeGuess',\n            '4.3PlierEtendreReleverElancer',\n            '4.4MyTrueLoveSentToMe',\n            '4.5GoodOlFizzBuzz'\n        ],\n        level5: [\n            // Add level 5 lessons here\n            'Ai',\n            'MachineLearning',\n            'NeuralNetworks',\n            'DeepLearning',\n            'Future',\n            'Final',\n            'Whatisnlp',\n            'Human',\n            'Breakingitdownwithtokenization',\n            'Cleaningupthemess',\n            'Letsgetsentimental',\n            'Buildingachatbot'\n        ]\n    };\n    const currentSequence = lessonSequences[currentLevel];\n    if (!currentSequence) return null;\n    const currentIndex = currentSequence.indexOf(currentLesson);\n    if (currentIndex === -1 || currentIndex === currentSequence.length - 1) {\n        // Move to next level if current level is complete\n        const levelNumber = parseInt(currentLevel.replace('level', ''));\n        const nextLevel = `level${levelNumber + 1}`;\n        if (lessonSequences[nextLevel]) {\n            return {\n                level: nextLevel,\n                lesson: lessonSequences[nextLevel][0]\n            };\n        }\n        return null; // All levels completed\n    }\n    return {\n        level: currentLevel,\n        lesson: currentSequence[currentIndex + 1]\n    };\n};\n// Calculate overall progress percentage\nconst calculateOverallProgress = (completedLessons)=>{\n    const totalLessons = 100; // Adjust based on actual total\n    return Math.round(completedLessons.length / totalLessons * 100);\n};\n// Get lesson display name\nconst getLessonDisplayName = (lessonId)=>{\n    const lessonNames = {\n        '1.1WhatIsComputer': 'What is a Computer?',\n        '1.2HowComputersWork': 'How Computers Work',\n        '1.3WhatIsAProgram': 'What is a Program?',\n        '1.4TypesOfComputers': 'Types of Computers',\n        '1.5HardwareAndSoftware': 'Hardware and Software',\n        '1.6OperatingSystem': 'Operating System',\n        '2.1WhatIsScratch': 'What is Scratch?',\n        '3.1WhatIsIoT': 'What is IoT?',\n        '3.2HowIoTWorks': 'How IoT Works',\n        '3.3IoTExamples': 'IoT Examples',\n        '3.4IoTBenefits': 'IoT Benefits',\n        '4.1WhatIsComputerVision': 'What is Computer Vision?',\n        '4.2HowComputerVisionWorks': 'How Computer Vision Works',\n        '4.3ComputerVisionApplications': 'Computer Vision Applications',\n        '5.1Summary': 'Summary',\n        '5.3CompletionMessage': 'Completion Message'\n    };\n    return lessonNames[lessonId] || lessonId;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/progressTracker.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Award,Bell,BookOpen,Brain,Calendar,ChevronRight,Clock,Code,Filter,LogOut,Play,Rocket,Search,Settings,Star,Target,TrendingUp,Trophy,User,Zap!=!../node_modules/lucide-react/dist/esm/lucide-react.js":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Award,Bell,BookOpen,Brain,Calendar,ChevronRight,Clock,Code,Filter,LogOut,Play,Rocket,Search,Settings,Star,Target,TrendingUp,Trophy,User,Zap!=!../node_modules/lucide-react/dist/esm/lucide-react.js ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Award: () => (/* reexport safe */ _icons_award_js__WEBPACK_IMPORTED_MODULE_0__[\"default\"]),\n/* harmony export */   Bell: () => (/* reexport safe */ _icons_bell_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"]),\n/* harmony export */   BookOpen: () => (/* reexport safe */ _icons_book_open_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"]),\n/* harmony export */   Brain: () => (/* reexport safe */ _icons_brain_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"]),\n/* harmony export */   Calendar: () => (/* reexport safe */ _icons_calendar_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"]),\n/* harmony export */   ChevronRight: () => (/* reexport safe */ _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_5__[\"default\"]),\n/* harmony export */   Clock: () => (/* reexport safe */ _icons_clock_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"]),\n/* harmony export */   Code: () => (/* reexport safe */ _icons_code_js__WEBPACK_IMPORTED_MODULE_7__[\"default\"]),\n/* harmony export */   Filter: () => (/* reexport safe */ _icons_filter_js__WEBPACK_IMPORTED_MODULE_8__[\"default\"]),\n/* harmony export */   LogOut: () => (/* reexport safe */ _icons_log_out_js__WEBPACK_IMPORTED_MODULE_9__[\"default\"]),\n/* harmony export */   Play: () => (/* reexport safe */ _icons_play_js__WEBPACK_IMPORTED_MODULE_10__[\"default\"]),\n/* harmony export */   Rocket: () => (/* reexport safe */ _icons_rocket_js__WEBPACK_IMPORTED_MODULE_11__[\"default\"]),\n/* harmony export */   Search: () => (/* reexport safe */ _icons_search_js__WEBPACK_IMPORTED_MODULE_12__[\"default\"]),\n/* harmony export */   Settings: () => (/* reexport safe */ _icons_settings_js__WEBPACK_IMPORTED_MODULE_13__[\"default\"]),\n/* harmony export */   Star: () => (/* reexport safe */ _icons_star_js__WEBPACK_IMPORTED_MODULE_14__[\"default\"]),\n/* harmony export */   Target: () => (/* reexport safe */ _icons_target_js__WEBPACK_IMPORTED_MODULE_15__[\"default\"]),\n/* harmony export */   TrendingUp: () => (/* reexport safe */ _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_16__[\"default\"]),\n/* harmony export */   Trophy: () => (/* reexport safe */ _icons_trophy_js__WEBPACK_IMPORTED_MODULE_17__[\"default\"]),\n/* harmony export */   User: () => (/* reexport safe */ _icons_user_js__WEBPACK_IMPORTED_MODULE_18__[\"default\"]),\n/* harmony export */   Zap: () => (/* reexport safe */ _icons_zap_js__WEBPACK_IMPORTED_MODULE_19__[\"default\"])\n/* harmony export */ });\n/* harmony import */ var _icons_award_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./icons/award.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/award.js\");\n/* harmony import */ var _icons_bell_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./icons/bell.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/bell.js\");\n/* harmony import */ var _icons_book_open_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./icons/book-open.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/book-open.js\");\n/* harmony import */ var _icons_brain_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./icons/brain.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/brain.js\");\n/* harmony import */ var _icons_calendar_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./icons/calendar.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _icons_chevron_right_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./icons/chevron-right.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _icons_clock_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./icons/clock.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/clock.js\");\n/* harmony import */ var _icons_code_js__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./icons/code.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/code.js\");\n/* harmony import */ var _icons_filter_js__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./icons/filter.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/filter.js\");\n/* harmony import */ var _icons_log_out_js__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./icons/log-out.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/log-out.js\");\n/* harmony import */ var _icons_play_js__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ./icons/play.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/play.js\");\n/* harmony import */ var _icons_rocket_js__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ./icons/rocket.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/rocket.js\");\n/* harmony import */ var _icons_search_js__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ./icons/search.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _icons_settings_js__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ./icons/settings.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/settings.js\");\n/* harmony import */ var _icons_star_js__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ./icons/star.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/star.js\");\n/* harmony import */ var _icons_target_js__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ./icons/target.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/target.js\");\n/* harmony import */ var _icons_trending_up_js__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ./icons/trending-up.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/trending-up.js\");\n/* harmony import */ var _icons_trophy_js__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ./icons/trophy.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/trophy.js\");\n/* harmony import */ var _icons_user_js__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! ./icons/user.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/user.js\");\n/* harmony import */ var _icons_zap_js__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! ./icons/zap.js */ \"(pages-dir-node)/../node_modules/lucide-react/dist/esm/icons/zap.js\");\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Award,Bell,BookOpen,Brain,Calendar,ChevronRight,Clock,Code,Filter,LogOut,Play,Rocket,Search,Settings,Star,Target,TrendingUp,Trophy,User,Zap!=!../node_modules/lucide-react/dist/esm/lucide-react.js\n");

/***/ }),

/***/ "(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../node_modules/recharts/es6/index.js":
/*!******************************************************************************************************************************************************************!*\
  !*** __barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../node_modules/recharts/es6/index.js ***!
  \******************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bar: () => (/* reexport safe */ _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__.Bar),\n/* harmony export */   BarChart: () => (/* reexport safe */ _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__.BarChart),\n/* harmony export */   CartesianGrid: () => (/* reexport safe */ _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__.CartesianGrid),\n/* harmony export */   Legend: () => (/* reexport safe */ _component_Legend__WEBPACK_IMPORTED_MODULE_3__.Legend),\n/* harmony export */   Line: () => (/* reexport safe */ _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__.Line),\n/* harmony export */   LineChart: () => (/* reexport safe */ _chart_LineChart__WEBPACK_IMPORTED_MODULE_5__.LineChart),\n/* harmony export */   ResponsiveContainer: () => (/* reexport safe */ _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__.ResponsiveContainer),\n/* harmony export */   Tooltip: () => (/* reexport safe */ _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__.Tooltip),\n/* harmony export */   XAxis: () => (/* reexport safe */ _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__.XAxis),\n/* harmony export */   YAxis: () => (/* reexport safe */ _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__.YAxis)\n/* harmony export */ });\n/* harmony import */ var _cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./cartesian/Bar */ \"(pages-dir-node)/../node_modules/recharts/es6/cartesian/Bar.js\");\n/* harmony import */ var _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./chart/BarChart */ \"(pages-dir-node)/../node_modules/recharts/es6/chart/BarChart.js\");\n/* harmony import */ var _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./cartesian/CartesianGrid */ \"(pages-dir-node)/../node_modules/recharts/es6/cartesian/CartesianGrid.js\");\n/* harmony import */ var _component_Legend__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./component/Legend */ \"(pages-dir-node)/../node_modules/recharts/es6/component/Legend.js\");\n/* harmony import */ var _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./cartesian/Line */ \"(pages-dir-node)/../node_modules/recharts/es6/cartesian/Line.js\");\n/* harmony import */ var _chart_LineChart__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./chart/LineChart */ \"(pages-dir-node)/../node_modules/recharts/es6/chart/LineChart.js\");\n/* harmony import */ var _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./component/ResponsiveContainer */ \"(pages-dir-node)/../node_modules/recharts/es6/component/ResponsiveContainer.js\");\n/* harmony import */ var _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ./component/Tooltip */ \"(pages-dir-node)/../node_modules/recharts/es6/component/Tooltip.js\");\n/* harmony import */ var _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ./cartesian/XAxis */ \"(pages-dir-node)/../node_modules/recharts/es6/cartesian/XAxis.js\");\n/* harmony import */ var _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ./cartesian/YAxis */ \"(pages-dir-node)/../node_modules/recharts/es6/cartesian/YAxis.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__, _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _component_Legend__WEBPACK_IMPORTED_MODULE_3__, _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__, _chart_LineChart__WEBPACK_IMPORTED_MODULE_5__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__]);\n([_cartesian_Bar__WEBPACK_IMPORTED_MODULE_0__, _chart_BarChart__WEBPACK_IMPORTED_MODULE_1__, _cartesian_CartesianGrid__WEBPACK_IMPORTED_MODULE_2__, _component_Legend__WEBPACK_IMPORTED_MODULE_3__, _cartesian_Line__WEBPACK_IMPORTED_MODULE_4__, _chart_LineChart__WEBPACK_IMPORTED_MODULE_5__, _component_ResponsiveContainer__WEBPACK_IMPORTED_MODULE_6__, _component_Tooltip__WEBPACK_IMPORTED_MODULE_7__, _cartesian_XAxis__WEBPACK_IMPORTED_MODULE_8__, _cartesian_YAxis__WEBPACK_IMPORTED_MODULE_9__] = __webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__);\n\n\n\n\n\n\n\n\n\n\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS9fX2JhcnJlbF9vcHRpbWl6ZV9fP25hbWVzPUJhcixCYXJDaGFydCxDYXJ0ZXNpYW5HcmlkLExlZ2VuZCxMaW5lLExpbmVDaGFydCxSZXNwb25zaXZlQ29udGFpbmVyLFRvb2x0aXAsWEF4aXMsWUF4aXMhPSEuLi9ub2RlX21vZHVsZXMvcmVjaGFydHMvZXM2L2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQUNxQztBQUNNO0FBQ2M7QUFDZDtBQUNKO0FBQ007QUFDd0I7QUFDeEI7QUFDSiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvbm9kZV9tb2R1bGVzL3JlY2hhcnRzL2VzNi9pbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJcbmV4cG9ydCB7IEJhciB9IGZyb20gXCIuL2NhcnRlc2lhbi9CYXJcIlxuZXhwb3J0IHsgQmFyQ2hhcnQgfSBmcm9tIFwiLi9jaGFydC9CYXJDaGFydFwiXG5leHBvcnQgeyBDYXJ0ZXNpYW5HcmlkIH0gZnJvbSBcIi4vY2FydGVzaWFuL0NhcnRlc2lhbkdyaWRcIlxuZXhwb3J0IHsgTGVnZW5kIH0gZnJvbSBcIi4vY29tcG9uZW50L0xlZ2VuZFwiXG5leHBvcnQgeyBMaW5lIH0gZnJvbSBcIi4vY2FydGVzaWFuL0xpbmVcIlxuZXhwb3J0IHsgTGluZUNoYXJ0IH0gZnJvbSBcIi4vY2hhcnQvTGluZUNoYXJ0XCJcbmV4cG9ydCB7IFJlc3BvbnNpdmVDb250YWluZXIgfSBmcm9tIFwiLi9jb21wb25lbnQvUmVzcG9uc2l2ZUNvbnRhaW5lclwiXG5leHBvcnQgeyBUb29sdGlwIH0gZnJvbSBcIi4vY29tcG9uZW50L1Rvb2x0aXBcIlxuZXhwb3J0IHsgWEF4aXMgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vWEF4aXNcIlxuZXhwb3J0IHsgWUF4aXMgfSBmcm9tIFwiLi9jYXJ0ZXNpYW4vWUF4aXNcIiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/__barrel_optimize__?names=Bar,BarChart,CartesianGrid,Legend,Line,LineChart,ResponsiveContainer,Tooltip,XAxis,YAxis!=!../node_modules/recharts/es6/index.js\n");

/***/ }),

/***/ "../../server/app-render/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/server/app-render/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/action-async-storage.external.js");

/***/ }),

/***/ "../../server/app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "clsx":
/*!***********************!*\
  !*** external "clsx" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = import("clsx");;

/***/ }),

/***/ "eventemitter3":
/*!********************************!*\
  !*** external "eventemitter3" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("eventemitter3");

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "lodash/every":
/*!*******************************!*\
  !*** external "lodash/every" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/every");

/***/ }),

/***/ "lodash/find":
/*!******************************!*\
  !*** external "lodash/find" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/find");

/***/ }),

/***/ "lodash/flatMap":
/*!*********************************!*\
  !*** external "lodash/flatMap" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/flatMap");

/***/ }),

/***/ "lodash/get":
/*!*****************************!*\
  !*** external "lodash/get" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/get");

/***/ }),

/***/ "lodash/isBoolean":
/*!***********************************!*\
  !*** external "lodash/isBoolean" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isBoolean");

/***/ }),

/***/ "lodash/isEqual":
/*!*********************************!*\
  !*** external "lodash/isEqual" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isEqual");

/***/ }),

/***/ "lodash/isFunction":
/*!************************************!*\
  !*** external "lodash/isFunction" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isFunction");

/***/ }),

/***/ "lodash/isNaN":
/*!*******************************!*\
  !*** external "lodash/isNaN" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNaN");

/***/ }),

/***/ "lodash/isNil":
/*!*******************************!*\
  !*** external "lodash/isNil" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNil");

/***/ }),

/***/ "lodash/isNumber":
/*!**********************************!*\
  !*** external "lodash/isNumber" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isNumber");

/***/ }),

/***/ "lodash/isObject":
/*!**********************************!*\
  !*** external "lodash/isObject" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isObject");

/***/ }),

/***/ "lodash/isPlainObject":
/*!***************************************!*\
  !*** external "lodash/isPlainObject" ***!
  \***************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isPlainObject");

/***/ }),

/***/ "lodash/isString":
/*!**********************************!*\
  !*** external "lodash/isString" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/isString");

/***/ }),

/***/ "lodash/last":
/*!******************************!*\
  !*** external "lodash/last" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/last");

/***/ }),

/***/ "lodash/mapValues":
/*!***********************************!*\
  !*** external "lodash/mapValues" ***!
  \***********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/mapValues");

/***/ }),

/***/ "lodash/max":
/*!*****************************!*\
  !*** external "lodash/max" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/max");

/***/ }),

/***/ "lodash/memoize":
/*!*********************************!*\
  !*** external "lodash/memoize" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/memoize");

/***/ }),

/***/ "lodash/min":
/*!*****************************!*\
  !*** external "lodash/min" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/min");

/***/ }),

/***/ "lodash/range":
/*!*******************************!*\
  !*** external "lodash/range" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/range");

/***/ }),

/***/ "lodash/some":
/*!******************************!*\
  !*** external "lodash/some" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/some");

/***/ }),

/***/ "lodash/sortBy":
/*!********************************!*\
  !*** external "lodash/sortBy" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/sortBy");

/***/ }),

/***/ "lodash/throttle":
/*!**********************************!*\
  !*** external "lodash/throttle" ***!
  \**********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/throttle");

/***/ }),

/***/ "lodash/uniqBy":
/*!********************************!*\
  !*** external "lodash/uniqBy" ***!
  \********************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/uniqBy");

/***/ }),

/***/ "lodash/upperFirst":
/*!************************************!*\
  !*** external "lodash/upperFirst" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("lodash/upperFirst");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react-smooth":
/*!*******************************!*\
  !*** external "react-smooth" ***!
  \*******************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-smooth");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "recharts-scale":
/*!*********************************!*\
  !*** external "recharts-scale" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("recharts-scale");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tiny-invariant":
/*!*********************************!*\
  !*** external "tiny-invariant" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = import("tiny-invariant");;

/***/ }),

/***/ "victory-vendor/d3-scale":
/*!******************************************!*\
  !*** external "victory-vendor/d3-scale" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("victory-vendor/d3-scale");

/***/ }),

/***/ "victory-vendor/d3-shape":
/*!******************************************!*\
  !*** external "victory-vendor/d3-shape" ***!
  \******************************************/
/***/ ((module) => {

"use strict";
module.exports = require("victory-vendor/d3-shape");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc","vendor-chunks/lucide-react","vendor-chunks/recharts"], () => (__webpack_exec__("(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Fdashboard&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Fdashboard.jsx&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();