@echo off
echo 🔧 Starting SPRINT - AI Backend
echo ===============================

echo 📁 Navigating to backend directory...
cd backend

echo 📦 Installing dependencies (if needed)...
npm install

echo 🔧 Starting backend server...
echo.
echo 🌐 Backend will be available at: http://localhost:5000
echo.
echo 📋 API endpoints:
echo    - Health check: http://localhost:5000/api/hello
echo    - User routes: http://localhost:5000/api/users
echo    - Auth routes: http://localhost:5000/api/auth
echo.

npm run dev
