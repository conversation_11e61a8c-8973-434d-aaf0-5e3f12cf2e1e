# Backend API URL (points to your Express server)
NEXT_PUBLIC_API_URL=http://localhost:5000

# Replicate API for AI features (optional)
REPLICATE_API_TOKEN=your_replicate_token_here

# MongoDB Connection String (REQUIRED) - Using backend's MongoDB
MONGODB_URI=mongodb+srv://admin:<EMAIL>/course?retryWrites=true&w=majority&appName=Cluster0

# JWT Secret for authentication (REQUIRED) - Same as backend
JWT_SECRET=5d750438a20a314afb1d6fdaaa3e033b1fd5ece9113391f5ab1f45ce21fd25db

# PayCaps Payment Integration (optional for payment features)
PAYCAPS_APP_ID=TEST_MODE
PAYCAPS_SECRET=your_paycaps_secret
PAYCAPS_MERCHANT_NAME=CodeSprint
NEXT_PUBLIC_PAYCAPS_PAYMENT_REQUEST_URL=https://api.paycaps.com/payment-request
