"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_process_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/process.js":
/*!***********************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/process.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (() => {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(moduleArg = {}) {\n\nvar Module=moduleArg;var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise((resolve,reject)=>{readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides=Object.assign({},Module);var arguments_=[];var thisProgram=\"./this.program\";var quit_=(status,toThrow)=>{throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!=\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=url=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=(url,onload,onerror)=>{var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=()=>{if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.error.bind(console);Object.assign(Module,moduleOverrides);moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime=Module[\"noExitRuntime\"]||true;if(typeof WebAssembly!=\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(text)}}var HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateMemoryViews(){var b=wasmMemory.buffer;Module[\"HEAP8\"]=HEAP8=new Int8Array(b);Module[\"HEAP16\"]=HEAP16=new Int16Array(b);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(b);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(b);Module[\"HEAP32\"]=HEAP32=new Int32Array(b);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(b);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(b);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(b)}var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;if(!Module[\"noFSInit\"]&&!FS.init.initialized)FS.init();FS.ignorePermissions=false;TTY.init();callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function getUniqueRunDependency(id){return id}function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}function abort(what){if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -sASSERTIONS for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile=\"process.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinarySync(file){if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}throw\"both async and sync fetching of the wasm failed\"}function getBinaryPromise(binaryFile){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+binaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(()=>getBinarySync(binaryFile))}}return Promise.resolve().then(()=>getBinarySync(binaryFile))}function instantiateArrayBuffer(binaryFile,imports,receiver){return getBinaryPromise(binaryFile).then(binary=>WebAssembly.instantiate(binary,imports)).then(instance=>instance).then(receiver,reason=>{err(`failed to asynchronously prepare wasm: ${reason}`);abort(reason)})}function instantiateAsync(binary,binaryFile,imports,callback){if(!binary&&typeof WebAssembly.instantiateStreaming==\"function\"&&!isDataURI(binaryFile)&&typeof fetch==\"function\"){return fetch(binaryFile,{credentials:\"same-origin\"}).then(response=>{var result=WebAssembly.instantiateStreaming(response,imports);return result.then(callback,function(reason){err(`wasm streaming compile failed: ${reason}`);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(binaryFile,imports,callback)})})}return instantiateArrayBuffer(binaryFile,imports,callback)}function createWasm(){var info={\"a\":wasmImports};function receiveInstance(instance,module){wasmExports=instance.exports;wasmMemory=wasmExports[\"K\"];updateMemoryViews();wasmTable=wasmExports[\"O\"];addOnInit(wasmExports[\"L\"]);removeRunDependency(\"wasm-instantiate\");return wasmExports}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}if(Module[\"instantiateWasm\"]){try{return Module[\"instantiateWasm\"](info,receiveInstance)}catch(e){err(`Module.instantiateWasm callback failed with error: ${e}`);readyPromiseReject(e)}}instantiateAsync(wasmBinary,wasmBinaryFile,info,receiveInstantiationResult).catch(readyPromiseReject);return{}}var tempDouble;var tempI64;var callRuntimeCallbacks=callbacks=>{while(callbacks.length>0){callbacks.shift()(Module)}};function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-24;this.set_type=function(type){HEAPU32[this.ptr+4>>2]=type};this.get_type=function(){return HEAPU32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAPU32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAPU32[this.ptr+8>>2]};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_adjusted_ptr(0);this.set_type(type);this.set_destructor(destructor)};this.set_adjusted_ptr=function(adjustedPtr){HEAPU32[this.ptr+16>>2]=adjustedPtr};this.get_adjusted_ptr=function(){return HEAPU32[this.ptr+16>>2]};this.get_exception_ptr=function(){var isPointer=___cxa_is_pointer_type(this.get_type());if(isPointer){return HEAPU32[this.excPtr>>2]}var adjusted=this.get_adjusted_ptr();if(adjusted!==0)return adjusted;return this.excPtr}}var exceptionLast=0;var uncaughtExceptionCount=0;var ___cxa_throw=(ptr,type,destructor)=>{var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw exceptionLast};var tupleRegistrations={};var runDestructors=destructors=>{while(destructors.length){var ptr=destructors.pop();var del=destructors.pop();del(ptr)}};function simpleReadValueFromPointer(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}var awaitingDependencies={};var registeredTypes={};var typeDependencies={};var InternalError;var throwInternalError=message=>{throw new InternalError(message)};var whenDependentTypesAreResolved=(myTypes,dependentTypes,getTypeConverters)=>{myTypes.forEach(function(type){typeDependencies[type]=dependentTypes});function onComplete(typeConverters){var myTypeConverters=getTypeConverters(typeConverters);if(myTypeConverters.length!==myTypes.length){throwInternalError(\"Mismatched type converter count\")}for(var i=0;i<myTypes.length;++i){registerType(myTypes[i],myTypeConverters[i])}}var typeConverters=new Array(dependentTypes.length);var unregisteredTypes=[];var registered=0;dependentTypes.forEach((dt,i)=>{if(registeredTypes.hasOwnProperty(dt)){typeConverters[i]=registeredTypes[dt]}else{unregisteredTypes.push(dt);if(!awaitingDependencies.hasOwnProperty(dt)){awaitingDependencies[dt]=[]}awaitingDependencies[dt].push(()=>{typeConverters[i]=registeredTypes[dt];++registered;if(registered===unregisteredTypes.length){onComplete(typeConverters)}})}});if(0===unregisteredTypes.length){onComplete(typeConverters)}};var __embind_finalize_value_array=rawTupleType=>{var reg=tupleRegistrations[rawTupleType];delete tupleRegistrations[rawTupleType];var elements=reg.elements;var elementsLength=elements.length;var elementTypes=elements.map(elt=>elt.getterReturnType).concat(elements.map(elt=>elt.setterArgumentType));var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;whenDependentTypesAreResolved([rawTupleType],elementTypes,function(elementTypes){elements.forEach((elt,i)=>{var getterReturnType=elementTypes[i];var getter=elt.getter;var getterContext=elt.getterContext;var setterArgumentType=elementTypes[i+elementsLength];var setter=elt.setter;var setterContext=elt.setterContext;elt.read=ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr));elt.write=(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv=new Array(elementsLength);for(var i=0;i<elementsLength;++i){rv[i]=elements[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{if(elementsLength!==o.length){throw new TypeError(`Incorrect number of tuple elements for ${reg.name}: expected=${elementsLength}, actual=${o.length}`)}var ptr=rawConstructor();for(var i=0;i<elementsLength;++i){elements[i].write(ptr,o[i])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var structRegistrations={};var __embind_finalize_value_object=structType=>{var reg=structRegistrations[structType];delete structRegistrations[structType];var rawConstructor=reg.rawConstructor;var rawDestructor=reg.rawDestructor;var fieldRecords=reg.fields;var fieldTypes=fieldRecords.map(field=>field.getterReturnType).concat(fieldRecords.map(field=>field.setterArgumentType));whenDependentTypesAreResolved([structType],fieldTypes,fieldTypes=>{var fields={};fieldRecords.forEach((field,i)=>{var fieldName=field.fieldName;var getterReturnType=fieldTypes[i];var getter=field.getter;var getterContext=field.getterContext;var setterArgumentType=fieldTypes[i+fieldRecords.length];var setter=field.setter;var setterContext=field.setterContext;fields[fieldName]={read:ptr=>getterReturnType[\"fromWireType\"](getter(getterContext,ptr)),write:(ptr,o)=>{var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,o));runDestructors(destructors)}}});return[{name:reg.name,\"fromWireType\":ptr=>{var rv={};for(var i in fields){rv[i]=fields[i].read(ptr)}rawDestructor(ptr);return rv},\"toWireType\":(destructors,o)=>{for(var fieldName in fields){if(!(fieldName in o)){throw new TypeError(`Missing field: \"${fieldName}\"`)}}var ptr=rawConstructor();for(fieldName in fields){fields[fieldName].write(ptr,o[fieldName])}if(destructors!==null){destructors.push(rawDestructor,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:rawDestructor}]})};var __embind_register_bigint=(primitiveType,name,size,minRange,maxRange)=>{};var embind_init_charCodes=()=>{var codes=new Array(256);for(var i=0;i<256;++i){codes[i]=String.fromCharCode(i)}embind_charCodes=codes};var embind_charCodes;var readLatin1String=ptr=>{var ret=\"\";var c=ptr;while(HEAPU8[c]){ret+=embind_charCodes[HEAPU8[c++]]}return ret};var BindingError;var throwBindingError=message=>{throw new BindingError(message)};function sharedRegisterType(rawType,registeredInstance,options={}){var name=registeredInstance.name;if(!rawType){throwBindingError(`type \"${name}\" must have a positive integer typeid pointer`)}if(registeredTypes.hasOwnProperty(rawType)){if(options.ignoreDuplicateRegistrations){return}else{throwBindingError(`Cannot register type '${name}' twice`)}}registeredTypes[rawType]=registeredInstance;delete typeDependencies[rawType];if(awaitingDependencies.hasOwnProperty(rawType)){var callbacks=awaitingDependencies[rawType];delete awaitingDependencies[rawType];callbacks.forEach(cb=>cb())}}function registerType(rawType,registeredInstance,options={}){if(!(\"argPackAdvance\"in registeredInstance)){throw new TypeError(\"registerType registeredInstance requires argPackAdvance\")}return sharedRegisterType(rawType,registeredInstance,options)}var GenericWireTypeSize=8;var __embind_register_bool=(rawType,name,trueValue,falseValue)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":function(wt){return!!wt},\"toWireType\":function(destructors,o){return o?trueValue:falseValue},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":function(pointer){return this[\"fromWireType\"](HEAPU8[pointer])},destructorFunction:null})};var shallowCopyInternalPointer=o=>({count:o.count,deleteScheduled:o.deleteScheduled,preservePointerOnDelete:o.preservePointerOnDelete,ptr:o.ptr,ptrType:o.ptrType,smartPtr:o.smartPtr,smartPtrType:o.smartPtrType});var throwInstanceAlreadyDeleted=obj=>{function getInstanceTypeName(handle){return handle.$$.ptrType.registeredClass.name}throwBindingError(getInstanceTypeName(obj)+\" instance already deleted\")};var finalizationRegistry=false;var detachFinalizer=handle=>{};var runDestructor=$$=>{if($$.smartPtr){$$.smartPtrType.rawDestructor($$.smartPtr)}else{$$.ptrType.registeredClass.rawDestructor($$.ptr)}};var releaseClassHandle=$$=>{$$.count.value-=1;var toDelete=0===$$.count.value;if(toDelete){runDestructor($$)}};var downcastPointer=(ptr,ptrClass,desiredClass)=>{if(ptrClass===desiredClass){return ptr}if(undefined===desiredClass.baseClass){return null}var rv=downcastPointer(ptr,ptrClass,desiredClass.baseClass);if(rv===null){return null}return desiredClass.downcast(rv)};var registeredPointers={};var getInheritedInstanceCount=()=>Object.keys(registeredInstances).length;var getLiveInheritedInstances=()=>{var rv=[];for(var k in registeredInstances){if(registeredInstances.hasOwnProperty(k)){rv.push(registeredInstances[k])}}return rv};var deletionQueue=[];var flushPendingDeletes=()=>{while(deletionQueue.length){var obj=deletionQueue.pop();obj.$$.deleteScheduled=false;obj[\"delete\"]()}};var delayFunction;var setDelayFunction=fn=>{delayFunction=fn;if(deletionQueue.length&&delayFunction){delayFunction(flushPendingDeletes)}};var init_embind=()=>{Module[\"getInheritedInstanceCount\"]=getInheritedInstanceCount;Module[\"getLiveInheritedInstances\"]=getLiveInheritedInstances;Module[\"flushPendingDeletes\"]=flushPendingDeletes;Module[\"setDelayFunction\"]=setDelayFunction};var registeredInstances={};var getBasestPointer=(class_,ptr)=>{if(ptr===undefined){throwBindingError(\"ptr should not be undefined\")}while(class_.baseClass){ptr=class_.upcast(ptr);class_=class_.baseClass}return ptr};var getInheritedInstance=(class_,ptr)=>{ptr=getBasestPointer(class_,ptr);return registeredInstances[ptr]};var makeClassHandle=(prototype,record)=>{if(!record.ptrType||!record.ptr){throwInternalError(\"makeClassHandle requires ptr and ptrType\")}var hasSmartPtrType=!!record.smartPtrType;var hasSmartPtr=!!record.smartPtr;if(hasSmartPtrType!==hasSmartPtr){throwInternalError(\"Both smartPtrType and smartPtr must be specified\")}record.count={value:1};return attachFinalizer(Object.create(prototype,{$$:{value:record}}))};function RegisteredPointer_fromWireType(ptr){var rawPointer=this.getPointee(ptr);if(!rawPointer){this.destructor(ptr);return null}var registeredInstance=getInheritedInstance(this.registeredClass,rawPointer);if(undefined!==registeredInstance){if(0===registeredInstance.$$.count.value){registeredInstance.$$.ptr=rawPointer;registeredInstance.$$.smartPtr=ptr;return registeredInstance[\"clone\"]()}else{var rv=registeredInstance[\"clone\"]();this.destructor(ptr);return rv}}function makeDefaultHandle(){if(this.isSmartPointer){return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this.pointeeType,ptr:rawPointer,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(this.registeredClass.instancePrototype,{ptrType:this,ptr:ptr})}}var actualType=this.registeredClass.getActualType(rawPointer);var registeredPointerRecord=registeredPointers[actualType];if(!registeredPointerRecord){return makeDefaultHandle.call(this)}var toType;if(this.isConst){toType=registeredPointerRecord.constPointerType}else{toType=registeredPointerRecord.pointerType}var dp=downcastPointer(rawPointer,this.registeredClass,toType.registeredClass);if(dp===null){return makeDefaultHandle.call(this)}if(this.isSmartPointer){return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp,smartPtrType:this,smartPtr:ptr})}else{return makeClassHandle(toType.registeredClass.instancePrototype,{ptrType:toType,ptr:dp})}}var attachFinalizer=handle=>{if(\"undefined\"===typeof FinalizationRegistry){attachFinalizer=handle=>handle;return handle}finalizationRegistry=new FinalizationRegistry(info=>{releaseClassHandle(info.$$)});attachFinalizer=handle=>{var $$=handle.$$;var hasSmartPtr=!!$$.smartPtr;if(hasSmartPtr){var info={$$:$$};finalizationRegistry.register(handle,info,handle)}return handle};detachFinalizer=handle=>finalizationRegistry.unregister(handle);return attachFinalizer(handle)};var init_ClassHandle=()=>{Object.assign(ClassHandle.prototype,{\"isAliasOf\"(other){if(!(this instanceof ClassHandle)){return false}if(!(other instanceof ClassHandle)){return false}var leftClass=this.$$.ptrType.registeredClass;var left=this.$$.ptr;other.$$=other.$$;var rightClass=other.$$.ptrType.registeredClass;var right=other.$$.ptr;while(leftClass.baseClass){left=leftClass.upcast(left);leftClass=leftClass.baseClass}while(rightClass.baseClass){right=rightClass.upcast(right);rightClass=rightClass.baseClass}return leftClass===rightClass&&left===right},\"clone\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.preservePointerOnDelete){this.$$.count.value+=1;return this}else{var clone=attachFinalizer(Object.create(Object.getPrototypeOf(this),{$$:{value:shallowCopyInternalPointer(this.$$)}}));clone.$$.count.value+=1;clone.$$.deleteScheduled=false;return clone}},\"delete\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}detachFinalizer(this);releaseClassHandle(this.$$);if(!this.$$.preservePointerOnDelete){this.$$.smartPtr=undefined;this.$$.ptr=undefined}},\"isDeleted\"(){return!this.$$.ptr},\"deleteLater\"(){if(!this.$$.ptr){throwInstanceAlreadyDeleted(this)}if(this.$$.deleteScheduled&&!this.$$.preservePointerOnDelete){throwBindingError(\"Object already scheduled for deletion\")}deletionQueue.push(this);if(deletionQueue.length===1&&delayFunction){delayFunction(flushPendingDeletes)}this.$$.deleteScheduled=true;return this}})};function ClassHandle(){}var char_0=48;var char_9=57;var makeLegalFunctionName=name=>{if(undefined===name){return\"_unknown\"}name=name.replace(/[^a-zA-Z0-9_]/g,\"$\");var f=name.charCodeAt(0);if(f>=char_0&&f<=char_9){return`_${name}`}return name};function createNamedFunction(name,body){name=makeLegalFunctionName(name);return{[name]:function(){return body.apply(this,arguments)}}[name]}var ensureOverloadTable=(proto,methodName,humanName)=>{if(undefined===proto[methodName].overloadTable){var prevFunc=proto[methodName];proto[methodName]=function(){if(!proto[methodName].overloadTable.hasOwnProperty(arguments.length)){throwBindingError(`Function '${humanName}' called with an invalid number of arguments (${arguments.length}) - expects one of (${proto[methodName].overloadTable})!`)}return proto[methodName].overloadTable[arguments.length].apply(this,arguments)};proto[methodName].overloadTable=[];proto[methodName].overloadTable[prevFunc.argCount]=prevFunc}};var exposePublicSymbol=(name,value,numArguments)=>{if(Module.hasOwnProperty(name)){if(undefined===numArguments||undefined!==Module[name].overloadTable&&undefined!==Module[name].overloadTable[numArguments]){throwBindingError(`Cannot register public name '${name}' twice`)}ensureOverloadTable(Module,name,name);if(Module.hasOwnProperty(numArguments)){throwBindingError(`Cannot register multiple overloads of a function with the same number of arguments (${numArguments})!`)}Module[name].overloadTable[numArguments]=value}else{Module[name]=value;if(undefined!==numArguments){Module[name].numArguments=numArguments}}};function RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast){this.name=name;this.constructor=constructor;this.instancePrototype=instancePrototype;this.rawDestructor=rawDestructor;this.baseClass=baseClass;this.getActualType=getActualType;this.upcast=upcast;this.downcast=downcast;this.pureVirtualFunctions=[]}var upcastPointer=(ptr,ptrClass,desiredClass)=>{while(ptrClass!==desiredClass){if(!ptrClass.upcast){throwBindingError(`Expected null or instance of ${desiredClass.name}, got an instance of ${ptrClass.name}`)}ptr=ptrClass.upcast(ptr);ptrClass=ptrClass.baseClass}return ptr};function constNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function genericPointerToWireType(destructors,handle){var ptr;if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}if(this.isSmartPointer){ptr=this.rawConstructor();if(destructors!==null){destructors.push(this.rawDestructor,ptr)}return ptr}else{return 0}}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(!this.isConst&&handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);if(this.isSmartPointer){if(undefined===handle.$$.smartPtr){throwBindingError(\"Passing raw pointer to smart pointer is illegal\")}switch(this.sharingPolicy){case 0:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{throwBindingError(`Cannot convert argument of type ${handle.$$.smartPtrType?handle.$$.smartPtrType.name:handle.$$.ptrType.name} to parameter type ${this.name}`)}break;case 1:ptr=handle.$$.smartPtr;break;case 2:if(handle.$$.smartPtrType===this){ptr=handle.$$.smartPtr}else{var clonedHandle=handle[\"clone\"]();ptr=this.rawShare(ptr,Emval.toHandle(()=>clonedHandle[\"delete\"]()));if(destructors!==null){destructors.push(this.rawDestructor,ptr)}}break;default:throwBindingError(\"Unsupporting sharing policy\")}}return ptr}function nonConstNoSmartPtrRawPointerToWireType(destructors,handle){if(handle===null){if(this.isReference){throwBindingError(`null is not a valid ${this.name}`)}return 0}if(!handle.$$){throwBindingError(`Cannot pass \"${embindRepr(handle)}\" as a ${this.name}`)}if(!handle.$$.ptr){throwBindingError(`Cannot pass deleted object as a pointer of type ${this.name}`)}if(handle.$$.ptrType.isConst){throwBindingError(`Cannot convert argument of type ${handle.$$.ptrType.name} to parameter type ${this.name}`)}var handleClass=handle.$$.ptrType.registeredClass;var ptr=upcastPointer(handle.$$.ptr,handleClass,this.registeredClass);return ptr}function readPointer(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])}var init_RegisteredPointer=()=>{Object.assign(RegisteredPointer.prototype,{getPointee(ptr){if(this.rawGetPointee){ptr=this.rawGetPointee(ptr)}return ptr},destructor(ptr){if(this.rawDestructor){this.rawDestructor(ptr)}},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,\"deleteObject\"(handle){if(handle!==null){handle[\"delete\"]()}},\"fromWireType\":RegisteredPointer_fromWireType})};function RegisteredPointer(name,registeredClass,isReference,isConst,isSmartPointer,pointeeType,sharingPolicy,rawGetPointee,rawConstructor,rawShare,rawDestructor){this.name=name;this.registeredClass=registeredClass;this.isReference=isReference;this.isConst=isConst;this.isSmartPointer=isSmartPointer;this.pointeeType=pointeeType;this.sharingPolicy=sharingPolicy;this.rawGetPointee=rawGetPointee;this.rawConstructor=rawConstructor;this.rawShare=rawShare;this.rawDestructor=rawDestructor;if(!isSmartPointer&&registeredClass.baseClass===undefined){if(isConst){this[\"toWireType\"]=constNoSmartPtrRawPointerToWireType;this.destructorFunction=null}else{this[\"toWireType\"]=nonConstNoSmartPtrRawPointerToWireType;this.destructorFunction=null}}else{this[\"toWireType\"]=genericPointerToWireType}}var replacePublicSymbol=(name,value,numArguments)=>{if(!Module.hasOwnProperty(name)){throwInternalError(\"Replacing nonexistant public symbol\")}if(undefined!==Module[name].overloadTable&&undefined!==numArguments){Module[name].overloadTable[numArguments]=value}else{Module[name]=value;Module[name].argCount=numArguments}};var dynCallLegacy=(sig,ptr,args)=>{var f=Module[\"dynCall_\"+sig];return args&&args.length?f.apply(null,[ptr].concat(args)):f.call(null,ptr)};var wasmTableMirror=[];var wasmTable;var getWasmTableEntry=funcPtr=>{var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func};var dynCall=(sig,ptr,args)=>{if(sig.includes(\"j\")){return dynCallLegacy(sig,ptr,args)}var rtn=getWasmTableEntry(ptr).apply(null,args);return rtn};var getDynCaller=(sig,ptr)=>{var argCache=[];return function(){argCache.length=0;Object.assign(argCache,arguments);return dynCall(sig,ptr,argCache)}};var embind__requireFunction=(signature,rawFunction)=>{signature=readLatin1String(signature);function makeDynCaller(){if(signature.includes(\"j\")){return getDynCaller(signature,rawFunction)}return getWasmTableEntry(rawFunction)}var fp=makeDynCaller();if(typeof fp!=\"function\"){throwBindingError(`unknown function pointer with signature ${signature}: ${rawFunction}`)}return fp};var extendError=(baseErrorType,errorName)=>{var errorClass=createNamedFunction(errorName,function(message){this.name=errorName;this.message=message;var stack=new Error(message).stack;if(stack!==undefined){this.stack=this.toString()+\"\\n\"+stack.replace(/^Error(:[^\\n]*)?\\n/,\"\")}});errorClass.prototype=Object.create(baseErrorType.prototype);errorClass.prototype.constructor=errorClass;errorClass.prototype.toString=function(){if(this.message===undefined){return this.name}else{return`${this.name}: ${this.message}`}};return errorClass};var UnboundTypeError;var getTypeName=type=>{var ptr=___getTypeName(type);var rv=readLatin1String(ptr);_free(ptr);return rv};var throwUnboundTypeError=(message,types)=>{var unboundTypes=[];var seen={};function visit(type){if(seen[type]){return}if(registeredTypes[type]){return}if(typeDependencies[type]){typeDependencies[type].forEach(visit);return}unboundTypes.push(type);seen[type]=true}types.forEach(visit);throw new UnboundTypeError(`${message}: `+unboundTypes.map(getTypeName).join([\", \"]))};var __embind_register_class=(rawType,rawPointerType,rawConstPointerType,baseClassRawType,getActualTypeSignature,getActualType,upcastSignature,upcast,downcastSignature,downcast,name,destructorSignature,rawDestructor)=>{name=readLatin1String(name);getActualType=embind__requireFunction(getActualTypeSignature,getActualType);if(upcast){upcast=embind__requireFunction(upcastSignature,upcast)}if(downcast){downcast=embind__requireFunction(downcastSignature,downcast)}rawDestructor=embind__requireFunction(destructorSignature,rawDestructor);var legalFunctionName=makeLegalFunctionName(name);exposePublicSymbol(legalFunctionName,function(){throwUnboundTypeError(`Cannot construct ${name} due to unbound types`,[baseClassRawType])});whenDependentTypesAreResolved([rawType,rawPointerType,rawConstPointerType],baseClassRawType?[baseClassRawType]:[],function(base){base=base[0];var baseClass;var basePrototype;if(baseClassRawType){baseClass=base.registeredClass;basePrototype=baseClass.instancePrototype}else{basePrototype=ClassHandle.prototype}var constructor=createNamedFunction(legalFunctionName,function(){if(Object.getPrototypeOf(this)!==instancePrototype){throw new BindingError(\"Use 'new' to construct \"+name)}if(undefined===registeredClass.constructor_body){throw new BindingError(name+\" has no accessible constructor\")}var body=registeredClass.constructor_body[arguments.length];if(undefined===body){throw new BindingError(`Tried to invoke ctor of ${name} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(registeredClass.constructor_body).toString()}) parameters instead!`)}return body.apply(this,arguments)});var instancePrototype=Object.create(basePrototype,{constructor:{value:constructor}});constructor.prototype=instancePrototype;var registeredClass=new RegisteredClass(name,constructor,instancePrototype,rawDestructor,baseClass,getActualType,upcast,downcast);if(registeredClass.baseClass){if(registeredClass.baseClass.__derivedClasses===undefined){registeredClass.baseClass.__derivedClasses=[]}registeredClass.baseClass.__derivedClasses.push(registeredClass)}var referenceConverter=new RegisteredPointer(name,registeredClass,true,false,false);var pointerConverter=new RegisteredPointer(name+\"*\",registeredClass,false,false,false);var constPointerConverter=new RegisteredPointer(name+\" const*\",registeredClass,false,true,false);registeredPointers[rawType]={pointerType:pointerConverter,constPointerType:constPointerConverter};replacePublicSymbol(legalFunctionName,constructor);return[referenceConverter,pointerConverter,constPointerConverter]})};var heap32VectorToArray=(count,firstElement)=>{var array=[];for(var i=0;i<count;i++){array.push(HEAPU32[firstElement+i*4>>2])}return array};function newFunc(constructor,argumentList){if(!(constructor instanceof Function)){throw new TypeError(`new_ called with constructor type ${typeof constructor} which is not a function`)}var dummy=createNamedFunction(constructor.name||\"unknownFunctionName\",function(){});dummy.prototype=constructor.prototype;var obj=new dummy;var r=constructor.apply(obj,argumentList);return r instanceof Object?r:obj}function craftInvokerFunction(humanName,argTypes,classType,cppInvokerFunc,cppTargetFunc,isAsync){var argCount=argTypes.length;if(argCount<2){throwBindingError(\"argTypes array size mismatch! Must at least get return value and 'this' types!\")}var isClassMethodFunc=argTypes[1]!==null&&classType!==null;var needsDestructorStack=false;for(var i=1;i<argTypes.length;++i){if(argTypes[i]!==null&&argTypes[i].destructorFunction===undefined){needsDestructorStack=true;break}}var returns=argTypes[0].name!==\"void\";var argsList=\"\";var argsListWired=\"\";for(var i=0;i<argCount-2;++i){argsList+=(i!==0?\", \":\"\")+\"arg\"+i;argsListWired+=(i!==0?\", \":\"\")+\"arg\"+i+\"Wired\"}var invokerFnBody=`\\n        return function ${makeLegalFunctionName(humanName)}(${argsList}) {\\n        if (arguments.length !== ${argCount-2}) {\\n          throwBindingError('function ${humanName} called with ' + arguments.length + ' arguments, expected ${argCount-2}');\\n        }`;if(needsDestructorStack){invokerFnBody+=\"var destructors = [];\\n\"}var dtorStack=needsDestructorStack?\"destructors\":\"null\";var args1=[\"throwBindingError\",\"invoker\",\"fn\",\"runDestructors\",\"retType\",\"classParam\"];var args2=[throwBindingError,cppInvokerFunc,cppTargetFunc,runDestructors,argTypes[0],argTypes[1]];if(isClassMethodFunc){invokerFnBody+=\"var thisWired = classParam.toWireType(\"+dtorStack+\", this);\\n\"}for(var i=0;i<argCount-2;++i){invokerFnBody+=\"var arg\"+i+\"Wired = argType\"+i+\".toWireType(\"+dtorStack+\", arg\"+i+\"); // \"+argTypes[i+2].name+\"\\n\";args1.push(\"argType\"+i);args2.push(argTypes[i+2])}if(isClassMethodFunc){argsListWired=\"thisWired\"+(argsListWired.length>0?\", \":\"\")+argsListWired}invokerFnBody+=(returns||isAsync?\"var rv = \":\"\")+\"invoker(fn\"+(argsListWired.length>0?\", \":\"\")+argsListWired+\");\\n\";if(needsDestructorStack){invokerFnBody+=\"runDestructors(destructors);\\n\"}else{for(var i=isClassMethodFunc?1:2;i<argTypes.length;++i){var paramName=i===1?\"thisWired\":\"arg\"+(i-2)+\"Wired\";if(argTypes[i].destructorFunction!==null){invokerFnBody+=paramName+\"_dtor(\"+paramName+\"); // \"+argTypes[i].name+\"\\n\";args1.push(paramName+\"_dtor\");args2.push(argTypes[i].destructorFunction)}}}if(returns){invokerFnBody+=\"var ret = retType.fromWireType(rv);\\n\"+\"return ret;\\n\"}else{}invokerFnBody+=\"}\\n\";args1.push(invokerFnBody);return newFunc(Function,args1).apply(null,args2)}var __embind_register_class_constructor=(rawClassType,argCount,rawArgTypesAddr,invokerSignature,invoker,rawConstructor)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);invoker=embind__requireFunction(invokerSignature,invoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`constructor ${classType.name}`;if(undefined===classType.registeredClass.constructor_body){classType.registeredClass.constructor_body=[]}if(undefined!==classType.registeredClass.constructor_body[argCount-1]){throw new BindingError(`Cannot register multiple constructors with identical number of parameters (${argCount-1}) for class '${classType.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`)}classType.registeredClass.constructor_body[argCount-1]=()=>{throwUnboundTypeError(`Cannot construct ${classType.name} due to unbound types`,rawArgTypes)};whenDependentTypesAreResolved([],rawArgTypes,argTypes=>{argTypes.splice(1,0,null);classType.registeredClass.constructor_body[argCount-1]=craftInvokerFunction(humanName,argTypes,null,invoker,rawConstructor);return[]});return[]})};var __embind_register_class_function=(rawClassType,methodName,argCount,rawArgTypesAddr,invokerSignature,rawInvoker,context,isPureVirtual,isAsync)=>{var rawArgTypes=heap32VectorToArray(argCount,rawArgTypesAddr);methodName=readLatin1String(methodName);rawInvoker=embind__requireFunction(invokerSignature,rawInvoker);whenDependentTypesAreResolved([],[rawClassType],function(classType){classType=classType[0];var humanName=`${classType.name}.${methodName}`;if(methodName.startsWith(\"@@\")){methodName=Symbol[methodName.substring(2)]}if(isPureVirtual){classType.registeredClass.pureVirtualFunctions.push(methodName)}function unboundTypesHandler(){throwUnboundTypeError(`Cannot call ${humanName} due to unbound types`,rawArgTypes)}var proto=classType.registeredClass.instancePrototype;var method=proto[methodName];if(undefined===method||undefined===method.overloadTable&&method.className!==classType.name&&method.argCount===argCount-2){unboundTypesHandler.argCount=argCount-2;unboundTypesHandler.className=classType.name;proto[methodName]=unboundTypesHandler}else{ensureOverloadTable(proto,methodName,humanName);proto[methodName].overloadTable[argCount-2]=unboundTypesHandler}whenDependentTypesAreResolved([],rawArgTypes,function(argTypes){var memberFunction=craftInvokerFunction(humanName,argTypes,classType,rawInvoker,context,isAsync);if(undefined===proto[methodName].overloadTable){memberFunction.argCount=argCount-2;proto[methodName]=memberFunction}else{proto[methodName].overloadTable[argCount-2]=memberFunction}return[]});return[]})};var validateThis=(this_,classType,humanName)=>{if(!(this_ instanceof Object)){throwBindingError(`${humanName} with invalid \"this\": ${this_}`)}if(!(this_ instanceof classType.registeredClass.constructor)){throwBindingError(`${humanName} incompatible with \"this\" of type ${this_.constructor.name}`)}if(!this_.$$.ptr){throwBindingError(`cannot call emscripten binding method ${humanName} on deleted object`)}return upcastPointer(this_.$$.ptr,this_.$$.ptrType.registeredClass,classType.registeredClass)};var __embind_register_class_property=(classType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{fieldName=readLatin1String(fieldName);getter=embind__requireFunction(getterSignature,getter);whenDependentTypesAreResolved([],[classType],function(classType){classType=classType[0];var humanName=`${classType.name}.${fieldName}`;var desc={get(){throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])},enumerable:true,configurable:true};if(setter){desc.set=()=>throwUnboundTypeError(`Cannot access ${humanName} due to unbound types`,[getterReturnType,setterArgumentType])}else{desc.set=v=>throwBindingError(humanName+\" is a read-only property\")}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);whenDependentTypesAreResolved([],setter?[getterReturnType,setterArgumentType]:[getterReturnType],function(types){var getterReturnType=types[0];var desc={get(){var ptr=validateThis(this,classType,humanName+\" getter\");return getterReturnType[\"fromWireType\"](getter(getterContext,ptr))},enumerable:true};if(setter){setter=embind__requireFunction(setterSignature,setter);var setterArgumentType=types[1];desc.set=function(v){var ptr=validateThis(this,classType,humanName+\" setter\");var destructors=[];setter(setterContext,ptr,setterArgumentType[\"toWireType\"](destructors,v));runDestructors(destructors)}}Object.defineProperty(classType.registeredClass.instancePrototype,fieldName,desc);return[]});return[]})};function handleAllocatorInit(){Object.assign(HandleAllocator.prototype,{get(id){return this.allocated[id]},has(id){return this.allocated[id]!==undefined},allocate(handle){var id=this.freelist.pop()||this.allocated.length;this.allocated[id]=handle;return id},free(id){this.allocated[id]=undefined;this.freelist.push(id)}})}function HandleAllocator(){this.allocated=[undefined];this.freelist=[]}var emval_handles=new HandleAllocator;var __emval_decref=handle=>{if(handle>=emval_handles.reserved&&0===--emval_handles.get(handle).refcount){emval_handles.free(handle)}};var count_emval_handles=()=>{var count=0;for(var i=emval_handles.reserved;i<emval_handles.allocated.length;++i){if(emval_handles.allocated[i]!==undefined){++count}}return count};var init_emval=()=>{emval_handles.allocated.push({value:undefined},{value:null},{value:true},{value:false});emval_handles.reserved=emval_handles.allocated.length;Module[\"count_emval_handles\"]=count_emval_handles};var Emval={toValue:handle=>{if(!handle){throwBindingError(\"Cannot use deleted val. handle = \"+handle)}return emval_handles.get(handle).value},toHandle:value=>{switch(value){case undefined:return 1;case null:return 2;case true:return 3;case false:return 4;default:{return emval_handles.allocate({refcount:1,value:value})}}}};var __embind_register_emval=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":handle=>{var rv=Emval.toValue(handle);__emval_decref(handle);return rv},\"toWireType\":(destructors,value)=>Emval.toHandle(value),\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction:null})};var enumReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?function(pointer){return this[\"fromWireType\"](HEAP8[pointer>>0])}:function(pointer){return this[\"fromWireType\"](HEAPU8[pointer>>0])};case 2:return signed?function(pointer){return this[\"fromWireType\"](HEAP16[pointer>>1])}:function(pointer){return this[\"fromWireType\"](HEAPU16[pointer>>1])};case 4:return signed?function(pointer){return this[\"fromWireType\"](HEAP32[pointer>>2])}:function(pointer){return this[\"fromWireType\"](HEAPU32[pointer>>2])};default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_enum=(rawType,name,size,isSigned)=>{name=readLatin1String(name);function ctor(){}ctor.values={};registerType(rawType,{name:name,constructor:ctor,\"fromWireType\":function(c){return this.constructor.values[c]},\"toWireType\":(destructors,c)=>c.value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":enumReadValueFromPointer(name,size,isSigned),destructorFunction:null});exposePublicSymbol(name,ctor)};var requireRegisteredType=(rawType,humanName)=>{var impl=registeredTypes[rawType];if(undefined===impl){throwBindingError(humanName+\" has unknown type \"+getTypeName(rawType))}return impl};var __embind_register_enum_value=(rawEnumType,name,enumValue)=>{var enumType=requireRegisteredType(rawEnumType,\"enum\");name=readLatin1String(name);var Enum=enumType.constructor;var Value=Object.create(enumType.constructor.prototype,{value:{value:enumValue},constructor:{value:createNamedFunction(`${enumType.name}_${name}`,function(){})}});Enum.values[enumValue]=Value;Enum[name]=Value};var embindRepr=v=>{if(v===null){return\"null\"}var t=typeof v;if(t===\"object\"||t===\"array\"||t===\"function\"){return v.toString()}else{return\"\"+v}};var floatReadValueFromPointer=(name,width)=>{switch(width){case 4:return function(pointer){return this[\"fromWireType\"](HEAPF32[pointer>>2])};case 8:return function(pointer){return this[\"fromWireType\"](HEAPF64[pointer>>3])};default:throw new TypeError(`invalid float width (${width}): ${name}`)}};var __embind_register_float=(rawType,name,size)=>{name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":value=>value,\"toWireType\":(destructors,value)=>value,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":floatReadValueFromPointer(name,size),destructorFunction:null})};var __embind_register_function=(name,argCount,rawArgTypesAddr,signature,rawInvoker,fn,isAsync)=>{var argTypes=heap32VectorToArray(argCount,rawArgTypesAddr);name=readLatin1String(name);rawInvoker=embind__requireFunction(signature,rawInvoker);exposePublicSymbol(name,function(){throwUnboundTypeError(`Cannot call ${name} due to unbound types`,argTypes)},argCount-1);whenDependentTypesAreResolved([],argTypes,function(argTypes){var invokerArgsArray=[argTypes[0],null].concat(argTypes.slice(1));replacePublicSymbol(name,craftInvokerFunction(name,invokerArgsArray,null,rawInvoker,fn,isAsync),argCount-1);return[]})};var integerReadValueFromPointer=(name,width,signed)=>{switch(width){case 1:return signed?pointer=>HEAP8[pointer>>0]:pointer=>HEAPU8[pointer>>0];case 2:return signed?pointer=>HEAP16[pointer>>1]:pointer=>HEAPU16[pointer>>1];case 4:return signed?pointer=>HEAP32[pointer>>2]:pointer=>HEAPU32[pointer>>2];default:throw new TypeError(`invalid integer width (${width}): ${name}`)}};var __embind_register_integer=(primitiveType,name,size,minRange,maxRange)=>{name=readLatin1String(name);if(maxRange===-1){maxRange=4294967295}var fromWireType=value=>value;if(minRange===0){var bitshift=32-8*size;fromWireType=value=>value<<bitshift>>>bitshift}var isUnsignedType=name.includes(\"unsigned\");var checkAssertions=(value,toTypeName)=>{};var toWireType;if(isUnsignedType){toWireType=function(destructors,value){checkAssertions(value,this.name);return value>>>0}}else{toWireType=function(destructors,value){checkAssertions(value,this.name);return value}}registerType(primitiveType,{name:name,\"fromWireType\":fromWireType,\"toWireType\":toWireType,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":integerReadValueFromPointer(name,size,minRange!==0),destructorFunction:null})};var __embind_register_memory_view=(rawType,dataTypeIndex,name)=>{var typeMapping=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array];var TA=typeMapping[dataTypeIndex];function decodeMemoryView(handle){var size=HEAPU32[handle>>2];var data=HEAPU32[handle+4>>2];return new TA(HEAP8.buffer,data,size)}name=readLatin1String(name);registerType(rawType,{name:name,\"fromWireType\":decodeMemoryView,\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":decodeMemoryView},{ignoreDuplicateRegistrations:true})};var stringToUTF8Array=(str,heap,outIdx,maxBytesToWrite)=>{if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx};var stringToUTF8=(str,outPtr,maxBytesToWrite)=>stringToUTF8Array(str,HEAPU8,outPtr,maxBytesToWrite);var lengthBytesUTF8=str=>{var len=0;for(var i=0;i<str.length;++i){var c=str.charCodeAt(i);if(c<=127){len++}else if(c<=2047){len+=2}else if(c>=55296&&c<=57343){len+=4;++i}else{len+=3}}return len};var UTF8Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf8\"):undefined;var UTF8ArrayToString=(heapOrArray,idx,maxBytesToRead)=>{var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heapOrArray[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heapOrArray.buffer&&UTF8Decoder){return UTF8Decoder.decode(heapOrArray.subarray(idx,endPtr))}var str=\"\";while(idx<endPtr){var u0=heapOrArray[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heapOrArray[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heapOrArray[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heapOrArray[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}return str};var UTF8ToString=(ptr,maxBytesToRead)=>ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\";var __embind_register_std_string=(rawType,name)=>{name=readLatin1String(name);var stdStringIsUTF8=name===\"std::string\";registerType(rawType,{name:name,\"fromWireType\"(value){var length=HEAPU32[value>>2];var payload=value+4;var str;if(stdStringIsUTF8){var decodeStartPtr=payload;for(var i=0;i<=length;++i){var currentBytePtr=payload+i;if(i==length||HEAPU8[currentBytePtr]==0){var maxRead=currentBytePtr-decodeStartPtr;var stringSegment=UTF8ToString(decodeStartPtr,maxRead);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+1}}}else{var a=new Array(length);for(var i=0;i<length;++i){a[i]=String.fromCharCode(HEAPU8[payload+i])}str=a.join(\"\")}_free(value);return str},\"toWireType\"(destructors,value){if(value instanceof ArrayBuffer){value=new Uint8Array(value)}var length;var valueIsOfTypeString=typeof value==\"string\";if(!(valueIsOfTypeString||value instanceof Uint8Array||value instanceof Uint8ClampedArray||value instanceof Int8Array)){throwBindingError(\"Cannot pass non-string to std::string\")}if(stdStringIsUTF8&&valueIsOfTypeString){length=lengthBytesUTF8(value)}else{length=value.length}var base=_malloc(4+length+1);var ptr=base+4;HEAPU32[base>>2]=length;if(stdStringIsUTF8&&valueIsOfTypeString){stringToUTF8(value,ptr,length+1)}else{if(valueIsOfTypeString){for(var i=0;i<length;++i){var charCode=value.charCodeAt(i);if(charCode>255){_free(ptr);throwBindingError(\"String has UTF-16 code units that do not fit in 8 bits\")}HEAPU8[ptr+i]=charCode}}else{for(var i=0;i<length;++i){HEAPU8[ptr+i]=value[i]}}}if(destructors!==null){destructors.push(_free,base)}return base},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":readPointer,destructorFunction(ptr){_free(ptr)}})};var UTF16Decoder=typeof TextDecoder!=\"undefined\"?new TextDecoder(\"utf-16le\"):undefined;var UTF16ToString=(ptr,maxBytesToRead)=>{var endPtr=ptr;var idx=endPtr>>1;var maxIdx=idx+maxBytesToRead/2;while(!(idx>=maxIdx)&&HEAPU16[idx])++idx;endPtr=idx<<1;if(endPtr-ptr>32&&UTF16Decoder)return UTF16Decoder.decode(HEAPU8.subarray(ptr,endPtr));var str=\"\";for(var i=0;!(i>=maxBytesToRead/2);++i){var codeUnit=HEAP16[ptr+i*2>>1];if(codeUnit==0)break;str+=String.fromCharCode(codeUnit)}return str};var stringToUTF16=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<2)return 0;maxBytesToWrite-=2;var startPtr=outPtr;var numCharsToWrite=maxBytesToWrite<str.length*2?maxBytesToWrite/2:str.length;for(var i=0;i<numCharsToWrite;++i){var codeUnit=str.charCodeAt(i);HEAP16[outPtr>>1]=codeUnit;outPtr+=2}HEAP16[outPtr>>1]=0;return outPtr-startPtr};var lengthBytesUTF16=str=>str.length*2;var UTF32ToString=(ptr,maxBytesToRead)=>{var i=0;var str=\"\";while(!(i>=maxBytesToRead/4)){var utf32=HEAP32[ptr+i*4>>2];if(utf32==0)break;++i;if(utf32>=65536){var ch=utf32-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}else{str+=String.fromCharCode(utf32)}}return str};var stringToUTF32=(str,outPtr,maxBytesToWrite)=>{if(maxBytesToWrite===undefined){maxBytesToWrite=2147483647}if(maxBytesToWrite<4)return 0;var startPtr=outPtr;var endPtr=startPtr+maxBytesToWrite-4;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343){var trailSurrogate=str.charCodeAt(++i);codeUnit=65536+((codeUnit&1023)<<10)|trailSurrogate&1023}HEAP32[outPtr>>2]=codeUnit;outPtr+=4;if(outPtr+4>endPtr)break}HEAP32[outPtr>>2]=0;return outPtr-startPtr};var lengthBytesUTF32=str=>{var len=0;for(var i=0;i<str.length;++i){var codeUnit=str.charCodeAt(i);if(codeUnit>=55296&&codeUnit<=57343)++i;len+=4}return len};var __embind_register_std_wstring=(rawType,charSize,name)=>{name=readLatin1String(name);var decodeString,encodeString,getHeap,lengthBytesUTF,shift;if(charSize===2){decodeString=UTF16ToString;encodeString=stringToUTF16;lengthBytesUTF=lengthBytesUTF16;getHeap=()=>HEAPU16;shift=1}else if(charSize===4){decodeString=UTF32ToString;encodeString=stringToUTF32;lengthBytesUTF=lengthBytesUTF32;getHeap=()=>HEAPU32;shift=2}registerType(rawType,{name:name,\"fromWireType\":value=>{var length=HEAPU32[value>>2];var HEAP=getHeap();var str;var decodeStartPtr=value+4;for(var i=0;i<=length;++i){var currentBytePtr=value+4+i*charSize;if(i==length||HEAP[currentBytePtr>>shift]==0){var maxReadBytes=currentBytePtr-decodeStartPtr;var stringSegment=decodeString(decodeStartPtr,maxReadBytes);if(str===undefined){str=stringSegment}else{str+=String.fromCharCode(0);str+=stringSegment}decodeStartPtr=currentBytePtr+charSize}}_free(value);return str},\"toWireType\":(destructors,value)=>{if(!(typeof value==\"string\")){throwBindingError(`Cannot pass non-string to C++ string type ${name}`)}var length=lengthBytesUTF(value);var ptr=_malloc(4+length+charSize);HEAPU32[ptr>>2]=length>>shift;encodeString(value,ptr+4,length+charSize);if(destructors!==null){destructors.push(_free,ptr)}return ptr},\"argPackAdvance\":GenericWireTypeSize,\"readValueFromPointer\":simpleReadValueFromPointer,destructorFunction(ptr){_free(ptr)}})};var __embind_register_value_array=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{tupleRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),elements:[]}};var __embind_register_value_array_element=(rawTupleType,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{tupleRegistrations[rawTupleType].elements.push({getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_value_object=(rawType,name,constructorSignature,rawConstructor,destructorSignature,rawDestructor)=>{structRegistrations[rawType]={name:readLatin1String(name),rawConstructor:embind__requireFunction(constructorSignature,rawConstructor),rawDestructor:embind__requireFunction(destructorSignature,rawDestructor),fields:[]}};var __embind_register_value_object_field=(structType,fieldName,getterReturnType,getterSignature,getter,getterContext,setterArgumentType,setterSignature,setter,setterContext)=>{structRegistrations[structType].fields.push({fieldName:readLatin1String(fieldName),getterReturnType:getterReturnType,getter:embind__requireFunction(getterSignature,getter),getterContext:getterContext,setterArgumentType:setterArgumentType,setter:embind__requireFunction(setterSignature,setter),setterContext:setterContext})};var __embind_register_void=(rawType,name)=>{name=readLatin1String(name);registerType(rawType,{isVoid:true,name:name,\"argPackAdvance\":0,\"fromWireType\":()=>undefined,\"toWireType\":(destructors,o)=>undefined})};var __emval_incref=handle=>{if(handle>4){emval_handles.get(handle).refcount+=1}};var __emval_take_value=(type,arg)=>{type=requireRegisteredType(type,\"_emval_take_value\");var v=type[\"readValueFromPointer\"](arg);return Emval.toHandle(v)};var _abort=()=>{abort(\"\")};var _emscripten_memcpy_js=(dest,src,num)=>HEAPU8.copyWithin(dest,src,src+num);var getHeapMax=()=>2147483648;var growMemory=size=>{var b=wasmMemory.buffer;var pages=(size-b.byteLength+65535)/65536;try{wasmMemory.grow(pages);updateMemoryViews();return 1}catch(e){}};var _emscripten_resize_heap=requestedSize=>{var oldSize=HEAPU8.length;requestedSize>>>=0;var maxHeapSize=getHeapMax();if(requestedSize>maxHeapSize){return false}var alignUp=(x,multiple)=>x+(multiple-x%multiple)%multiple;for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=growMemory(newSize);if(replacement){return true}}return false};var ENV={};var getExecutableName=()=>thisProgram||\"./this.program\";var getEnvStrings=()=>{if(!getEnvStrings.strings){var lang=(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(`${x}=${env[x]}`)}getEnvStrings.strings=strings}return getEnvStrings.strings};var stringToAscii=(str,buffer)=>{for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}HEAP8[buffer>>0]=0};var PATH={isAbs:path=>path.charAt(0)===\"/\",splitPath:filename=>{var splitPathRe=/^(\\/?|)([\\s\\S]*?)((?:\\.{1,2}|[^\\/]+?|)(\\.[^.\\/]*|))(?:[\\/]*)$/;return splitPathRe.exec(filename).slice(1)},normalizeArray:(parts,allowAboveRoot)=>{var up=0;for(var i=parts.length-1;i>=0;i--){var last=parts[i];if(last===\".\"){parts.splice(i,1)}else if(last===\"..\"){parts.splice(i,1);up++}else if(up){parts.splice(i,1);up--}}if(allowAboveRoot){for(;up;up--){parts.unshift(\"..\")}}return parts},normalize:path=>{var isAbsolute=PATH.isAbs(path),trailingSlash=path.substr(-1)===\"/\";path=PATH.normalizeArray(path.split(\"/\").filter(p=>!!p),!isAbsolute).join(\"/\");if(!path&&!isAbsolute){path=\".\"}if(path&&trailingSlash){path+=\"/\"}return(isAbsolute?\"/\":\"\")+path},dirname:path=>{var result=PATH.splitPath(path),root=result[0],dir=result[1];if(!root&&!dir){return\".\"}if(dir){dir=dir.substr(0,dir.length-1)}return root+dir},basename:path=>{if(path===\"/\")return\"/\";path=PATH.normalize(path);path=path.replace(/\\/$/,\"\");var lastSlash=path.lastIndexOf(\"/\");if(lastSlash===-1)return path;return path.substr(lastSlash+1)},join:function(){var paths=Array.prototype.slice.call(arguments);return PATH.normalize(paths.join(\"/\"))},join2:(l,r)=>PATH.normalize(l+\"/\"+r)};var initRandomFill=()=>{if(typeof crypto==\"object\"&&typeof crypto[\"getRandomValues\"]==\"function\"){return view=>crypto.getRandomValues(view)}else abort(\"initRandomDevice\")};var randomFill=view=>(randomFill=initRandomFill())(view);var PATH_FS={resolve:function(){var resolvedPath=\"\",resolvedAbsolute=false;for(var i=arguments.length-1;i>=-1&&!resolvedAbsolute;i--){var path=i>=0?arguments[i]:FS.cwd();if(typeof path!=\"string\"){throw new TypeError(\"Arguments to path.resolve must be strings\")}else if(!path){return\"\"}resolvedPath=path+\"/\"+resolvedPath;resolvedAbsolute=PATH.isAbs(path)}resolvedPath=PATH.normalizeArray(resolvedPath.split(\"/\").filter(p=>!!p),!resolvedAbsolute).join(\"/\");return(resolvedAbsolute?\"/\":\"\")+resolvedPath||\".\"},relative:(from,to)=>{from=PATH_FS.resolve(from).substr(1);to=PATH_FS.resolve(to).substr(1);function trim(arr){var start=0;for(;start<arr.length;start++){if(arr[start]!==\"\")break}var end=arr.length-1;for(;end>=0;end--){if(arr[end]!==\"\")break}if(start>end)return[];return arr.slice(start,end-start+1)}var fromParts=trim(from.split(\"/\"));var toParts=trim(to.split(\"/\"));var length=Math.min(fromParts.length,toParts.length);var samePartsLength=length;for(var i=0;i<length;i++){if(fromParts[i]!==toParts[i]){samePartsLength=i;break}}var outputParts=[];for(var i=samePartsLength;i<fromParts.length;i++){outputParts.push(\"..\")}outputParts=outputParts.concat(toParts.slice(samePartsLength));return outputParts.join(\"/\")}};var FS_stdin_getChar_buffer=[];function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var FS_stdin_getChar=()=>{if(!FS_stdin_getChar_buffer.length){var result=null;if(typeof window!=\"undefined\"&&typeof window.prompt==\"function\"){result=window.prompt(\"Input: \");if(result!==null){result+=\"\\n\"}}else if(typeof readline==\"function\"){result=readline();if(result!==null){result+=\"\\n\"}}if(!result){return null}FS_stdin_getChar_buffer=intArrayFromString(result,true)}return FS_stdin_getChar_buffer.shift()};var TTY={ttys:[],init(){},shutdown(){},register(dev,ops){TTY.ttys[dev]={input:[],output:[],ops:ops};FS.registerDevice(dev,TTY.stream_ops)},stream_ops:{open(stream){var tty=TTY.ttys[stream.node.rdev];if(!tty){throw new FS.ErrnoError(43)}stream.tty=tty;stream.seekable=false},close(stream){stream.tty.ops.fsync(stream.tty)},fsync(stream){stream.tty.ops.fsync(stream.tty)},read(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.get_char){throw new FS.ErrnoError(60)}var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=stream.tty.ops.get_char(stream.tty)}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){if(!stream.tty||!stream.tty.ops.put_char){throw new FS.ErrnoError(60)}try{for(var i=0;i<length;i++){stream.tty.ops.put_char(stream.tty,buffer[offset+i])}}catch(e){throw new FS.ErrnoError(29)}if(length){stream.node.timestamp=Date.now()}return i}},default_tty_ops:{get_char(tty){return FS_stdin_getChar()},put_char(tty,val){if(val===null||val===10){out(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){out(UTF8ArrayToString(tty.output,0));tty.output=[]}},ioctl_tcgets(tty){return{c_iflag:25856,c_oflag:5,c_cflag:191,c_lflag:35387,c_cc:[3,28,127,21,4,0,1,0,17,19,26,0,18,15,23,22,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0]}},ioctl_tcsets(tty,optional_actions,data){return 0},ioctl_tiocgwinsz(tty){return[24,80]}},default_tty1_ops:{put_char(tty,val){if(val===null||val===10){err(UTF8ArrayToString(tty.output,0));tty.output=[]}else{if(val!=0)tty.output.push(val)}},fsync(tty){if(tty.output&&tty.output.length>0){err(UTF8ArrayToString(tty.output,0));tty.output=[]}}}};var mmapAlloc=size=>{abort()};var MEMFS={ops_table:null,mount(mount){return MEMFS.createNode(null,\"/\",16384|511,0)},createNode(parent,name,mode,dev){if(FS.isBlkdev(mode)||FS.isFIFO(mode)){throw new FS.ErrnoError(63)}if(!MEMFS.ops_table){MEMFS.ops_table={dir:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,lookup:MEMFS.node_ops.lookup,mknod:MEMFS.node_ops.mknod,rename:MEMFS.node_ops.rename,unlink:MEMFS.node_ops.unlink,rmdir:MEMFS.node_ops.rmdir,readdir:MEMFS.node_ops.readdir,symlink:MEMFS.node_ops.symlink},stream:{llseek:MEMFS.stream_ops.llseek}},file:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:{llseek:MEMFS.stream_ops.llseek,read:MEMFS.stream_ops.read,write:MEMFS.stream_ops.write,allocate:MEMFS.stream_ops.allocate,mmap:MEMFS.stream_ops.mmap,msync:MEMFS.stream_ops.msync}},link:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr,readlink:MEMFS.node_ops.readlink},stream:{}},chrdev:{node:{getattr:MEMFS.node_ops.getattr,setattr:MEMFS.node_ops.setattr},stream:FS.chrdev_stream_ops}}}var node=FS.createNode(parent,name,mode,dev);if(FS.isDir(node.mode)){node.node_ops=MEMFS.ops_table.dir.node;node.stream_ops=MEMFS.ops_table.dir.stream;node.contents={}}else if(FS.isFile(node.mode)){node.node_ops=MEMFS.ops_table.file.node;node.stream_ops=MEMFS.ops_table.file.stream;node.usedBytes=0;node.contents=null}else if(FS.isLink(node.mode)){node.node_ops=MEMFS.ops_table.link.node;node.stream_ops=MEMFS.ops_table.link.stream}else if(FS.isChrdev(node.mode)){node.node_ops=MEMFS.ops_table.chrdev.node;node.stream_ops=MEMFS.ops_table.chrdev.stream}node.timestamp=Date.now();if(parent){parent.contents[name]=node;parent.timestamp=node.timestamp}return node},getFileDataAsTypedArray(node){if(!node.contents)return new Uint8Array(0);if(node.contents.subarray)return node.contents.subarray(0,node.usedBytes);return new Uint8Array(node.contents)},expandFileStorage(node,newCapacity){var prevCapacity=node.contents?node.contents.length:0;if(prevCapacity>=newCapacity)return;var CAPACITY_DOUBLING_MAX=1024*1024;newCapacity=Math.max(newCapacity,prevCapacity*(prevCapacity<CAPACITY_DOUBLING_MAX?2:1.125)>>>0);if(prevCapacity!=0)newCapacity=Math.max(newCapacity,256);var oldContents=node.contents;node.contents=new Uint8Array(newCapacity);if(node.usedBytes>0)node.contents.set(oldContents.subarray(0,node.usedBytes),0)},resizeFileStorage(node,newSize){if(node.usedBytes==newSize)return;if(newSize==0){node.contents=null;node.usedBytes=0}else{var oldContents=node.contents;node.contents=new Uint8Array(newSize);if(oldContents){node.contents.set(oldContents.subarray(0,Math.min(newSize,node.usedBytes)))}node.usedBytes=newSize}},node_ops:{getattr(node){var attr={};attr.dev=FS.isChrdev(node.mode)?node.id:1;attr.ino=node.id;attr.mode=node.mode;attr.nlink=1;attr.uid=0;attr.gid=0;attr.rdev=node.rdev;if(FS.isDir(node.mode)){attr.size=4096}else if(FS.isFile(node.mode)){attr.size=node.usedBytes}else if(FS.isLink(node.mode)){attr.size=node.link.length}else{attr.size=0}attr.atime=new Date(node.timestamp);attr.mtime=new Date(node.timestamp);attr.ctime=new Date(node.timestamp);attr.blksize=4096;attr.blocks=Math.ceil(attr.size/attr.blksize);return attr},setattr(node,attr){if(attr.mode!==undefined){node.mode=attr.mode}if(attr.timestamp!==undefined){node.timestamp=attr.timestamp}if(attr.size!==undefined){MEMFS.resizeFileStorage(node,attr.size)}},lookup(parent,name){throw FS.genericErrors[44]},mknod(parent,name,mode,dev){return MEMFS.createNode(parent,name,mode,dev)},rename(old_node,new_dir,new_name){if(FS.isDir(old_node.mode)){var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(new_node){for(var i in new_node.contents){throw new FS.ErrnoError(55)}}}delete old_node.parent.contents[old_node.name];old_node.parent.timestamp=Date.now();old_node.name=new_name;new_dir.contents[new_name]=old_node;new_dir.timestamp=old_node.parent.timestamp;old_node.parent=new_dir},unlink(parent,name){delete parent.contents[name];parent.timestamp=Date.now()},rmdir(parent,name){var node=FS.lookupNode(parent,name);for(var i in node.contents){throw new FS.ErrnoError(55)}delete parent.contents[name];parent.timestamp=Date.now()},readdir(node){var entries=[\".\",\"..\"];for(var key in node.contents){if(!node.contents.hasOwnProperty(key)){continue}entries.push(key)}return entries},symlink(parent,newname,oldpath){var node=MEMFS.createNode(parent,newname,511|40960,0);node.link=oldpath;return node},readlink(node){if(!FS.isLink(node.mode)){throw new FS.ErrnoError(28)}return node.link}},stream_ops:{read(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=stream.node.usedBytes)return 0;var size=Math.min(stream.node.usedBytes-position,length);if(size>8&&contents.subarray){buffer.set(contents.subarray(position,position+size),offset)}else{for(var i=0;i<size;i++)buffer[offset+i]=contents[position+i]}return size},write(stream,buffer,offset,length,position,canOwn){if(buffer.buffer===HEAP8.buffer){canOwn=false}if(!length)return 0;var node=stream.node;node.timestamp=Date.now();if(buffer.subarray&&(!node.contents||node.contents.subarray)){if(canOwn){node.contents=buffer.subarray(offset,offset+length);node.usedBytes=length;return length}else if(node.usedBytes===0&&position===0){node.contents=buffer.slice(offset,offset+length);node.usedBytes=length;return length}else if(position+length<=node.usedBytes){node.contents.set(buffer.subarray(offset,offset+length),position);return length}}MEMFS.expandFileStorage(node,position+length);if(node.contents.subarray&&buffer.subarray){node.contents.set(buffer.subarray(offset,offset+length),position)}else{for(var i=0;i<length;i++){node.contents[position+i]=buffer[offset+i]}}node.usedBytes=Math.max(node.usedBytes,position+length);return length},llseek(stream,offset,whence){var position=offset;if(whence===1){position+=stream.position}else if(whence===2){if(FS.isFile(stream.node.mode)){position+=stream.node.usedBytes}}if(position<0){throw new FS.ErrnoError(28)}return position},allocate(stream,offset,length){MEMFS.expandFileStorage(stream.node,offset+length);stream.node.usedBytes=Math.max(stream.node.usedBytes,offset+length)},mmap(stream,length,position,prot,flags){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}var ptr;var allocated;var contents=stream.node.contents;if(!(flags&2)&&contents.buffer===HEAP8.buffer){allocated=false;ptr=contents.byteOffset}else{if(position>0||position+length<contents.length){if(contents.subarray){contents=contents.subarray(position,position+length)}else{contents=Array.prototype.slice.call(contents,position,position+length)}}allocated=true;ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}HEAP8.set(contents,ptr)}return{ptr:ptr,allocated:allocated}},msync(stream,buffer,offset,length,mmapFlags){MEMFS.stream_ops.write(stream,buffer,0,length,offset,false);return 0}}};var asyncLoad=(url,onload,onerror,noRunDep)=>{var dep=!noRunDep?getUniqueRunDependency(`al ${url}`):\"\";readAsync(url,arrayBuffer=>{assert(arrayBuffer,`Loading data file \"${url}\" failed (no arrayBuffer).`);onload(new Uint8Array(arrayBuffer));if(dep)removeRunDependency(dep)},event=>{if(onerror){onerror()}else{throw`Loading data file \"${url}\" failed.`}});if(dep)addRunDependency(dep)};var FS_createDataFile=(parent,name,fileData,canRead,canWrite,canOwn)=>FS.createDataFile(parent,name,fileData,canRead,canWrite,canOwn);var preloadPlugins=Module[\"preloadPlugins\"]||[];var FS_handledByPreloadPlugin=(byteArray,fullname,finish,onerror)=>{if(typeof Browser!=\"undefined\")Browser.init();var handled=false;preloadPlugins.forEach(plugin=>{if(handled)return;if(plugin[\"canHandle\"](fullname)){plugin[\"handle\"](byteArray,fullname,finish,onerror);handled=true}});return handled};var FS_createPreloadedFile=(parent,name,url,canRead,canWrite,onload,onerror,dontCreateFile,canOwn,preFinish)=>{var fullname=name?PATH_FS.resolve(PATH.join2(parent,name)):parent;var dep=getUniqueRunDependency(`cp ${fullname}`);function processData(byteArray){function finish(byteArray){if(preFinish)preFinish();if(!dontCreateFile){FS_createDataFile(parent,name,byteArray,canRead,canWrite,canOwn)}if(onload)onload();removeRunDependency(dep)}if(FS_handledByPreloadPlugin(byteArray,fullname,finish,()=>{if(onerror)onerror();removeRunDependency(dep)})){return}finish(byteArray)}addRunDependency(dep);if(typeof url==\"string\"){asyncLoad(url,byteArray=>processData(byteArray),onerror)}else{processData(url)}};var FS_modeStringToFlags=str=>{var flagModes={\"r\":0,\"r+\":2,\"w\":512|64|1,\"w+\":512|64|2,\"a\":1024|64|1,\"a+\":1024|64|2};var flags=flagModes[str];if(typeof flags==\"undefined\"){throw new Error(`Unknown file open mode: ${str}`)}return flags};var FS_getMode=(canRead,canWrite)=>{var mode=0;if(canRead)mode|=292|73;if(canWrite)mode|=146;return mode};var FS={root:null,mounts:[],devices:{},streams:[],nextInode:1,nameTable:null,currentPath:\"/\",initialized:false,ignorePermissions:true,ErrnoError:null,genericErrors:{},filesystems:null,syncFSRequests:0,lookupPath(path,opts={}){path=PATH_FS.resolve(path);if(!path)return{path:\"\",node:null};var defaults={follow_mount:true,recurse_count:0};opts=Object.assign(defaults,opts);if(opts.recurse_count>8){throw new FS.ErrnoError(32)}var parts=path.split(\"/\").filter(p=>!!p);var current=FS.root;var current_path=\"/\";for(var i=0;i<parts.length;i++){var islast=i===parts.length-1;if(islast&&opts.parent){break}current=FS.lookupNode(current,parts[i]);current_path=PATH.join2(current_path,parts[i]);if(FS.isMountpoint(current)){if(!islast||islast&&opts.follow_mount){current=current.mounted.root}}if(!islast||opts.follow){var count=0;while(FS.isLink(current.mode)){var link=FS.readlink(current_path);current_path=PATH_FS.resolve(PATH.dirname(current_path),link);var lookup=FS.lookupPath(current_path,{recurse_count:opts.recurse_count+1});current=lookup.node;if(count++>40){throw new FS.ErrnoError(32)}}}}return{path:current_path,node:current}},getPath(node){var path;while(true){if(FS.isRoot(node)){var mount=node.mount.mountpoint;if(!path)return mount;return mount[mount.length-1]!==\"/\"?`${mount}/${path}`:mount+path}path=path?`${node.name}/${path}`:node.name;node=node.parent}},hashName(parentid,name){var hash=0;for(var i=0;i<name.length;i++){hash=(hash<<5)-hash+name.charCodeAt(i)|0}return(parentid+hash>>>0)%FS.nameTable.length},hashAddNode(node){var hash=FS.hashName(node.parent.id,node.name);node.name_next=FS.nameTable[hash];FS.nameTable[hash]=node},hashRemoveNode(node){var hash=FS.hashName(node.parent.id,node.name);if(FS.nameTable[hash]===node){FS.nameTable[hash]=node.name_next}else{var current=FS.nameTable[hash];while(current){if(current.name_next===node){current.name_next=node.name_next;break}current=current.name_next}}},lookupNode(parent,name){var errCode=FS.mayLookup(parent);if(errCode){throw new FS.ErrnoError(errCode,parent)}var hash=FS.hashName(parent.id,name);for(var node=FS.nameTable[hash];node;node=node.name_next){var nodeName=node.name;if(node.parent.id===parent.id&&nodeName===name){return node}}return FS.lookup(parent,name)},createNode(parent,name,mode,rdev){var node=new FS.FSNode(parent,name,mode,rdev);FS.hashAddNode(node);return node},destroyNode(node){FS.hashRemoveNode(node)},isRoot(node){return node===node.parent},isMountpoint(node){return!!node.mounted},isFile(mode){return(mode&61440)===32768},isDir(mode){return(mode&61440)===16384},isLink(mode){return(mode&61440)===40960},isChrdev(mode){return(mode&61440)===8192},isBlkdev(mode){return(mode&61440)===24576},isFIFO(mode){return(mode&61440)===4096},isSocket(mode){return(mode&49152)===49152},flagsToPermissionString(flag){var perms=[\"r\",\"w\",\"rw\"][flag&3];if(flag&512){perms+=\"w\"}return perms},nodePermissions(node,perms){if(FS.ignorePermissions){return 0}if(perms.includes(\"r\")&&!(node.mode&292)){return 2}else if(perms.includes(\"w\")&&!(node.mode&146)){return 2}else if(perms.includes(\"x\")&&!(node.mode&73)){return 2}return 0},mayLookup(dir){var errCode=FS.nodePermissions(dir,\"x\");if(errCode)return errCode;if(!dir.node_ops.lookup)return 2;return 0},mayCreate(dir,name){try{var node=FS.lookupNode(dir,name);return 20}catch(e){}return FS.nodePermissions(dir,\"wx\")},mayDelete(dir,name,isdir){var node;try{node=FS.lookupNode(dir,name)}catch(e){return e.errno}var errCode=FS.nodePermissions(dir,\"wx\");if(errCode){return errCode}if(isdir){if(!FS.isDir(node.mode)){return 54}if(FS.isRoot(node)||FS.getPath(node)===FS.cwd()){return 10}}else{if(FS.isDir(node.mode)){return 31}}return 0},mayOpen(node,flags){if(!node){return 44}if(FS.isLink(node.mode)){return 32}else if(FS.isDir(node.mode)){if(FS.flagsToPermissionString(flags)!==\"r\"||flags&512){return 31}}return FS.nodePermissions(node,FS.flagsToPermissionString(flags))},MAX_OPEN_FDS:4096,nextfd(){for(var fd=0;fd<=FS.MAX_OPEN_FDS;fd++){if(!FS.streams[fd]){return fd}}throw new FS.ErrnoError(33)},getStreamChecked(fd){var stream=FS.getStream(fd);if(!stream){throw new FS.ErrnoError(8)}return stream},getStream:fd=>FS.streams[fd],createStream(stream,fd=-1){if(!FS.FSStream){FS.FSStream=function(){this.shared={}};FS.FSStream.prototype={};Object.defineProperties(FS.FSStream.prototype,{object:{get(){return this.node},set(val){this.node=val}},isRead:{get(){return(this.flags&2097155)!==1}},isWrite:{get(){return(this.flags&2097155)!==0}},isAppend:{get(){return this.flags&1024}},flags:{get(){return this.shared.flags},set(val){this.shared.flags=val}},position:{get(){return this.shared.position},set(val){this.shared.position=val}}})}stream=Object.assign(new FS.FSStream,stream);if(fd==-1){fd=FS.nextfd()}stream.fd=fd;FS.streams[fd]=stream;return stream},closeStream(fd){FS.streams[fd]=null},chrdev_stream_ops:{open(stream){var device=FS.getDevice(stream.node.rdev);stream.stream_ops=device.stream_ops;if(stream.stream_ops.open){stream.stream_ops.open(stream)}},llseek(){throw new FS.ErrnoError(70)}},major:dev=>dev>>8,minor:dev=>dev&255,makedev:(ma,mi)=>ma<<8|mi,registerDevice(dev,ops){FS.devices[dev]={stream_ops:ops}},getDevice:dev=>FS.devices[dev],getMounts(mount){var mounts=[];var check=[mount];while(check.length){var m=check.pop();mounts.push(m);check.push.apply(check,m.mounts)}return mounts},syncfs(populate,callback){if(typeof populate==\"function\"){callback=populate;populate=false}FS.syncFSRequests++;if(FS.syncFSRequests>1){err(`warning: ${FS.syncFSRequests} FS.syncfs operations in flight at once, probably just doing extra work`)}var mounts=FS.getMounts(FS.root.mount);var completed=0;function doCallback(errCode){FS.syncFSRequests--;return callback(errCode)}function done(errCode){if(errCode){if(!done.errored){done.errored=true;return doCallback(errCode)}return}if(++completed>=mounts.length){doCallback(null)}}mounts.forEach(mount=>{if(!mount.type.syncfs){return done(null)}mount.type.syncfs(mount,populate,done)})},mount(type,opts,mountpoint){var root=mountpoint===\"/\";var pseudo=!mountpoint;var node;if(root&&FS.root){throw new FS.ErrnoError(10)}else if(!root&&!pseudo){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});mountpoint=lookup.path;node=lookup.node;if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}if(!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}}var mount={type:type,opts:opts,mountpoint:mountpoint,mounts:[]};var mountRoot=type.mount(mount);mountRoot.mount=mount;mount.root=mountRoot;if(root){FS.root=mountRoot}else if(node){node.mounted=mount;if(node.mount){node.mount.mounts.push(mount)}}return mountRoot},unmount(mountpoint){var lookup=FS.lookupPath(mountpoint,{follow_mount:false});if(!FS.isMountpoint(lookup.node)){throw new FS.ErrnoError(28)}var node=lookup.node;var mount=node.mounted;var mounts=FS.getMounts(mount);Object.keys(FS.nameTable).forEach(hash=>{var current=FS.nameTable[hash];while(current){var next=current.name_next;if(mounts.includes(current.mount)){FS.destroyNode(current)}current=next}});node.mounted=null;var idx=node.mount.mounts.indexOf(mount);node.mount.mounts.splice(idx,1)},lookup(parent,name){return parent.node_ops.lookup(parent,name)},mknod(path,mode,dev){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);if(!name||name===\".\"||name===\"..\"){throw new FS.ErrnoError(28)}var errCode=FS.mayCreate(parent,name);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.mknod){throw new FS.ErrnoError(63)}return parent.node_ops.mknod(parent,name,mode,dev)},create(path,mode){mode=mode!==undefined?mode:438;mode&=4095;mode|=32768;return FS.mknod(path,mode,0)},mkdir(path,mode){mode=mode!==undefined?mode:511;mode&=511|512;mode|=16384;return FS.mknod(path,mode,0)},mkdirTree(path,mode){var dirs=path.split(\"/\");var d=\"\";for(var i=0;i<dirs.length;++i){if(!dirs[i])continue;d+=\"/\"+dirs[i];try{FS.mkdir(d,mode)}catch(e){if(e.errno!=20)throw e}}},mkdev(path,mode,dev){if(typeof dev==\"undefined\"){dev=mode;mode=438}mode|=8192;return FS.mknod(path,mode,dev)},symlink(oldpath,newpath){if(!PATH_FS.resolve(oldpath)){throw new FS.ErrnoError(44)}var lookup=FS.lookupPath(newpath,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var newname=PATH.basename(newpath);var errCode=FS.mayCreate(parent,newname);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.symlink){throw new FS.ErrnoError(63)}return parent.node_ops.symlink(parent,newname,oldpath)},rename(old_path,new_path){var old_dirname=PATH.dirname(old_path);var new_dirname=PATH.dirname(new_path);var old_name=PATH.basename(old_path);var new_name=PATH.basename(new_path);var lookup,old_dir,new_dir;lookup=FS.lookupPath(old_path,{parent:true});old_dir=lookup.node;lookup=FS.lookupPath(new_path,{parent:true});new_dir=lookup.node;if(!old_dir||!new_dir)throw new FS.ErrnoError(44);if(old_dir.mount!==new_dir.mount){throw new FS.ErrnoError(75)}var old_node=FS.lookupNode(old_dir,old_name);var relative=PATH_FS.relative(old_path,new_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(28)}relative=PATH_FS.relative(new_path,old_dirname);if(relative.charAt(0)!==\".\"){throw new FS.ErrnoError(55)}var new_node;try{new_node=FS.lookupNode(new_dir,new_name)}catch(e){}if(old_node===new_node){return}var isdir=FS.isDir(old_node.mode);var errCode=FS.mayDelete(old_dir,old_name,isdir);if(errCode){throw new FS.ErrnoError(errCode)}errCode=new_node?FS.mayDelete(new_dir,new_name,isdir):FS.mayCreate(new_dir,new_name);if(errCode){throw new FS.ErrnoError(errCode)}if(!old_dir.node_ops.rename){throw new FS.ErrnoError(63)}if(FS.isMountpoint(old_node)||new_node&&FS.isMountpoint(new_node)){throw new FS.ErrnoError(10)}if(new_dir!==old_dir){errCode=FS.nodePermissions(old_dir,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}}FS.hashRemoveNode(old_node);try{old_dir.node_ops.rename(old_node,new_dir,new_name)}catch(e){throw e}finally{FS.hashAddNode(old_node)}},rmdir(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,true);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.rmdir){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.rmdir(parent,name);FS.destroyNode(node)},readdir(path){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;if(!node.node_ops.readdir){throw new FS.ErrnoError(54)}return node.node_ops.readdir(node)},unlink(path){var lookup=FS.lookupPath(path,{parent:true});var parent=lookup.node;if(!parent){throw new FS.ErrnoError(44)}var name=PATH.basename(path);var node=FS.lookupNode(parent,name);var errCode=FS.mayDelete(parent,name,false);if(errCode){throw new FS.ErrnoError(errCode)}if(!parent.node_ops.unlink){throw new FS.ErrnoError(63)}if(FS.isMountpoint(node)){throw new FS.ErrnoError(10)}parent.node_ops.unlink(parent,name);FS.destroyNode(node)},readlink(path){var lookup=FS.lookupPath(path);var link=lookup.node;if(!link){throw new FS.ErrnoError(44)}if(!link.node_ops.readlink){throw new FS.ErrnoError(28)}return PATH_FS.resolve(FS.getPath(link.parent),link.node_ops.readlink(link))},stat(path,dontFollow){var lookup=FS.lookupPath(path,{follow:!dontFollow});var node=lookup.node;if(!node){throw new FS.ErrnoError(44)}if(!node.node_ops.getattr){throw new FS.ErrnoError(63)}return node.node_ops.getattr(node)},lstat(path){return FS.stat(path,true)},chmod(path,mode,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{mode:mode&4095|node.mode&~4095,timestamp:Date.now()})},lchmod(path,mode){FS.chmod(path,mode,true)},fchmod(fd,mode){var stream=FS.getStreamChecked(fd);FS.chmod(stream.node,mode)},chown(path,uid,gid,dontFollow){var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:!dontFollow});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}node.node_ops.setattr(node,{timestamp:Date.now()})},lchown(path,uid,gid){FS.chown(path,uid,gid,true)},fchown(fd,uid,gid){var stream=FS.getStreamChecked(fd);FS.chown(stream.node,uid,gid)},truncate(path,len){if(len<0){throw new FS.ErrnoError(28)}var node;if(typeof path==\"string\"){var lookup=FS.lookupPath(path,{follow:true});node=lookup.node}else{node=path}if(!node.node_ops.setattr){throw new FS.ErrnoError(63)}if(FS.isDir(node.mode)){throw new FS.ErrnoError(31)}if(!FS.isFile(node.mode)){throw new FS.ErrnoError(28)}var errCode=FS.nodePermissions(node,\"w\");if(errCode){throw new FS.ErrnoError(errCode)}node.node_ops.setattr(node,{size:len,timestamp:Date.now()})},ftruncate(fd,len){var stream=FS.getStreamChecked(fd);if((stream.flags&2097155)===0){throw new FS.ErrnoError(28)}FS.truncate(stream.node,len)},utime(path,atime,mtime){var lookup=FS.lookupPath(path,{follow:true});var node=lookup.node;node.node_ops.setattr(node,{timestamp:Math.max(atime,mtime)})},open(path,flags,mode){if(path===\"\"){throw new FS.ErrnoError(44)}flags=typeof flags==\"string\"?FS_modeStringToFlags(flags):flags;mode=typeof mode==\"undefined\"?438:mode;if(flags&64){mode=mode&4095|32768}else{mode=0}var node;if(typeof path==\"object\"){node=path}else{path=PATH.normalize(path);try{var lookup=FS.lookupPath(path,{follow:!(flags&131072)});node=lookup.node}catch(e){}}var created=false;if(flags&64){if(node){if(flags&128){throw new FS.ErrnoError(20)}}else{node=FS.mknod(path,mode,0);created=true}}if(!node){throw new FS.ErrnoError(44)}if(FS.isChrdev(node.mode)){flags&=~512}if(flags&65536&&!FS.isDir(node.mode)){throw new FS.ErrnoError(54)}if(!created){var errCode=FS.mayOpen(node,flags);if(errCode){throw new FS.ErrnoError(errCode)}}if(flags&512&&!created){FS.truncate(node,0)}flags&=~(128|512|131072);var stream=FS.createStream({node:node,path:FS.getPath(node),flags:flags,seekable:true,position:0,stream_ops:node.stream_ops,ungotten:[],error:false});if(stream.stream_ops.open){stream.stream_ops.open(stream)}if(Module[\"logReadFiles\"]&&!(flags&1)){if(!FS.readFiles)FS.readFiles={};if(!(path in FS.readFiles)){FS.readFiles[path]=1}}return stream},close(stream){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(stream.getdents)stream.getdents=null;try{if(stream.stream_ops.close){stream.stream_ops.close(stream)}}catch(e){throw e}finally{FS.closeStream(stream.fd)}stream.fd=null},isClosed(stream){return stream.fd===null},llseek(stream,offset,whence){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(!stream.seekable||!stream.stream_ops.llseek){throw new FS.ErrnoError(70)}if(whence!=0&&whence!=1&&whence!=2){throw new FS.ErrnoError(28)}stream.position=stream.stream_ops.llseek(stream,offset,whence);stream.ungotten=[];return stream.position},read(stream,buffer,offset,length,position){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.read){throw new FS.ErrnoError(28)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesRead=stream.stream_ops.read(stream,buffer,offset,length,position);if(!seeking)stream.position+=bytesRead;return bytesRead},write(stream,buffer,offset,length,position,canOwn){if(length<0||position<0){throw new FS.ErrnoError(28)}if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(FS.isDir(stream.node.mode)){throw new FS.ErrnoError(31)}if(!stream.stream_ops.write){throw new FS.ErrnoError(28)}if(stream.seekable&&stream.flags&1024){FS.llseek(stream,0,2)}var seeking=typeof position!=\"undefined\";if(!seeking){position=stream.position}else if(!stream.seekable){throw new FS.ErrnoError(70)}var bytesWritten=stream.stream_ops.write(stream,buffer,offset,length,position,canOwn);if(!seeking)stream.position+=bytesWritten;return bytesWritten},allocate(stream,offset,length){if(FS.isClosed(stream)){throw new FS.ErrnoError(8)}if(offset<0||length<=0){throw new FS.ErrnoError(28)}if((stream.flags&2097155)===0){throw new FS.ErrnoError(8)}if(!FS.isFile(stream.node.mode)&&!FS.isDir(stream.node.mode)){throw new FS.ErrnoError(43)}if(!stream.stream_ops.allocate){throw new FS.ErrnoError(138)}stream.stream_ops.allocate(stream,offset,length)},mmap(stream,length,position,prot,flags){if((prot&2)!==0&&(flags&2)===0&&(stream.flags&2097155)!==2){throw new FS.ErrnoError(2)}if((stream.flags&2097155)===1){throw new FS.ErrnoError(2)}if(!stream.stream_ops.mmap){throw new FS.ErrnoError(43)}return stream.stream_ops.mmap(stream,length,position,prot,flags)},msync(stream,buffer,offset,length,mmapFlags){if(!stream.stream_ops.msync){return 0}return stream.stream_ops.msync(stream,buffer,offset,length,mmapFlags)},munmap:stream=>0,ioctl(stream,cmd,arg){if(!stream.stream_ops.ioctl){throw new FS.ErrnoError(59)}return stream.stream_ops.ioctl(stream,cmd,arg)},readFile(path,opts={}){opts.flags=opts.flags||0;opts.encoding=opts.encoding||\"binary\";if(opts.encoding!==\"utf8\"&&opts.encoding!==\"binary\"){throw new Error(`Invalid encoding type \"${opts.encoding}\"`)}var ret;var stream=FS.open(path,opts.flags);var stat=FS.stat(path);var length=stat.size;var buf=new Uint8Array(length);FS.read(stream,buf,0,length,0);if(opts.encoding===\"utf8\"){ret=UTF8ArrayToString(buf,0)}else if(opts.encoding===\"binary\"){ret=buf}FS.close(stream);return ret},writeFile(path,data,opts={}){opts.flags=opts.flags||577;var stream=FS.open(path,opts.flags,opts.mode);if(typeof data==\"string\"){var buf=new Uint8Array(lengthBytesUTF8(data)+1);var actualNumBytes=stringToUTF8Array(data,buf,0,buf.length);FS.write(stream,buf,0,actualNumBytes,undefined,opts.canOwn)}else if(ArrayBuffer.isView(data)){FS.write(stream,data,0,data.byteLength,undefined,opts.canOwn)}else{throw new Error(\"Unsupported data type\")}FS.close(stream)},cwd:()=>FS.currentPath,chdir(path){var lookup=FS.lookupPath(path,{follow:true});if(lookup.node===null){throw new FS.ErrnoError(44)}if(!FS.isDir(lookup.node.mode)){throw new FS.ErrnoError(54)}var errCode=FS.nodePermissions(lookup.node,\"x\");if(errCode){throw new FS.ErrnoError(errCode)}FS.currentPath=lookup.path},createDefaultDirectories(){FS.mkdir(\"/tmp\");FS.mkdir(\"/home\");FS.mkdir(\"/home/<USER>")},createDefaultDevices(){FS.mkdir(\"/dev\");FS.registerDevice(FS.makedev(1,3),{read:()=>0,write:(stream,buffer,offset,length,pos)=>length});FS.mkdev(\"/dev/null\",FS.makedev(1,3));TTY.register(FS.makedev(5,0),TTY.default_tty_ops);TTY.register(FS.makedev(6,0),TTY.default_tty1_ops);FS.mkdev(\"/dev/tty\",FS.makedev(5,0));FS.mkdev(\"/dev/tty1\",FS.makedev(6,0));var randomBuffer=new Uint8Array(1024),randomLeft=0;var randomByte=()=>{if(randomLeft===0){randomLeft=randomFill(randomBuffer).byteLength}return randomBuffer[--randomLeft]};FS.createDevice(\"/dev\",\"random\",randomByte);FS.createDevice(\"/dev\",\"urandom\",randomByte);FS.mkdir(\"/dev/shm\");FS.mkdir(\"/dev/shm/tmp\")},createSpecialDirectories(){FS.mkdir(\"/proc\");var proc_self=FS.mkdir(\"/proc/self\");FS.mkdir(\"/proc/self/fd\");FS.mount({mount(){var node=FS.createNode(proc_self,\"fd\",16384|511,73);node.node_ops={lookup(parent,name){var fd=+name;var stream=FS.getStreamChecked(fd);var ret={parent:null,mount:{mountpoint:\"fake\"},node_ops:{readlink:()=>stream.path}};ret.parent=ret;return ret}};return node}},{},\"/proc/self/fd\")},createStandardStreams(){if(Module[\"stdin\"]){FS.createDevice(\"/dev\",\"stdin\",Module[\"stdin\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdin\")}if(Module[\"stdout\"]){FS.createDevice(\"/dev\",\"stdout\",null,Module[\"stdout\"])}else{FS.symlink(\"/dev/tty\",\"/dev/stdout\")}if(Module[\"stderr\"]){FS.createDevice(\"/dev\",\"stderr\",null,Module[\"stderr\"])}else{FS.symlink(\"/dev/tty1\",\"/dev/stderr\")}var stdin=FS.open(\"/dev/stdin\",0);var stdout=FS.open(\"/dev/stdout\",1);var stderr=FS.open(\"/dev/stderr\",1)},ensureErrnoError(){if(FS.ErrnoError)return;FS.ErrnoError=function ErrnoError(errno,node){this.name=\"ErrnoError\";this.node=node;this.setErrno=function(errno){this.errno=errno};this.setErrno(errno);this.message=\"FS error\"};FS.ErrnoError.prototype=new Error;FS.ErrnoError.prototype.constructor=FS.ErrnoError;[44].forEach(code=>{FS.genericErrors[code]=new FS.ErrnoError(code);FS.genericErrors[code].stack=\"<generic error, no stack>\"})},staticInit(){FS.ensureErrnoError();FS.nameTable=new Array(4096);FS.mount(MEMFS,{},\"/\");FS.createDefaultDirectories();FS.createDefaultDevices();FS.createSpecialDirectories();FS.filesystems={\"MEMFS\":MEMFS}},init(input,output,error){FS.init.initialized=true;FS.ensureErrnoError();Module[\"stdin\"]=input||Module[\"stdin\"];Module[\"stdout\"]=output||Module[\"stdout\"];Module[\"stderr\"]=error||Module[\"stderr\"];FS.createStandardStreams()},quit(){FS.init.initialized=false;for(var i=0;i<FS.streams.length;i++){var stream=FS.streams[i];if(!stream){continue}FS.close(stream)}},findObject(path,dontResolveLastLink){var ret=FS.analyzePath(path,dontResolveLastLink);if(!ret.exists){return null}return ret.object},analyzePath(path,dontResolveLastLink){try{var lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});path=lookup.path}catch(e){}var ret={isRoot:false,exists:false,error:0,name:null,path:null,object:null,parentExists:false,parentPath:null,parentObject:null};try{var lookup=FS.lookupPath(path,{parent:true});ret.parentExists=true;ret.parentPath=lookup.path;ret.parentObject=lookup.node;ret.name=PATH.basename(path);lookup=FS.lookupPath(path,{follow:!dontResolveLastLink});ret.exists=true;ret.path=lookup.path;ret.object=lookup.node;ret.name=lookup.node.name;ret.isRoot=lookup.path===\"/\"}catch(e){ret.error=e.errno}return ret},createPath(parent,path,canRead,canWrite){parent=typeof parent==\"string\"?parent:FS.getPath(parent);var parts=path.split(\"/\").reverse();while(parts.length){var part=parts.pop();if(!part)continue;var current=PATH.join2(parent,part);try{FS.mkdir(current)}catch(e){}parent=current}return current},createFile(parent,name,properties,canRead,canWrite){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(canRead,canWrite);return FS.create(path,mode)},createDataFile(parent,name,data,canRead,canWrite,canOwn){var path=name;if(parent){parent=typeof parent==\"string\"?parent:FS.getPath(parent);path=name?PATH.join2(parent,name):parent}var mode=FS_getMode(canRead,canWrite);var node=FS.create(path,mode);if(data){if(typeof data==\"string\"){var arr=new Array(data.length);for(var i=0,len=data.length;i<len;++i)arr[i]=data.charCodeAt(i);data=arr}FS.chmod(node,mode|146);var stream=FS.open(node,577);FS.write(stream,data,0,data.length,0,canOwn);FS.close(stream);FS.chmod(node,mode)}return node},createDevice(parent,name,input,output){var path=PATH.join2(typeof parent==\"string\"?parent:FS.getPath(parent),name);var mode=FS_getMode(!!input,!!output);if(!FS.createDevice.major)FS.createDevice.major=64;var dev=FS.makedev(FS.createDevice.major++,0);FS.registerDevice(dev,{open(stream){stream.seekable=false},close(stream){if(output&&output.buffer&&output.buffer.length){output(10)}},read(stream,buffer,offset,length,pos){var bytesRead=0;for(var i=0;i<length;i++){var result;try{result=input()}catch(e){throw new FS.ErrnoError(29)}if(result===undefined&&bytesRead===0){throw new FS.ErrnoError(6)}if(result===null||result===undefined)break;bytesRead++;buffer[offset+i]=result}if(bytesRead){stream.node.timestamp=Date.now()}return bytesRead},write(stream,buffer,offset,length,pos){for(var i=0;i<length;i++){try{output(buffer[offset+i])}catch(e){throw new FS.ErrnoError(29)}}if(length){stream.node.timestamp=Date.now()}return i}});return FS.mkdev(path,mode,dev)},forceLoadFile(obj){if(obj.isDevice||obj.isFolder||obj.link||obj.contents)return true;if(typeof XMLHttpRequest!=\"undefined\"){throw new Error(\"Lazy loading should have been performed (contents set) in createLazyFile, but it was not. Lazy loading only works in web workers. Use --embed-file or --preload-file in emcc on the main thread.\")}else if(read_){try{obj.contents=intArrayFromString(read_(obj.url),true);obj.usedBytes=obj.contents.length}catch(e){throw new FS.ErrnoError(29)}}else{throw new Error(\"Cannot load without read() or XMLHttpRequest.\")}},createLazyFile(parent,name,url,canRead,canWrite){function LazyUint8Array(){this.lengthKnown=false;this.chunks=[]}LazyUint8Array.prototype.get=function LazyUint8Array_get(idx){if(idx>this.length-1||idx<0){return undefined}var chunkOffset=idx%this.chunkSize;var chunkNum=idx/this.chunkSize|0;return this.getter(chunkNum)[chunkOffset]};LazyUint8Array.prototype.setDataGetter=function LazyUint8Array_setDataGetter(getter){this.getter=getter};LazyUint8Array.prototype.cacheLength=function LazyUint8Array_cacheLength(){var xhr=new XMLHttpRequest;xhr.open(\"HEAD\",url,false);xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);var datalength=Number(xhr.getResponseHeader(\"Content-length\"));var header;var hasByteServing=(header=xhr.getResponseHeader(\"Accept-Ranges\"))&&header===\"bytes\";var usesGzip=(header=xhr.getResponseHeader(\"Content-Encoding\"))&&header===\"gzip\";var chunkSize=1024*1024;if(!hasByteServing)chunkSize=datalength;var doXHR=(from,to)=>{if(from>to)throw new Error(\"invalid range (\"+from+\", \"+to+\") or no bytes requested!\");if(to>datalength-1)throw new Error(\"only \"+datalength+\" bytes available! programmer error!\");var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);if(datalength!==chunkSize)xhr.setRequestHeader(\"Range\",\"bytes=\"+from+\"-\"+to);xhr.responseType=\"arraybuffer\";if(xhr.overrideMimeType){xhr.overrideMimeType(\"text/plain; charset=x-user-defined\")}xhr.send(null);if(!(xhr.status>=200&&xhr.status<300||xhr.status===304))throw new Error(\"Couldn't load \"+url+\". Status: \"+xhr.status);if(xhr.response!==undefined){return new Uint8Array(xhr.response||[])}return intArrayFromString(xhr.responseText||\"\",true)};var lazyArray=this;lazyArray.setDataGetter(chunkNum=>{var start=chunkNum*chunkSize;var end=(chunkNum+1)*chunkSize-1;end=Math.min(end,datalength-1);if(typeof lazyArray.chunks[chunkNum]==\"undefined\"){lazyArray.chunks[chunkNum]=doXHR(start,end)}if(typeof lazyArray.chunks[chunkNum]==\"undefined\")throw new Error(\"doXHR failed!\");return lazyArray.chunks[chunkNum]});if(usesGzip||!datalength){chunkSize=datalength=1;datalength=this.getter(0).length;chunkSize=datalength;out(\"LazyFiles on gzip forces download of the whole file when length is accessed\")}this._length=datalength;this._chunkSize=chunkSize;this.lengthKnown=true};if(typeof XMLHttpRequest!=\"undefined\"){if(!ENVIRONMENT_IS_WORKER)throw\"Cannot do synchronous binary XHRs outside webworkers in modern browsers. Use --embed-file or --preload-file in emcc\";var lazyArray=new LazyUint8Array;Object.defineProperties(lazyArray,{length:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._length}},chunkSize:{get:function(){if(!this.lengthKnown){this.cacheLength()}return this._chunkSize}}});var properties={isDevice:false,contents:lazyArray}}else{var properties={isDevice:false,url:url}}var node=FS.createFile(parent,name,properties,canRead,canWrite);if(properties.contents){node.contents=properties.contents}else if(properties.url){node.contents=null;node.url=properties.url}Object.defineProperties(node,{usedBytes:{get:function(){return this.contents.length}}});var stream_ops={};var keys=Object.keys(node.stream_ops);keys.forEach(key=>{var fn=node.stream_ops[key];stream_ops[key]=function forceLoadLazyFile(){FS.forceLoadFile(node);return fn.apply(null,arguments)}});function writeChunks(stream,buffer,offset,length,position){var contents=stream.node.contents;if(position>=contents.length)return 0;var size=Math.min(contents.length-position,length);if(contents.slice){for(var i=0;i<size;i++){buffer[offset+i]=contents[position+i]}}else{for(var i=0;i<size;i++){buffer[offset+i]=contents.get(position+i)}}return size}stream_ops.read=(stream,buffer,offset,length,position)=>{FS.forceLoadFile(node);return writeChunks(stream,buffer,offset,length,position)};stream_ops.mmap=(stream,length,position,prot,flags)=>{FS.forceLoadFile(node);var ptr=mmapAlloc(length);if(!ptr){throw new FS.ErrnoError(48)}writeChunks(stream,HEAP8,ptr,length,position);return{ptr:ptr,allocated:true}};node.stream_ops=stream_ops;return node}};var SYSCALLS={DEFAULT_POLLMASK:5,calculateAt(dirfd,path,allowEmpty){if(PATH.isAbs(path)){return path}var dir;if(dirfd===-100){dir=FS.cwd()}else{var dirstream=SYSCALLS.getStreamFromFD(dirfd);dir=dirstream.path}if(path.length==0){if(!allowEmpty){throw new FS.ErrnoError(44)}return dir}return PATH.join2(dir,path)},doStat(func,path,buf){try{var stat=func(path)}catch(e){if(e&&e.node&&PATH.normalize(path)!==PATH.normalize(FS.getPath(e.node))){return-54}throw e}HEAP32[buf>>2]=stat.dev;HEAP32[buf+4>>2]=stat.mode;HEAPU32[buf+8>>2]=stat.nlink;HEAP32[buf+12>>2]=stat.uid;HEAP32[buf+16>>2]=stat.gid;HEAP32[buf+20>>2]=stat.rdev;tempI64=[stat.size>>>0,(tempDouble=stat.size,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+24>>2]=tempI64[0],HEAP32[buf+28>>2]=tempI64[1];HEAP32[buf+32>>2]=4096;HEAP32[buf+36>>2]=stat.blocks;var atime=stat.atime.getTime();var mtime=stat.mtime.getTime();var ctime=stat.ctime.getTime();tempI64=[Math.floor(atime/1e3)>>>0,(tempDouble=Math.floor(atime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+40>>2]=tempI64[0],HEAP32[buf+44>>2]=tempI64[1];HEAPU32[buf+48>>2]=atime%1e3*1e3;tempI64=[Math.floor(mtime/1e3)>>>0,(tempDouble=Math.floor(mtime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+56>>2]=tempI64[0],HEAP32[buf+60>>2]=tempI64[1];HEAPU32[buf+64>>2]=mtime%1e3*1e3;tempI64=[Math.floor(ctime/1e3)>>>0,(tempDouble=Math.floor(ctime/1e3),+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+72>>2]=tempI64[0],HEAP32[buf+76>>2]=tempI64[1];HEAPU32[buf+80>>2]=ctime%1e3*1e3;tempI64=[stat.ino>>>0,(tempDouble=stat.ino,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[buf+88>>2]=tempI64[0],HEAP32[buf+92>>2]=tempI64[1];return 0},doMsync(addr,stream,len,flags,offset){if(!FS.isFile(stream.node.mode)){throw new FS.ErrnoError(43)}if(flags&2){return 0}var buffer=HEAPU8.slice(addr,addr+len);FS.msync(stream,buffer,offset,len,flags)},varargs:undefined,get(){var ret=HEAP32[+SYSCALLS.varargs>>2];SYSCALLS.varargs+=4;return ret},getp(){return SYSCALLS.get()},getStr(ptr){var ret=UTF8ToString(ptr);return ret},getStreamFromFD(fd){var stream=FS.getStreamChecked(fd);return stream}};var _environ_get=(__environ,environ_buf)=>{var bufSize=0;getEnvStrings().forEach((string,i)=>{var ptr=environ_buf+bufSize;HEAPU32[__environ+i*4>>2]=ptr;stringToAscii(string,ptr);bufSize+=string.length+1});return 0};var _environ_sizes_get=(penviron_count,penviron_buf_size)=>{var strings=getEnvStrings();HEAPU32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(string=>bufSize+=string.length+1);HEAPU32[penviron_buf_size>>2]=bufSize;return 0};function _fd_close(fd){try{var stream=SYSCALLS.getStreamFromFD(fd);FS.close(stream);return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doReadv=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.read(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(curr<len)break;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_read(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doReadv(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var convertI32PairToI53Checked=(lo,hi)=>hi+2097152>>>0<4194305-!!lo?(lo>>>0)+hi*4294967296:NaN;function _fd_seek(fd,offset_low,offset_high,whence,newOffset){var offset=convertI32PairToI53Checked(offset_low,offset_high);try{if(isNaN(offset))return 61;var stream=SYSCALLS.getStreamFromFD(fd);FS.llseek(stream,offset,whence);tempI64=[stream.position>>>0,(tempDouble=stream.position,+Math.abs(tempDouble)>=1?tempDouble>0?+Math.floor(tempDouble/4294967296)>>>0:~~+Math.ceil((tempDouble-+(~~tempDouble>>>0))/4294967296)>>>0:0)],HEAP32[newOffset>>2]=tempI64[0],HEAP32[newOffset+4>>2]=tempI64[1];if(stream.getdents&&offset===0&&whence===0)stream.getdents=null;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var doWritev=(stream,iov,iovcnt,offset)=>{var ret=0;for(var i=0;i<iovcnt;i++){var ptr=HEAPU32[iov>>2];var len=HEAPU32[iov+4>>2];iov+=8;var curr=FS.write(stream,HEAP8,ptr,len,offset);if(curr<0)return-1;ret+=curr;if(typeof offset!==\"undefined\"){offset+=curr}}return ret};function _fd_write(fd,iov,iovcnt,pnum){try{var stream=SYSCALLS.getStreamFromFD(fd);var num=doWritev(stream,iov,iovcnt);HEAPU32[pnum>>2]=num;return 0}catch(e){if(typeof FS==\"undefined\"||!(e.name===\"ErrnoError\"))throw e;return e.errno}}var isLeapYear=year=>year%4===0&&(year%100!==0||year%400===0);var arraySum=(array,index)=>{var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum};var MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];var addDays=(date,days)=>{var newDate=new Date(date.getTime());while(days>0){var leap=isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate};var writeArrayToMemory=(array,buffer)=>{HEAP8.set(array,buffer)};var _strftime=(s,maxsize,format,tm)=>{var tm_zone=HEAPU32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value==\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}return thisDate.getFullYear()}return thisDate.getFullYear()-1}var EXPANSION_RULES_2={\"%a\":date=>WEEKDAYS[date.tm_wday].substring(0,3),\"%A\":date=>WEEKDAYS[date.tm_wday],\"%b\":date=>MONTHS[date.tm_mon].substring(0,3),\"%B\":date=>MONTHS[date.tm_mon],\"%C\":date=>{var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":date=>leadingNulls(date.tm_mday,2),\"%e\":date=>leadingSomething(date.tm_mday,2,\" \"),\"%g\":date=>getWeekBasedYear(date).toString().substring(2),\"%G\":date=>getWeekBasedYear(date),\"%H\":date=>leadingNulls(date.tm_hour,2),\"%I\":date=>{var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":date=>leadingNulls(date.tm_mday+arraySum(isLeapYear(date.tm_year+1900)?MONTH_DAYS_LEAP:MONTH_DAYS_REGULAR,date.tm_mon-1),3),\"%m\":date=>leadingNulls(date.tm_mon+1,2),\"%M\":date=>leadingNulls(date.tm_min,2),\"%n\":()=>\"\\n\",\"%p\":date=>{if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}return\"PM\"},\"%S\":date=>leadingNulls(date.tm_sec,2),\"%t\":()=>\"\\t\",\"%u\":date=>date.tm_wday||7,\"%U\":date=>{var days=date.tm_yday+7-date.tm_wday;return leadingNulls(Math.floor(days/7),2)},\"%V\":date=>{var val=Math.floor((date.tm_yday+7-(date.tm_wday+6)%7)/7);if((date.tm_wday+371-date.tm_yday-2)%7<=2){val++}if(!val){val=52;var dec31=(date.tm_wday+7-date.tm_yday-1)%7;if(dec31==4||dec31==5&&isLeapYear(date.tm_year%400-1)){val++}}else if(val==53){var jan1=(date.tm_wday+371-date.tm_yday)%7;if(jan1!=4&&(jan1!=3||!isLeapYear(date.tm_year)))val=1}return leadingNulls(val,2)},\"%w\":date=>date.tm_wday,\"%W\":date=>{var days=date.tm_yday+7-(date.tm_wday+6)%7;return leadingNulls(Math.floor(days/7),2)},\"%y\":date=>(date.tm_year+1900).toString().substring(2),\"%Y\":date=>date.tm_year+1900,\"%z\":date=>{var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":date=>date.tm_zone,\"%%\":()=>\"%\"};pattern=pattern.replace(/%%/g,\"\\0\\0\");for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}pattern=pattern.replace(/\\0\\0/g,\"%\");var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1};var _strftime_l=(s,maxsize,format,tm,loc)=>_strftime(s,maxsize,format,tm);var getCFunc=ident=>{var func=Module[\"_\"+ident];return func};var stringToUTF8OnStack=str=>{var size=lengthBytesUTF8(str)+1;var ret=stackAlloc(size);stringToUTF8(str,ret,size);return ret};var ccall=(ident,returnType,argTypes,args,opts)=>{var toC={\"string\":str=>{var ret=0;if(str!==null&&str!==undefined&&str!==0){ret=stringToUTF8OnStack(str)}return ret},\"array\":arr=>{var ret=stackAlloc(arr.length);writeArrayToMemory(arr,ret);return ret}};function convertReturnValue(ret){if(returnType===\"string\"){return UTF8ToString(ret)}if(returnType===\"boolean\")return Boolean(ret);return ret}var func=getCFunc(ident);var cArgs=[];var stack=0;if(args){for(var i=0;i<args.length;i++){var converter=toC[argTypes[i]];if(converter){if(stack===0)stack=stackSave();cArgs[i]=converter(args[i])}else{cArgs[i]=args[i]}}}var ret=func.apply(null,cArgs);function onDone(ret){if(stack!==0)stackRestore(stack);return convertReturnValue(ret)}ret=onDone(ret);return ret};InternalError=Module[\"InternalError\"]=class InternalError extends Error{constructor(message){super(message);this.name=\"InternalError\"}};embind_init_charCodes();BindingError=Module[\"BindingError\"]=class BindingError extends Error{constructor(message){super(message);this.name=\"BindingError\"}};init_ClassHandle();init_embind();init_RegisteredPointer();UnboundTypeError=Module[\"UnboundTypeError\"]=extendError(Error,\"UnboundTypeError\");handleAllocatorInit();init_emval();var FSNode=function(parent,name,mode,rdev){if(!parent){parent=this}this.parent=parent;this.mount=parent.mount;this.mounted=null;this.id=FS.nextInode++;this.name=name;this.mode=mode;this.node_ops={};this.stream_ops={};this.rdev=rdev};var readMode=292|73;var writeMode=146;Object.defineProperties(FSNode.prototype,{read:{get:function(){return(this.mode&readMode)===readMode},set:function(val){val?this.mode|=readMode:this.mode&=~readMode}},write:{get:function(){return(this.mode&writeMode)===writeMode},set:function(val){val?this.mode|=writeMode:this.mode&=~writeMode}},isFolder:{get:function(){return FS.isDir(this.mode)}},isDevice:{get:function(){return FS.isChrdev(this.mode)}}});FS.FSNode=FSNode;FS.createPreloadedFile=FS_createPreloadedFile;FS.staticInit();var wasmImports={d:___cxa_throw,n:__embind_finalize_value_array,l:__embind_finalize_value_object,w:__embind_register_bigint,G:__embind_register_bool,h:__embind_register_class,g:__embind_register_class_constructor,c:__embind_register_class_function,q:__embind_register_class_property,F:__embind_register_emval,p:__embind_register_enum,i:__embind_register_enum_value,t:__embind_register_float,a:__embind_register_function,j:__embind_register_integer,e:__embind_register_memory_view,u:__embind_register_std_string,r:__embind_register_std_wstring,o:__embind_register_value_array,b:__embind_register_value_array_element,m:__embind_register_value_object,f:__embind_register_value_object_field,H:__embind_register_void,I:__emval_decref,J:__emval_incref,k:__emval_take_value,s:_abort,E:_emscripten_memcpy_js,y:_emscripten_resize_heap,z:_environ_get,A:_environ_sizes_get,B:_fd_close,D:_fd_read,v:_fd_seek,C:_fd_write,x:_strftime_l};var wasmExports=createWasm();var ___wasm_call_ctors=()=>(___wasm_call_ctors=wasmExports[\"L\"])();var _malloc=Module[\"_malloc\"]=a0=>(_malloc=Module[\"_malloc\"]=wasmExports[\"M\"])(a0);var _free=Module[\"_free\"]=a0=>(_free=Module[\"_free\"]=wasmExports[\"N\"])(a0);var ___getTypeName=a0=>(___getTypeName=wasmExports[\"P\"])(a0);var __embind_initialize_bindings=Module[\"__embind_initialize_bindings\"]=()=>(__embind_initialize_bindings=Module[\"__embind_initialize_bindings\"]=wasmExports[\"Q\"])();var ___errno_location=()=>(___errno_location=wasmExports[\"__errno_location\"])();var stackSave=()=>(stackSave=wasmExports[\"R\"])();var stackRestore=a0=>(stackRestore=wasmExports[\"S\"])(a0);var stackAlloc=a0=>(stackAlloc=wasmExports[\"T\"])(a0);var ___cxa_increment_exception_refcount=a0=>(___cxa_increment_exception_refcount=wasmExports[\"__cxa_increment_exception_refcount\"])(a0);var ___cxa_is_pointer_type=a0=>(___cxa_is_pointer_type=wasmExports[\"U\"])(a0);var dynCall_jiji=Module[\"dynCall_jiji\"]=(a0,a1,a2,a3,a4)=>(dynCall_jiji=Module[\"dynCall_jiji\"]=wasmExports[\"V\"])(a0,a1,a2,a3,a4);var dynCall_viijii=Module[\"dynCall_viijii\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_viijii=Module[\"dynCall_viijii\"]=wasmExports[\"W\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=(a0,a1,a2,a3,a4,a5,a6)=>(dynCall_iiiiij=Module[\"dynCall_iiiiij\"]=wasmExports[\"X\"])(a0,a1,a2,a3,a4,a5,a6);var dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8)=>(dynCall_iiiiijj=Module[\"dynCall_iiiiijj\"]=wasmExports[\"Y\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8);var dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9)=>(dynCall_iiiiiijj=Module[\"dynCall_iiiiiijj\"]=wasmExports[\"Z\"])(a0,a1,a2,a3,a4,a5,a6,a7,a8,a9);Module[\"ccall\"]=ccall;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(){if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();\n\n\n  return moduleArg.ready\n}\n\n);\n})();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/process.js\n"));

/***/ })

}]);