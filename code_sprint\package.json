{"name": "login-app", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-images": "node check-images.js", "update-logos": "node update-logos.js", "fix-logo-names": "node fix-logo-names.js", "integrate-frontend": "node integrate-frontend.js", "setup": "npm install --legacy-peer-deps && cd backend && npm install && cd ../frontend && npm install --legacy-peer-deps && cd ..", "dev:backend": "cd backend && npm run dev", "dev:frontend": "cd frontend && npm run dev", "dev:frontend-root": "npm run dev", "dev:full": "concurrently \"npm run dev:backend\" \"npm run dev:frontend\"", "dev:full-root": "concurrently \"npm run dev:backend\" \"npm run dev:frontend-root\""}, "dependencies": {"@lottiefiles/react-lottie-player": "^3.5.4", "@monaco-editor/react": "^4.6.0", "@souhaildev/reactemojis": "^1.0.5", "@splinetool/react-spline": "^4.0.0", "@splinetool/runtime": "^1.9.98", "@splinetool/viewer": "^1.9.46", "@tabler/icons-react": "^3.19.0", "axios": "^1.7.9", "bcryptjs": "^2.4.3", "clsx": "^2.1.1", "dotenv": "^16.4.7", "framer-motion": "^11.5.6", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.440.0", "mongoose": "^8.5.4", "next": "^15.1.2", "prismjs": "^1.29.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.54.1", "react-icons": "^5.3.0", "react-simple-code-editor": "^0.14.1", "react-spring": "^9.7.4", "react-syntax-highlighter": "^15.6.1", "recharts": "^2.12.7", "replicate": "^1.0.1", "sentiment": "^5.0.2", "tailwind-merge": "^2.5.3"}, "devDependencies": {"@eslint/js": "^9.16.0", "autoprefixer": "^10.4.20", "concurrently": "^8.2.2", "eslint": "^9.16.0", "eslint-plugin-react": "^7.37.2", "globals": "^15.12.0", "postcss": "^8.4.45", "tailwindcss": "^3.4.11"}}