# 🚀 Quick Start Guide

## Step 1: Install Dependencies
```bash
npm run setup
```

## Step 2: Configure Environment
Create `.env` file with:
```env
MONGODB_URI=mongodb://localhost:27017/codesprint
JWT_SECRET=your_super_secret_jwt_key_here_make_it_long_and_random
```

## Step 3: Start MongoDB
```bash
# If using local MongoDB
mongod

# Or use MongoDB Atlas (cloud)
# Just update MONGODB_URI in .env
```

## Step 4: Run the Application

### Option A: Run Both Frontend & Backend Together
```bash
npm run dev:full
```

### Option B: Run Separately
```bash
# Terminal 1 - Backend
npm run dev:backend

# Terminal 2 - Frontend  
npm run dev:frontend
```

## Step 5: Check Images
```bash
npm run check-images
```

## 🌐 Access the Application
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5000

## 🔍 Test Pages
1. Home: http://localhost:3000
2. Courses: http://localhost:3000/courses
3. Login: http://localhost:3000/login
4. Dashboard: http://localhost:3000/dashboard (after login)

## ✅ Image Check Results
After running `npm run check-images`, you should see:
- ✅ All level icons (level1-icon.svg to level5-icon.svg)
- ✅ Chart images (line_charts.png, bar_charts.png, pie_charts.png)
- ✅ Logo and mascot images
- ✅ Level 1 lesson images

## 🐛 Common Issues

### Port Already in Use
```bash
# Kill processes on ports
npx kill-port 3000
npx kill-port 5000
```

### Dependency Issues
```bash
npm install --legacy-peer-deps --force
```

### MongoDB Connection
- Ensure MongoDB is running
- Check connection string in .env
- For Atlas: whitelist your IP address

---
**Need help? Check the full README.md**
