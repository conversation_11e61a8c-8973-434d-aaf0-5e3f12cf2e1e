"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_opentype_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/opentype.js":
/*!************************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/opentype.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Bidi: () => (/* binding */ ne),\n/* harmony export */   BoundingBox: () => (/* binding */ pe),\n/* harmony export */   Font: () => (/* binding */ w),\n/* harmony export */   Glyph: () => (/* binding */ Q),\n/* harmony export */   Path: () => (/* binding */ P),\n/* harmony export */   _parse: () => (/* binding */ k),\n/* harmony export */   \"default\": () => (/* binding */ Ho),\n/* harmony export */   load: () => (/* binding */ Bo),\n/* harmony export */   loadSync: () => (/* binding */ Io),\n/* harmony export */   parse: () => (/* binding */ Dr)\n/* harmony export */ });\n/* provided dependency */ var Buffer = __webpack_require__(/*! buffer */ \"(pages-dir-browser)/./node_modules/buffer/index.js\")[\"Buffer\"];\nvar va=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports);var Ne=va(()=>{});String.prototype.codePointAt||function(){var e=function(){try{var t={},a=Object.defineProperty,n=a(t,t,t)&&a}catch{}return n}(),r=function(t){if(this==null)throw TypeError();var a=String(this),n=a.length,s=t?Number(t):0;if(s!=s&&(s=0),!(s<0||s>=n)){var i=a.charCodeAt(s),u;return i>=55296&&i<=56319&&n>s+1&&(u=a.charCodeAt(s+1),u>=56320&&u<=57343)?(i-55296)*1024+u-56320+65536:i}};e?e(String.prototype,\"codePointAt\",{value:r,configurable:!0,writable:!0}):String.prototype.codePointAt=r}();var Sr=0,xt=-3;function Le(){this.table=new Uint16Array(16),this.trans=new Uint16Array(288)}function da(e,r){this.source=e,this.sourceIndex=0,this.tag=0,this.bitcount=0,this.dest=r,this.destLen=0,this.ltree=new Le,this.dtree=new Le}var bt=new Le,St=new Le,Tr=new Uint8Array(30),kr=new Uint16Array(30),Tt=new Uint8Array(30),kt=new Uint16Array(30),ga=new Uint8Array([16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15]),Br=new Le,se=new Uint8Array(288+32);function Ft(e,r,t,a){var n,s;for(n=0;n<t;++n)e[n]=0;for(n=0;n<30-t;++n)e[n+t]=n/t|0;for(s=a,n=0;n<30;++n)r[n]=s,s+=1<<e[n]}function ma(e,r){var t;for(t=0;t<7;++t)e.table[t]=0;for(e.table[7]=24,e.table[8]=152,e.table[9]=112,t=0;t<24;++t)e.trans[t]=256+t;for(t=0;t<144;++t)e.trans[24+t]=t;for(t=0;t<8;++t)e.trans[24+144+t]=280+t;for(t=0;t<112;++t)e.trans[24+144+8+t]=144+t;for(t=0;t<5;++t)r.table[t]=0;for(r.table[5]=32,t=0;t<32;++t)r.trans[t]=t}var Ir=new Uint16Array(16);function ar(e,r,t,a){var n,s;for(n=0;n<16;++n)e.table[n]=0;for(n=0;n<a;++n)e.table[r[t+n]]++;for(e.table[0]=0,s=0,n=0;n<16;++n)Ir[n]=s,s+=e.table[n];for(n=0;n<a;++n)r[t+n]&&(e.trans[Ir[r[t+n]]++]=n)}function ya(e){e.bitcount--||(e.tag=e.source[e.sourceIndex++],e.bitcount=7);var r=e.tag&1;return e.tag>>>=1,r}function ie(e,r,t){if(!r)return t;for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var a=e.tag&65535>>>16-r;return e.tag>>>=r,e.bitcount-=r,a+t}function hr(e,r){for(;e.bitcount<24;)e.tag|=e.source[e.sourceIndex++]<<e.bitcount,e.bitcount+=8;var t=0,a=0,n=0,s=e.tag;do a=2*a+(s&1),s>>>=1,++n,t+=r.table[n],a-=r.table[n];while(a>=0);return e.tag=s,e.bitcount-=n,r.trans[t+a]}function xa(e,r,t){var a,n,s,i,u,o;for(a=ie(e,5,257),n=ie(e,5,1),s=ie(e,4,4),i=0;i<19;++i)se[i]=0;for(i=0;i<s;++i){var l=ie(e,3,0);se[ga[i]]=l}for(ar(Br,se,0,19),u=0;u<a+n;){var f=hr(e,Br);switch(f){case 16:var h=se[u-1];for(o=ie(e,2,3);o;--o)se[u++]=h;break;case 17:for(o=ie(e,3,3);o;--o)se[u++]=0;break;case 18:for(o=ie(e,7,11);o;--o)se[u++]=0;break;default:se[u++]=f;break}}ar(r,se,0,a),ar(t,se,a,n)}function Mr(e,r,t){for(;;){var a=hr(e,r);if(a===256)return Sr;if(a<256)e.dest[e.destLen++]=a;else{var n,s,i,u;for(a-=257,n=ie(e,Tr[a],kr[a]),s=hr(e,t),i=e.destLen-ie(e,Tt[s],kt[s]),u=i;u<i+n;++u)e.dest[e.destLen++]=e.dest[u]}}}function ba(e){for(var r,t,a;e.bitcount>8;)e.sourceIndex--,e.bitcount-=8;if(r=e.source[e.sourceIndex+1],r=256*r+e.source[e.sourceIndex],t=e.source[e.sourceIndex+3],t=256*t+e.source[e.sourceIndex+2],r!==(~t&65535))return xt;for(e.sourceIndex+=4,a=r;a;--a)e.dest[e.destLen++]=e.source[e.sourceIndex++];return e.bitcount=0,Sr}function Sa(e,r){var t=new da(e,r),a,n,s;do{switch(a=ya(t),n=ie(t,2,0),n){case 0:s=ba(t);break;case 1:s=Mr(t,bt,St);break;case 2:xa(t,t.ltree,t.dtree),s=Mr(t,t.ltree,t.dtree);break;default:s=xt}if(s!==Sr)throw new Error(\"Data error\")}while(!a);return t.destLen<t.dest.length?typeof t.dest.slice==\"function\"?t.dest.slice(0,t.destLen):t.dest.subarray(0,t.destLen):t.dest}ma(bt,St);Ft(Tr,kr,4,3);Ft(Tt,kt,2,1);Tr[28]=0;kr[28]=258;var Ta=Sa;function Te(e,r,t,a,n){return Math.pow(1-n,3)*e+3*Math.pow(1-n,2)*n*r+3*(1-n)*Math.pow(n,2)*t+Math.pow(n,3)*a}function pe(){this.x1=Number.NaN,this.y1=Number.NaN,this.x2=Number.NaN,this.y2=Number.NaN}pe.prototype.isEmpty=function(){return isNaN(this.x1)||isNaN(this.y1)||isNaN(this.x2)||isNaN(this.y2)};pe.prototype.addPoint=function(e,r){typeof e==\"number\"&&((isNaN(this.x1)||isNaN(this.x2))&&(this.x1=e,this.x2=e),e<this.x1&&(this.x1=e),e>this.x2&&(this.x2=e)),typeof r==\"number\"&&((isNaN(this.y1)||isNaN(this.y2))&&(this.y1=r,this.y2=r),r<this.y1&&(this.y1=r),r>this.y2&&(this.y2=r))};pe.prototype.addX=function(e){this.addPoint(e,null)};pe.prototype.addY=function(e){this.addPoint(null,e)};pe.prototype.addBezier=function(e,r,t,a,n,s,i,u){var o=[e,r],l=[t,a],f=[n,s],h=[i,u];this.addPoint(e,r),this.addPoint(i,u);for(var p=0;p<=1;p++){var c=6*o[p]-12*l[p]+6*f[p],d=-3*o[p]+9*l[p]-9*f[p]+3*h[p],x=3*l[p]-3*o[p];if(d===0){if(c===0)continue;var m=-x/c;0<m&&m<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],m)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],m)));continue}var y=Math.pow(c,2)-4*x*d;if(!(y<0)){var C=(-c+Math.sqrt(y))/(2*d);0<C&&C<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],C)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],C)));var S=(-c-Math.sqrt(y))/(2*d);0<S&&S<1&&(p===0&&this.addX(Te(o[p],l[p],f[p],h[p],S)),p===1&&this.addY(Te(o[p],l[p],f[p],h[p],S)))}}};pe.prototype.addQuad=function(e,r,t,a,n,s){var i=e+.6666666666666666*(t-e),u=r+2/3*(a-r),o=i+1/3*(n-e),l=u+1/3*(s-r);this.addBezier(e,r,i,u,o,l,n,s)};function P(){this.commands=[],this.fill=\"black\",this.stroke=null,this.strokeWidth=1}P.prototype.moveTo=function(e,r){this.commands.push({type:\"M\",x:e,y:r})};P.prototype.lineTo=function(e,r){this.commands.push({type:\"L\",x:e,y:r})};P.prototype.curveTo=P.prototype.bezierCurveTo=function(e,r,t,a,n,s){this.commands.push({type:\"C\",x1:e,y1:r,x2:t,y2:a,x:n,y:s})};P.prototype.quadTo=P.prototype.quadraticCurveTo=function(e,r,t,a){this.commands.push({type:\"Q\",x1:e,y1:r,x:t,y:a})};P.prototype.close=P.prototype.closePath=function(){this.commands.push({type:\"Z\"})};P.prototype.extend=function(e){if(e.commands)e=e.commands;else if(e instanceof pe){var r=e;this.moveTo(r.x1,r.y1),this.lineTo(r.x2,r.y1),this.lineTo(r.x2,r.y2),this.lineTo(r.x1,r.y2),this.close();return}Array.prototype.push.apply(this.commands,e)};P.prototype.getBoundingBox=function(){for(var e=new pe,r=0,t=0,a=0,n=0,s=0;s<this.commands.length;s++){var i=this.commands[s];switch(i.type){case\"M\":e.addPoint(i.x,i.y),r=a=i.x,t=n=i.y;break;case\"L\":e.addPoint(i.x,i.y),a=i.x,n=i.y;break;case\"Q\":e.addQuad(a,n,i.x1,i.y1,i.x,i.y),a=i.x,n=i.y;break;case\"C\":e.addBezier(a,n,i.x1,i.y1,i.x2,i.y2,i.x,i.y),a=i.x,n=i.y;break;case\"Z\":a=r,n=t;break;default:throw new Error(\"Unexpected path command \"+i.type)}}return e.isEmpty()&&e.addPoint(0,0),e};P.prototype.draw=function(e){e.beginPath();for(var r=0;r<this.commands.length;r+=1){var t=this.commands[r];t.type===\"M\"?e.moveTo(t.x,t.y):t.type===\"L\"?e.lineTo(t.x,t.y):t.type===\"C\"?e.bezierCurveTo(t.x1,t.y1,t.x2,t.y2,t.x,t.y):t.type===\"Q\"?e.quadraticCurveTo(t.x1,t.y1,t.x,t.y):t.type===\"Z\"&&e.closePath()}this.fill&&(e.fillStyle=this.fill,e.fill()),this.stroke&&(e.strokeStyle=this.stroke,e.lineWidth=this.strokeWidth,e.stroke())};P.prototype.toPathData=function(e){e=e!==void 0?e:2;function r(i){return Math.round(i)===i?\"\"+Math.round(i):i.toFixed(e)}function t(){for(var i=arguments,u=\"\",o=0;o<arguments.length;o+=1){var l=i[o];l>=0&&o>0&&(u+=\" \"),u+=r(l)}return u}for(var a=\"\",n=0;n<this.commands.length;n+=1){var s=this.commands[n];s.type===\"M\"?a+=\"M\"+t(s.x,s.y):s.type===\"L\"?a+=\"L\"+t(s.x,s.y):s.type===\"C\"?a+=\"C\"+t(s.x1,s.y1,s.x2,s.y2,s.x,s.y):s.type===\"Q\"?a+=\"Q\"+t(s.x1,s.y1,s.x,s.y):s.type===\"Z\"&&(a+=\"Z\")}return a};P.prototype.toSVG=function(e){var r='<path d=\"';return r+=this.toPathData(e),r+='\"',this.fill&&this.fill!==\"black\"&&(this.fill===null?r+=' fill=\"none\"':r+=' fill=\"'+this.fill+'\"'),this.stroke&&(r+=' stroke=\"'+this.stroke+'\" stroke-width=\"'+this.strokeWidth+'\"'),r+=\"/>\",r};P.prototype.toDOMElement=function(e){var r=this.toPathData(e),t=document.createElementNS(\"http://www.w3.org/2000/svg\",\"path\");return t.setAttribute(\"d\",r),t};function Ut(e){throw new Error(e)}function Pr(e,r){e||Ut(r)}var U={fail:Ut,argument:Pr,assert:Pr},Gr=32768,Nr=2147483648,Fe={},g={},E={};function ae(e){return function(){return e}}g.BYTE=function(e){return U.argument(e>=0&&e<=255,\"Byte value should be between 0 and 255.\"),[e]};E.BYTE=ae(1);g.CHAR=function(e){return[e.charCodeAt(0)]};E.CHAR=ae(1);g.CHARARRAY=function(e){typeof e>\"u\"&&(e=\"\",console.warn(\"Undefined CHARARRAY encountered and treated as an empty string. This is probably caused by a missing glyph name.\"));for(var r=[],t=0;t<e.length;t+=1)r[t]=e.charCodeAt(t);return r};E.CHARARRAY=function(e){return typeof e>\"u\"?0:e.length};g.USHORT=function(e){return[e>>8&255,e&255]};E.USHORT=ae(2);g.SHORT=function(e){return e>=Gr&&(e=-(2*Gr-e)),[e>>8&255,e&255]};E.SHORT=ae(2);g.UINT24=function(e){return[e>>16&255,e>>8&255,e&255]};E.UINT24=ae(3);g.ULONG=function(e){return[e>>24&255,e>>16&255,e>>8&255,e&255]};E.ULONG=ae(4);g.LONG=function(e){return e>=Nr&&(e=-(2*Nr-e)),[e>>24&255,e>>16&255,e>>8&255,e&255]};E.LONG=ae(4);g.FIXED=g.ULONG;E.FIXED=E.ULONG;g.FWORD=g.SHORT;E.FWORD=E.SHORT;g.UFWORD=g.USHORT;E.UFWORD=E.USHORT;g.LONGDATETIME=function(e){return[0,0,0,0,e>>24&255,e>>16&255,e>>8&255,e&255]};E.LONGDATETIME=ae(8);g.TAG=function(e){return U.argument(e.length===4,\"Tag should be exactly 4 ASCII characters.\"),[e.charCodeAt(0),e.charCodeAt(1),e.charCodeAt(2),e.charCodeAt(3)]};E.TAG=ae(4);g.Card8=g.BYTE;E.Card8=E.BYTE;g.Card16=g.USHORT;E.Card16=E.USHORT;g.OffSize=g.BYTE;E.OffSize=E.BYTE;g.SID=g.USHORT;E.SID=E.USHORT;g.NUMBER=function(e){return e>=-107&&e<=107?[e+139]:e>=108&&e<=1131?(e=e-108,[(e>>8)+247,e&255]):e>=-1131&&e<=-108?(e=-e-108,[(e>>8)+251,e&255]):e>=-32768&&e<=32767?g.NUMBER16(e):g.NUMBER32(e)};E.NUMBER=function(e){return g.NUMBER(e).length};g.NUMBER16=function(e){return[28,e>>8&255,e&255]};E.NUMBER16=ae(3);g.NUMBER32=function(e){return[29,e>>24&255,e>>16&255,e>>8&255,e&255]};E.NUMBER32=ae(5);g.REAL=function(e){var r=e.toString(),t=/\\.(\\d*?)(?:9{5,20}|0{5,20})\\d{0,2}(?:e(.+)|$)/.exec(r);if(t){var a=parseFloat(\"1e\"+((t[2]?+t[2]:0)+t[1].length));r=(Math.round(e*a)/a).toString()}for(var n=\"\",s=0,i=r.length;s<i;s+=1){var u=r[s];u===\"e\"?n+=r[++s]===\"-\"?\"c\":\"b\":u===\".\"?n+=\"a\":u===\"-\"?n+=\"e\":n+=u}n+=n.length&1?\"f\":\"ff\";for(var o=[30],l=0,f=n.length;l<f;l+=2)o.push(parseInt(n.substr(l,2),16));return o};E.REAL=function(e){return g.REAL(e).length};g.NAME=g.CHARARRAY;E.NAME=E.CHARARRAY;g.STRING=g.CHARARRAY;E.STRING=E.CHARARRAY;Fe.UTF8=function(e,r,t){for(var a=[],n=t,s=0;s<n;s++,r+=1)a[s]=e.getUint8(r);return String.fromCharCode.apply(null,a)};Fe.UTF16=function(e,r,t){for(var a=[],n=t/2,s=0;s<n;s++,r+=2)a[s]=e.getUint16(r);return String.fromCharCode.apply(null,a)};g.UTF16=function(e){for(var r=[],t=0;t<e.length;t+=1){var a=e.charCodeAt(t);r[r.length]=a>>8&255,r[r.length]=a&255}return r};E.UTF16=function(e){return e.length*2};var cr={\"x-mac-croatian\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\u0160\\u2122\\xB4\\xA8\\u2260\\u017D\\xD8\\u221E\\xB1\\u2264\\u2265\\u2206\\xB5\\u2202\\u2211\\u220F\\u0161\\u222B\\xAA\\xBA\\u03A9\\u017E\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u0106\\xAB\\u010C\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u0110\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\uF8FF\\xA9\\u2044\\u20AC\\u2039\\u203A\\xC6\\xBB\\u2013\\xB7\\u201A\\u201E\\u2030\\xC2\\u0107\\xC1\\u010D\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\u0111\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u03C0\\xCB\\u02DA\\xB8\\xCA\\xE6\\u02C7\",\"x-mac-cyrillic\":\"\\u0410\\u0411\\u0412\\u0413\\u0414\\u0415\\u0416\\u0417\\u0418\\u0419\\u041A\\u041B\\u041C\\u041D\\u041E\\u041F\\u0420\\u0421\\u0422\\u0423\\u0424\\u0425\\u0426\\u0427\\u0428\\u0429\\u042A\\u042B\\u042C\\u042D\\u042E\\u042F\\u2020\\xB0\\u0490\\xA3\\xA7\\u2022\\xB6\\u0406\\xAE\\xA9\\u2122\\u0402\\u0452\\u2260\\u0403\\u0453\\u221E\\xB1\\u2264\\u2265\\u0456\\xB5\\u0491\\u0408\\u0404\\u0454\\u0407\\u0457\\u0409\\u0459\\u040A\\u045A\\u0458\\u0405\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\u040B\\u045B\\u040C\\u045C\\u0455\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u201E\\u040E\\u045E\\u040F\\u045F\\u2116\\u0401\\u0451\\u044F\\u0430\\u0431\\u0432\\u0433\\u0434\\u0435\\u0436\\u0437\\u0438\\u0439\\u043A\\u043B\\u043C\\u043D\\u043E\\u043F\\u0440\\u0441\\u0442\\u0443\\u0444\\u0445\\u0446\\u0447\\u0448\\u0449\\u044A\\u044B\\u044C\\u044D\\u044E\",\"x-mac-gaelic\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u1E02\\xB1\\u2264\\u2265\\u1E03\\u010A\\u010B\\u1E0A\\u1E0B\\u1E1E\\u1E1F\\u0120\\u0121\\u1E40\\xE6\\xF8\\u1E41\\u1E56\\u1E57\\u027C\\u0192\\u017F\\u1E60\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\u1E61\\u1E9B\\xFF\\u0178\\u1E6A\\u20AC\\u2039\\u203A\\u0176\\u0177\\u1E6B\\xB7\\u1EF2\\u1EF3\\u204A\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\u2663\\xD2\\xDA\\xDB\\xD9\\u0131\\xDD\\xFD\\u0174\\u0175\\u1E84\\u1E85\\u1E80\\u1E81\\u1E82\\u1E83\",\"x-mac-greek\":\"\\xC4\\xB9\\xB2\\xC9\\xB3\\xD6\\xDC\\u0385\\xE0\\xE2\\xE4\\u0384\\xA8\\xE7\\xE9\\xE8\\xEA\\xEB\\xA3\\u2122\\xEE\\xEF\\u2022\\xBD\\u2030\\xF4\\xF6\\xA6\\u20AC\\xF9\\xFB\\xFC\\u2020\\u0393\\u0394\\u0398\\u039B\\u039E\\u03A0\\xDF\\xAE\\xA9\\u03A3\\u03AA\\xA7\\u2260\\xB0\\xB7\\u0391\\xB1\\u2264\\u2265\\xA5\\u0392\\u0395\\u0396\\u0397\\u0399\\u039A\\u039C\\u03A6\\u03AB\\u03A8\\u03A9\\u03AC\\u039D\\xAC\\u039F\\u03A1\\u2248\\u03A4\\xAB\\xBB\\u2026\\xA0\\u03A5\\u03A7\\u0386\\u0388\\u0153\\u2013\\u2015\\u201C\\u201D\\u2018\\u2019\\xF7\\u0389\\u038A\\u038C\\u038E\\u03AD\\u03AE\\u03AF\\u03CC\\u038F\\u03CD\\u03B1\\u03B2\\u03C8\\u03B4\\u03B5\\u03C6\\u03B3\\u03B7\\u03B9\\u03BE\\u03BA\\u03BB\\u03BC\\u03BD\\u03BF\\u03C0\\u03CE\\u03C1\\u03C3\\u03C4\\u03B8\\u03C9\\u03C2\\u03C7\\u03C5\\u03B6\\u03CA\\u03CB\\u0390\\u03B0\\xAD\",\"x-mac-icelandic\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\xDD\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\xD0\\xF0\\xDE\\xFE\\xFD\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-inuit\":\"\\u1403\\u1404\\u1405\\u1406\\u140A\\u140B\\u1431\\u1432\\u1433\\u1434\\u1438\\u1439\\u1449\\u144E\\u144F\\u1450\\u1451\\u1455\\u1456\\u1466\\u146D\\u146E\\u146F\\u1470\\u1472\\u1473\\u1483\\u148B\\u148C\\u148D\\u148E\\u1490\\u1491\\xB0\\u14A1\\u14A5\\u14A6\\u2022\\xB6\\u14A7\\xAE\\xA9\\u2122\\u14A8\\u14AA\\u14AB\\u14BB\\u14C2\\u14C3\\u14C4\\u14C5\\u14C7\\u14C8\\u14D0\\u14EF\\u14F0\\u14F1\\u14F2\\u14F4\\u14F5\\u1505\\u14D5\\u14D6\\u14D7\\u14D8\\u14DA\\u14DB\\u14EA\\u1528\\u1529\\u152A\\u152B\\u152D\\u2026\\xA0\\u152E\\u153E\\u1555\\u1556\\u1557\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\u1558\\u1559\\u155A\\u155D\\u1546\\u1547\\u1548\\u1549\\u154B\\u154C\\u1550\\u157F\\u1580\\u1581\\u1582\\u1583\\u1584\\u1585\\u158F\\u1590\\u1591\\u1592\\u1593\\u1594\\u1595\\u1671\\u1672\\u1673\\u1674\\u1675\\u1676\\u1596\\u15A0\\u15A1\\u15A2\\u15A3\\u15A4\\u15A5\\u15A6\\u157C\\u0141\\u0142\",\"x-mac-ce\":\"\\xC4\\u0100\\u0101\\xC9\\u0104\\xD6\\xDC\\xE1\\u0105\\u010C\\xE4\\u010D\\u0106\\u0107\\xE9\\u0179\\u017A\\u010E\\xED\\u010F\\u0112\\u0113\\u0116\\xF3\\u0117\\xF4\\xF6\\xF5\\xFA\\u011A\\u011B\\xFC\\u2020\\xB0\\u0118\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\u0119\\xA8\\u2260\\u0123\\u012E\\u012F\\u012A\\u2264\\u2265\\u012B\\u0136\\u2202\\u2211\\u0142\\u013B\\u013C\\u013D\\u013E\\u0139\\u013A\\u0145\\u0146\\u0143\\xAC\\u221A\\u0144\\u0147\\u2206\\xAB\\xBB\\u2026\\xA0\\u0148\\u0150\\xD5\\u0151\\u014C\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\u014D\\u0154\\u0155\\u0158\\u2039\\u203A\\u0159\\u0156\\u0157\\u0160\\u201A\\u201E\\u0161\\u015A\\u015B\\xC1\\u0164\\u0165\\xCD\\u017D\\u017E\\u016A\\xD3\\xD4\\u016B\\u016E\\xDA\\u016F\\u0170\\u0171\\u0172\\u0173\\xDD\\xFD\\u0137\\u017B\\u0141\\u017C\\u0122\\u02C7\",macintosh:\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\u2039\\u203A\\uFB01\\uFB02\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-romanian\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\u0102\\u0218\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\u0103\\u0219\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u2044\\u20AC\\u2039\\u203A\\u021A\\u021B\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\u0131\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\",\"x-mac-turkish\":\"\\xC4\\xC5\\xC7\\xC9\\xD1\\xD6\\xDC\\xE1\\xE0\\xE2\\xE4\\xE3\\xE5\\xE7\\xE9\\xE8\\xEA\\xEB\\xED\\xEC\\xEE\\xEF\\xF1\\xF3\\xF2\\xF4\\xF6\\xF5\\xFA\\xF9\\xFB\\xFC\\u2020\\xB0\\xA2\\xA3\\xA7\\u2022\\xB6\\xDF\\xAE\\xA9\\u2122\\xB4\\xA8\\u2260\\xC6\\xD8\\u221E\\xB1\\u2264\\u2265\\xA5\\xB5\\u2202\\u2211\\u220F\\u03C0\\u222B\\xAA\\xBA\\u03A9\\xE6\\xF8\\xBF\\xA1\\xAC\\u221A\\u0192\\u2248\\u2206\\xAB\\xBB\\u2026\\xA0\\xC0\\xC3\\xD5\\u0152\\u0153\\u2013\\u2014\\u201C\\u201D\\u2018\\u2019\\xF7\\u25CA\\xFF\\u0178\\u011E\\u011F\\u0130\\u0131\\u015E\\u015F\\u2021\\xB7\\u201A\\u201E\\u2030\\xC2\\xCA\\xC1\\xCB\\xC8\\xCD\\xCE\\xCF\\xCC\\xD3\\xD4\\uF8FF\\xD2\\xDA\\xDB\\xD9\\uF8A0\\u02C6\\u02DC\\xAF\\u02D8\\u02D9\\u02DA\\xB8\\u02DD\\u02DB\\u02C7\"};Fe.MACSTRING=function(e,r,t,a){var n=cr[a];if(n!==void 0){for(var s=\"\",i=0;i<t;i++){var u=e.getUint8(r+i);u<=127?s+=String.fromCharCode(u):s+=n[u&127]}return s}};var He=typeof WeakMap==\"function\"&&new WeakMap,ze,ka=function(e){if(!ze){ze={};for(var r in cr)ze[r]=new String(r)}var t=ze[e];if(t!==void 0){if(He){var a=He.get(t);if(a!==void 0)return a}var n=cr[e];if(n!==void 0){for(var s={},i=0;i<n.length;i++)s[n.charCodeAt(i)]=i+128;return He&&He.set(t,s),s}}};g.MACSTRING=function(e,r){var t=ka(r);if(t!==void 0){for(var a=[],n=0;n<e.length;n++){var s=e.charCodeAt(n);if(s>=128&&(s=t[s],s===void 0))return;a[n]=s}return a}};E.MACSTRING=function(e,r){var t=g.MACSTRING(e,r);return t!==void 0?t.length:0};function vr(e){return e>=-128&&e<=127}function Fa(e,r,t){for(var a=0,n=e.length;r<n&&a<64&&e[r]===0;)++r,++a;return t.push(128|a-1),r}function Ua(e,r,t){for(var a=0,n=e.length,s=r;s<n&&a<64;){var i=e[s];if(!vr(i)||i===0&&s+1<n&&e[s+1]===0)break;++s,++a}t.push(a-1);for(var u=r;u<s;++u)t.push(e[u]+256&255);return s}function Ca(e,r,t){for(var a=0,n=e.length,s=r;s<n&&a<64;){var i=e[s];if(i===0||vr(i)&&s+1<n&&vr(e[s+1]))break;++s,++a}t.push(64|a-1);for(var u=r;u<s;++u){var o=e[u];t.push(o+65536>>8&255,o+256&255)}return s}g.VARDELTAS=function(e){for(var r=0,t=[];r<e.length;){var a=e[r];a===0?r=Fa(e,r,t):a>=-128&&a<=127?r=Ua(e,r,t):r=Ca(e,r,t)}return t};g.INDEX=function(e){for(var r=1,t=[r],a=[],n=0;n<e.length;n+=1){var s=g.OBJECT(e[n]);Array.prototype.push.apply(a,s),r+=s.length,t.push(r)}if(a.length===0)return[0,0];for(var i=[],u=1+Math.floor(Math.log(r)/Math.log(2))/8|0,o=[void 0,g.BYTE,g.USHORT,g.UINT24,g.ULONG][u],l=0;l<t.length;l+=1){var f=o(t[l]);Array.prototype.push.apply(i,f)}return Array.prototype.concat(g.Card16(e.length),g.OffSize(u),i,a)};E.INDEX=function(e){return g.INDEX(e).length};g.DICT=function(e){for(var r=[],t=Object.keys(e),a=t.length,n=0;n<a;n+=1){var s=parseInt(t[n],0),i=e[s];r=r.concat(g.OPERAND(i.value,i.type)),r=r.concat(g.OPERATOR(s))}return r};E.DICT=function(e){return g.DICT(e).length};g.OPERATOR=function(e){return e<1200?[e]:[12,e-1200]};g.OPERAND=function(e,r){var t=[];if(Array.isArray(r))for(var a=0;a<r.length;a+=1)U.argument(e.length===r.length,\"Not enough arguments given for type\"+r),t=t.concat(g.OPERAND(e[a],r[a]));else if(r===\"SID\")t=t.concat(g.NUMBER(e));else if(r===\"offset\")t=t.concat(g.NUMBER32(e));else if(r===\"number\")t=t.concat(g.NUMBER(e));else if(r===\"real\")t=t.concat(g.REAL(e));else throw new Error(\"Unknown operand type \"+r);return t};g.OP=g.BYTE;E.OP=E.BYTE;var We=typeof WeakMap==\"function\"&&new WeakMap;g.CHARSTRING=function(e){if(We){var r=We.get(e);if(r!==void 0)return r}for(var t=[],a=e.length,n=0;n<a;n+=1){var s=e[n];t=t.concat(g[s.type](s.value))}return We&&We.set(e,t),t};E.CHARSTRING=function(e){return g.CHARSTRING(e).length};g.OBJECT=function(e){var r=g[e.type];return U.argument(r!==void 0,\"No encoding function for type \"+e.type),r(e.value)};E.OBJECT=function(e){var r=E[e.type];return U.argument(r!==void 0,\"No sizeOf function for type \"+e.type),r(e.value)};g.TABLE=function(e){for(var r=[],t=e.fields.length,a=[],n=[],s=0;s<t;s+=1){var i=e.fields[s],u=g[i.type];U.argument(u!==void 0,\"No encoding function for field type \"+i.type+\" (\"+i.name+\")\");var o=e[i.name];o===void 0&&(o=i.value);var l=u(o);i.type===\"TABLE\"?(n.push(r.length),r=r.concat([0,0]),a.push(l)):r=r.concat(l)}for(var f=0;f<a.length;f+=1){var h=n[f],p=r.length;U.argument(p<65536,\"Table \"+e.tableName+\" too big.\"),r[h]=p>>8,r[h+1]=p&255,r=r.concat(a[f])}return r};E.TABLE=function(e){for(var r=0,t=e.fields.length,a=0;a<t;a+=1){var n=e.fields[a],s=E[n.type];U.argument(s!==void 0,\"No sizeOf function for field type \"+n.type+\" (\"+n.name+\")\");var i=e[n.name];i===void 0&&(i=n.value),r+=s(i),n.type===\"TABLE\"&&(r+=2)}return r};g.RECORD=g.TABLE;E.RECORD=E.TABLE;g.LITERAL=function(e){return e};E.LITERAL=function(e){return e.length};function z(e,r,t){if(r.length&&(r[0].name!==\"coverageFormat\"||r[0].value===1))for(var a=0;a<r.length;a+=1){var n=r[a];this[n.name]=n.value}if(this.tableName=e,this.fields=r,t)for(var s=Object.keys(t),i=0;i<s.length;i+=1){var u=s[i],o=t[u];this[u]!==void 0&&(this[u]=o)}}z.prototype.encode=function(){return g.TABLE(this)};z.prototype.sizeOf=function(){return E.TABLE(this)};function Re(e,r,t){t===void 0&&(t=r.length);var a=new Array(r.length+1);a[0]={name:e+\"Count\",type:\"USHORT\",value:t};for(var n=0;n<r.length;n++)a[n+1]={name:e+n,type:\"USHORT\",value:r[n]};return a}function dr(e,r,t){var a=r.length,n=new Array(a+1);n[0]={name:e+\"Count\",type:\"USHORT\",value:a};for(var s=0;s<a;s++)n[s+1]={name:e+s,type:\"TABLE\",value:t(r[s],s)};return n}function we(e,r,t){var a=r.length,n=[];n[0]={name:e+\"Count\",type:\"USHORT\",value:a};for(var s=0;s<a;s++)n=n.concat(t(r[s],s));return n}function Ye(e){e.format===1?z.call(this,\"coverageTable\",[{name:\"coverageFormat\",type:\"USHORT\",value:1}].concat(Re(\"glyph\",e.glyphs))):e.format===2?z.call(this,\"coverageTable\",[{name:\"coverageFormat\",type:\"USHORT\",value:2}].concat(we(\"rangeRecord\",e.ranges,function(r){return[{name:\"startGlyphID\",type:\"USHORT\",value:r.start},{name:\"endGlyphID\",type:\"USHORT\",value:r.end},{name:\"startCoverageIndex\",type:\"USHORT\",value:r.index}]}))):U.assert(!1,\"Coverage format must be 1 or 2.\")}Ye.prototype=Object.create(z.prototype);Ye.prototype.constructor=Ye;function Ze(e){z.call(this,\"scriptListTable\",we(\"scriptRecord\",e,function(r,t){var a=r.script,n=a.defaultLangSys;return U.assert(!!n,\"Unable to write GSUB: script \"+r.tag+\" has no default language system.\"),[{name:\"scriptTag\"+t,type:\"TAG\",value:r.tag},{name:\"script\"+t,type:\"TABLE\",value:new z(\"scriptTable\",[{name:\"defaultLangSys\",type:\"TABLE\",value:new z(\"defaultLangSys\",[{name:\"lookupOrder\",type:\"USHORT\",value:0},{name:\"reqFeatureIndex\",type:\"USHORT\",value:n.reqFeatureIndex}].concat(Re(\"featureIndex\",n.featureIndexes)))}].concat(we(\"langSys\",a.langSysRecords,function(s,i){var u=s.langSys;return[{name:\"langSysTag\"+i,type:\"TAG\",value:s.tag},{name:\"langSys\"+i,type:\"TABLE\",value:new z(\"langSys\",[{name:\"lookupOrder\",type:\"USHORT\",value:0},{name:\"reqFeatureIndex\",type:\"USHORT\",value:u.reqFeatureIndex}].concat(Re(\"featureIndex\",u.featureIndexes)))}]})))}]}))}Ze.prototype=Object.create(z.prototype);Ze.prototype.constructor=Ze;function Qe(e){z.call(this,\"featureListTable\",we(\"featureRecord\",e,function(r,t){var a=r.feature;return[{name:\"featureTag\"+t,type:\"TAG\",value:r.tag},{name:\"feature\"+t,type:\"TABLE\",value:new z(\"featureTable\",[{name:\"featureParams\",type:\"USHORT\",value:a.featureParams}].concat(Re(\"lookupListIndex\",a.lookupListIndexes)))}]}))}Qe.prototype=Object.create(z.prototype);Qe.prototype.constructor=Qe;function Ke(e,r){z.call(this,\"lookupListTable\",dr(\"lookup\",e,function(t){var a=r[t.lookupType];return U.assert(!!a,\"Unable to write GSUB lookup type \"+t.lookupType+\" tables.\"),new z(\"lookupTable\",[{name:\"lookupType\",type:\"USHORT\",value:t.lookupType},{name:\"lookupFlag\",type:\"USHORT\",value:t.lookupFlag}].concat(dr(\"subtable\",t.subtables,a)))}))}Ke.prototype=Object.create(z.prototype);Ke.prototype.constructor=Ke;var b={Table:z,Record:z,Coverage:Ye,ScriptList:Ze,FeatureList:Qe,LookupList:Ke,ushortList:Re,tableList:dr,recordList:we};function Hr(e,r){return e.getUint8(r)}function Je(e,r){return e.getUint16(r,!1)}function Ea(e,r){return e.getInt16(r,!1)}function Fr(e,r){return e.getUint32(r,!1)}function Ct(e,r){var t=e.getInt16(r,!1),a=e.getUint16(r+2,!1);return t+a/65535}function Oa(e,r){for(var t=\"\",a=r;a<r+4;a+=1)t+=String.fromCharCode(e.getInt8(a));return t}function La(e,r,t){for(var a=0,n=0;n<t;n+=1)a<<=8,a+=e.getUint8(r+n);return a}function Ra(e,r,t){for(var a=[],n=r;n<t;n+=1)a.push(e.getUint8(n));return a}function wa(e){for(var r=\"\",t=0;t<e.length;t+=1)r+=String.fromCharCode(e[t]);return r}var Da={byte:1,uShort:2,short:2,uLong:4,fixed:4,longDateTime:8,tag:4};function v(e,r){this.data=e,this.offset=r,this.relativeOffset=0}v.prototype.parseByte=function(){var e=this.data.getUint8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e};v.prototype.parseChar=function(){var e=this.data.getInt8(this.offset+this.relativeOffset);return this.relativeOffset+=1,e};v.prototype.parseCard8=v.prototype.parseByte;v.prototype.parseUShort=function(){var e=this.data.getUint16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e};v.prototype.parseCard16=v.prototype.parseUShort;v.prototype.parseSID=v.prototype.parseUShort;v.prototype.parseOffset16=v.prototype.parseUShort;v.prototype.parseShort=function(){var e=this.data.getInt16(this.offset+this.relativeOffset);return this.relativeOffset+=2,e};v.prototype.parseF2Dot14=function(){var e=this.data.getInt16(this.offset+this.relativeOffset)/16384;return this.relativeOffset+=2,e};v.prototype.parseULong=function(){var e=Fr(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e};v.prototype.parseOffset32=v.prototype.parseULong;v.prototype.parseFixed=function(){var e=Ct(this.data,this.offset+this.relativeOffset);return this.relativeOffset+=4,e};v.prototype.parseString=function(e){var r=this.data,t=this.offset+this.relativeOffset,a=\"\";this.relativeOffset+=e;for(var n=0;n<e;n++)a+=String.fromCharCode(r.getUint8(t+n));return a};v.prototype.parseTag=function(){return this.parseString(4)};v.prototype.parseLongDateTime=function(){var e=Fr(this.data,this.offset+this.relativeOffset+4);return e-=2082844800,this.relativeOffset+=8,e};v.prototype.parseVersion=function(e){var r=Je(this.data,this.offset+this.relativeOffset),t=Je(this.data,this.offset+this.relativeOffset+2);return this.relativeOffset+=4,e===void 0&&(e=4096),r+t/e/10};v.prototype.skip=function(e,r){r===void 0&&(r=1),this.relativeOffset+=Da[e]*r};v.prototype.parseULongList=function(e){e===void 0&&(e=this.parseULong());for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint32(a),a+=4;return this.relativeOffset+=e*4,r};v.prototype.parseOffset16List=v.prototype.parseUShortList=function(e){e===void 0&&(e=this.parseUShort());for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint16(a),a+=2;return this.relativeOffset+=e*2,r};v.prototype.parseShortList=function(e){for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getInt16(a),a+=2;return this.relativeOffset+=e*2,r};v.prototype.parseByteList=function(e){for(var r=new Array(e),t=this.data,a=this.offset+this.relativeOffset,n=0;n<e;n++)r[n]=t.getUint8(a++);return this.relativeOffset+=e,r};v.prototype.parseList=function(e,r){r||(r=e,e=this.parseUShort());for(var t=new Array(e),a=0;a<e;a++)t[a]=r.call(this);return t};v.prototype.parseList32=function(e,r){r||(r=e,e=this.parseULong());for(var t=new Array(e),a=0;a<e;a++)t[a]=r.call(this);return t};v.prototype.parseRecordList=function(e,r){r||(r=e,e=this.parseUShort());for(var t=new Array(e),a=Object.keys(r),n=0;n<e;n++){for(var s={},i=0;i<a.length;i++){var u=a[i],o=r[u];s[u]=o.call(this)}t[n]=s}return t};v.prototype.parseRecordList32=function(e,r){r||(r=e,e=this.parseULong());for(var t=new Array(e),a=Object.keys(r),n=0;n<e;n++){for(var s={},i=0;i<a.length;i++){var u=a[i],o=r[u];s[u]=o.call(this)}t[n]=s}return t};v.prototype.parseStruct=function(e){if(typeof e==\"function\")return e.call(this);for(var r=Object.keys(e),t={},a=0;a<r.length;a++){var n=r[a],s=e[n];t[n]=s.call(this)}return t};v.prototype.parseValueRecord=function(e){if(e===void 0&&(e=this.parseUShort()),e!==0){var r={};return e&1&&(r.xPlacement=this.parseShort()),e&2&&(r.yPlacement=this.parseShort()),e&4&&(r.xAdvance=this.parseShort()),e&8&&(r.yAdvance=this.parseShort()),e&16&&(r.xPlaDevice=void 0,this.parseShort()),e&32&&(r.yPlaDevice=void 0,this.parseShort()),e&64&&(r.xAdvDevice=void 0,this.parseShort()),e&128&&(r.yAdvDevice=void 0,this.parseShort()),r}};v.prototype.parseValueRecordList=function(){for(var e=this.parseUShort(),r=this.parseUShort(),t=new Array(r),a=0;a<r;a++)t[a]=this.parseValueRecord(e);return t};v.prototype.parsePointer=function(e){var r=this.parseOffset16();if(r>0)return new v(this.data,this.offset+r).parseStruct(e)};v.prototype.parsePointer32=function(e){var r=this.parseOffset32();if(r>0)return new v(this.data,this.offset+r).parseStruct(e)};v.prototype.parseListOfLists=function(e){for(var r=this.parseOffset16List(),t=r.length,a=this.relativeOffset,n=new Array(t),s=0;s<t;s++){var i=r[s];if(i===0){n[s]=void 0;continue}if(this.relativeOffset=i,e){for(var u=this.parseOffset16List(),o=new Array(u.length),l=0;l<u.length;l++)this.relativeOffset=i+u[l],o[l]=e.call(this);n[s]=o}else n[s]=this.parseUShortList()}return this.relativeOffset=a,n};v.prototype.parseCoverage=function(){var e=this.offset+this.relativeOffset,r=this.parseUShort(),t=this.parseUShort();if(r===1)return{format:1,glyphs:this.parseUShortList(t)};if(r===2){for(var a=new Array(t),n=0;n<t;n++)a[n]={start:this.parseUShort(),end:this.parseUShort(),index:this.parseUShort()};return{format:2,ranges:a}}throw new Error(\"0x\"+e.toString(16)+\": Coverage format must be 1 or 2.\")};v.prototype.parseClassDef=function(){var e=this.offset+this.relativeOffset,r=this.parseUShort();if(r===1)return{format:1,startGlyph:this.parseUShort(),classes:this.parseUShortList()};if(r===2)return{format:2,ranges:this.parseRecordList({start:v.uShort,end:v.uShort,classId:v.uShort})};throw new Error(\"0x\"+e.toString(16)+\": ClassDef format must be 1 or 2.\")};v.list=function(e,r){return function(){return this.parseList(e,r)}};v.list32=function(e,r){return function(){return this.parseList32(e,r)}};v.recordList=function(e,r){return function(){return this.parseRecordList(e,r)}};v.recordList32=function(e,r){return function(){return this.parseRecordList32(e,r)}};v.pointer=function(e){return function(){return this.parsePointer(e)}};v.pointer32=function(e){return function(){return this.parsePointer32(e)}};v.tag=v.prototype.parseTag;v.byte=v.prototype.parseByte;v.uShort=v.offset16=v.prototype.parseUShort;v.uShortList=v.prototype.parseUShortList;v.uLong=v.offset32=v.prototype.parseULong;v.uLongList=v.prototype.parseULongList;v.struct=v.prototype.parseStruct;v.coverage=v.prototype.parseCoverage;v.classDef=v.prototype.parseClassDef;var zr={reserved:v.uShort,reqFeatureIndex:v.uShort,featureIndexes:v.uShortList};v.prototype.parseScriptList=function(){return this.parsePointer(v.recordList({tag:v.tag,script:v.pointer({defaultLangSys:v.pointer(zr),langSysRecords:v.recordList({tag:v.tag,langSys:v.pointer(zr)})})}))||[]};v.prototype.parseFeatureList=function(){return this.parsePointer(v.recordList({tag:v.tag,feature:v.pointer({featureParams:v.offset16,lookupListIndexes:v.uShortList})}))||[]};v.prototype.parseLookupList=function(e){return this.parsePointer(v.list(v.pointer(function(){var r=this.parseUShort();U.argument(1<=r&&r<=9,\"GPOS/GSUB lookup type \"+r+\" unknown.\");var t=this.parseUShort(),a=t&16;return{lookupType:r,lookupFlag:t,subtables:this.parseList(v.pointer(e[r])),markFilteringSet:a?this.parseUShort():void 0}})))||[]};v.prototype.parseFeatureVariationsList=function(){return this.parsePointer32(function(){var e=this.parseUShort(),r=this.parseUShort();U.argument(e===1&&r<1,\"GPOS/GSUB feature variations table unknown.\");var t=this.parseRecordList32({conditionSetOffset:v.offset32,featureTableSubstitutionOffset:v.offset32});return t})||[]};var k={getByte:Hr,getCard8:Hr,getUShort:Je,getCard16:Je,getShort:Ea,getULong:Fr,getFixed:Ct,getTag:Oa,getOffset:La,getBytes:Ra,bytesToString:wa,Parser:v};function Aa(e,r){r.parseUShort(),e.length=r.parseULong(),e.language=r.parseULong();var t;e.groupCount=t=r.parseULong(),e.glyphIndexMap={};for(var a=0;a<t;a+=1)for(var n=r.parseULong(),s=r.parseULong(),i=r.parseULong(),u=n;u<=s;u+=1)e.glyphIndexMap[u]=i,i++}function Ba(e,r,t,a,n){e.length=r.parseUShort(),e.language=r.parseUShort();var s;e.segCount=s=r.parseUShort()>>1,r.skip(\"uShort\",3),e.glyphIndexMap={};for(var i=new k.Parser(t,a+n+14),u=new k.Parser(t,a+n+16+s*2),o=new k.Parser(t,a+n+16+s*4),l=new k.Parser(t,a+n+16+s*6),f=a+n+16+s*8,h=0;h<s-1;h+=1)for(var p=void 0,c=i.parseUShort(),d=u.parseUShort(),x=o.parseShort(),m=l.parseUShort(),y=d;y<=c;y+=1)m!==0?(f=l.offset+l.relativeOffset-2,f+=m,f+=(y-d)*2,p=k.getUShort(t,f),p!==0&&(p=p+x&65535)):p=y+x&65535,e.glyphIndexMap[y]=p}function Ia(e,r){var t={};t.version=k.getUShort(e,r),U.argument(t.version===0,\"cmap table version should be 0.\"),t.numTables=k.getUShort(e,r+2);for(var a=-1,n=t.numTables-1;n>=0;n-=1){var s=k.getUShort(e,r+4+n*8),i=k.getUShort(e,r+4+n*8+2);if(s===3&&(i===0||i===1||i===10)||s===0&&(i===0||i===1||i===2||i===3||i===4)){a=k.getULong(e,r+4+n*8+4);break}}if(a===-1)throw new Error(\"No valid cmap sub-tables found.\");var u=new k.Parser(e,r+a);if(t.format=u.parseUShort(),t.format===12)Aa(t,u);else if(t.format===4)Ba(t,u,e,r,a);else throw new Error(\"Only format 4 and 12 cmap tables are supported (found format \"+t.format+\").\");return t}function Ma(e,r,t){e.segments.push({end:r,start:r,delta:-(r-t),offset:0,glyphIndex:t})}function Pa(e){e.segments.push({end:65535,start:65535,delta:1,offset:0})}function Ga(e){var r=!0,t;for(t=e.length-1;t>0;t-=1){var a=e.get(t);if(a.unicode>65535){console.log(\"Adding CMAP format 12 (needed!)\"),r=!1;break}}var n=[{name:\"version\",type:\"USHORT\",value:0},{name:\"numTables\",type:\"USHORT\",value:r?1:2},{name:\"platformID\",type:\"USHORT\",value:3},{name:\"encodingID\",type:\"USHORT\",value:1},{name:\"offset\",type:\"ULONG\",value:r?12:12+8}];r||(n=n.concat([{name:\"cmap12PlatformID\",type:\"USHORT\",value:3},{name:\"cmap12EncodingID\",type:\"USHORT\",value:10},{name:\"cmap12Offset\",type:\"ULONG\",value:0}])),n=n.concat([{name:\"format\",type:\"USHORT\",value:4},{name:\"cmap4Length\",type:\"USHORT\",value:0},{name:\"language\",type:\"USHORT\",value:0},{name:\"segCountX2\",type:\"USHORT\",value:0},{name:\"searchRange\",type:\"USHORT\",value:0},{name:\"entrySelector\",type:\"USHORT\",value:0},{name:\"rangeShift\",type:\"USHORT\",value:0}]);var s=new b.Table(\"cmap\",n);for(s.segments=[],t=0;t<e.length;t+=1){for(var i=e.get(t),u=0;u<i.unicodes.length;u+=1)Ma(s,i.unicodes[u],t);s.segments=s.segments.sort(function(C,S){return C.start-S.start})}Pa(s);var o=s.segments.length,l=0,f=[],h=[],p=[],c=[],d=[],x=[];for(t=0;t<o;t+=1){var m=s.segments[t];m.end<=65535&&m.start<=65535?(f=f.concat({name:\"end_\"+t,type:\"USHORT\",value:m.end}),h=h.concat({name:\"start_\"+t,type:\"USHORT\",value:m.start}),p=p.concat({name:\"idDelta_\"+t,type:\"SHORT\",value:m.delta}),c=c.concat({name:\"idRangeOffset_\"+t,type:\"USHORT\",value:m.offset}),m.glyphId!==void 0&&(d=d.concat({name:\"glyph_\"+t,type:\"USHORT\",value:m.glyphId}))):l+=1,!r&&m.glyphIndex!==void 0&&(x=x.concat({name:\"cmap12Start_\"+t,type:\"ULONG\",value:m.start}),x=x.concat({name:\"cmap12End_\"+t,type:\"ULONG\",value:m.end}),x=x.concat({name:\"cmap12Glyph_\"+t,type:\"ULONG\",value:m.glyphIndex}))}if(s.segCountX2=(o-l)*2,s.searchRange=Math.pow(2,Math.floor(Math.log(o-l)/Math.log(2)))*2,s.entrySelector=Math.log(s.searchRange/2)/Math.log(2),s.rangeShift=s.segCountX2-s.searchRange,s.fields=s.fields.concat(f),s.fields.push({name:\"reservedPad\",type:\"USHORT\",value:0}),s.fields=s.fields.concat(h),s.fields=s.fields.concat(p),s.fields=s.fields.concat(c),s.fields=s.fields.concat(d),s.cmap4Length=14+f.length*2+2+h.length*2+p.length*2+c.length*2+d.length*2,!r){var y=16+x.length*4;s.cmap12Offset=12+2*2+4+s.cmap4Length,s.fields=s.fields.concat([{name:\"cmap12Format\",type:\"USHORT\",value:12},{name:\"cmap12Reserved\",type:\"USHORT\",value:0},{name:\"cmap12Length\",type:\"ULONG\",value:y},{name:\"cmap12Language\",type:\"ULONG\",value:0},{name:\"cmap12nGroups\",type:\"ULONG\",value:x.length/3}]),s.fields=s.fields.concat(x)}return s}var Et={parse:Ia,make:Ga},qe=[\".notdef\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quoteright\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"quoteleft\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"exclamdown\",\"cent\",\"sterling\",\"fraction\",\"yen\",\"florin\",\"section\",\"currency\",\"quotesingle\",\"quotedblleft\",\"guillemotleft\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"endash\",\"dagger\",\"daggerdbl\",\"periodcentered\",\"paragraph\",\"bullet\",\"quotesinglbase\",\"quotedblbase\",\"quotedblright\",\"guillemotright\",\"ellipsis\",\"perthousand\",\"questiondown\",\"grave\",\"acute\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"dieresis\",\"ring\",\"cedilla\",\"hungarumlaut\",\"ogonek\",\"caron\",\"emdash\",\"AE\",\"ordfeminine\",\"Lslash\",\"Oslash\",\"OE\",\"ordmasculine\",\"ae\",\"dotlessi\",\"lslash\",\"oslash\",\"oe\",\"germandbls\",\"onesuperior\",\"logicalnot\",\"mu\",\"trademark\",\"Eth\",\"onehalf\",\"plusminus\",\"Thorn\",\"onequarter\",\"divide\",\"brokenbar\",\"degree\",\"thorn\",\"threequarters\",\"twosuperior\",\"registered\",\"minus\",\"eth\",\"multiply\",\"threesuperior\",\"copyright\",\"Aacute\",\"Acircumflex\",\"Adieresis\",\"Agrave\",\"Aring\",\"Atilde\",\"Ccedilla\",\"Eacute\",\"Ecircumflex\",\"Edieresis\",\"Egrave\",\"Iacute\",\"Icircumflex\",\"Idieresis\",\"Igrave\",\"Ntilde\",\"Oacute\",\"Ocircumflex\",\"Odieresis\",\"Ograve\",\"Otilde\",\"Scaron\",\"Uacute\",\"Ucircumflex\",\"Udieresis\",\"Ugrave\",\"Yacute\",\"Ydieresis\",\"Zcaron\",\"aacute\",\"acircumflex\",\"adieresis\",\"agrave\",\"aring\",\"atilde\",\"ccedilla\",\"eacute\",\"ecircumflex\",\"edieresis\",\"egrave\",\"iacute\",\"icircumflex\",\"idieresis\",\"igrave\",\"ntilde\",\"oacute\",\"ocircumflex\",\"odieresis\",\"ograve\",\"otilde\",\"scaron\",\"uacute\",\"ucircumflex\",\"udieresis\",\"ugrave\",\"yacute\",\"ydieresis\",\"zcaron\",\"exclamsmall\",\"Hungarumlautsmall\",\"dollaroldstyle\",\"dollarsuperior\",\"ampersandsmall\",\"Acutesmall\",\"parenleftsuperior\",\"parenrightsuperior\",\"266 ff\",\"onedotenleader\",\"zerooldstyle\",\"oneoldstyle\",\"twooldstyle\",\"threeoldstyle\",\"fouroldstyle\",\"fiveoldstyle\",\"sixoldstyle\",\"sevenoldstyle\",\"eightoldstyle\",\"nineoldstyle\",\"commasuperior\",\"threequartersemdash\",\"periodsuperior\",\"questionsmall\",\"asuperior\",\"bsuperior\",\"centsuperior\",\"dsuperior\",\"esuperior\",\"isuperior\",\"lsuperior\",\"msuperior\",\"nsuperior\",\"osuperior\",\"rsuperior\",\"ssuperior\",\"tsuperior\",\"ff\",\"ffi\",\"ffl\",\"parenleftinferior\",\"parenrightinferior\",\"Circumflexsmall\",\"hyphensuperior\",\"Gravesmall\",\"Asmall\",\"Bsmall\",\"Csmall\",\"Dsmall\",\"Esmall\",\"Fsmall\",\"Gsmall\",\"Hsmall\",\"Ismall\",\"Jsmall\",\"Ksmall\",\"Lsmall\",\"Msmall\",\"Nsmall\",\"Osmall\",\"Psmall\",\"Qsmall\",\"Rsmall\",\"Ssmall\",\"Tsmall\",\"Usmall\",\"Vsmall\",\"Wsmall\",\"Xsmall\",\"Ysmall\",\"Zsmall\",\"colonmonetary\",\"onefitted\",\"rupiah\",\"Tildesmall\",\"exclamdownsmall\",\"centoldstyle\",\"Lslashsmall\",\"Scaronsmall\",\"Zcaronsmall\",\"Dieresissmall\",\"Brevesmall\",\"Caronsmall\",\"Dotaccentsmall\",\"Macronsmall\",\"figuredash\",\"hypheninferior\",\"Ogoneksmall\",\"Ringsmall\",\"Cedillasmall\",\"questiondownsmall\",\"oneeighth\",\"threeeighths\",\"fiveeighths\",\"seveneighths\",\"onethird\",\"twothirds\",\"zerosuperior\",\"foursuperior\",\"fivesuperior\",\"sixsuperior\",\"sevensuperior\",\"eightsuperior\",\"ninesuperior\",\"zeroinferior\",\"oneinferior\",\"twoinferior\",\"threeinferior\",\"fourinferior\",\"fiveinferior\",\"sixinferior\",\"seveninferior\",\"eightinferior\",\"nineinferior\",\"centinferior\",\"dollarinferior\",\"periodinferior\",\"commainferior\",\"Agravesmall\",\"Aacutesmall\",\"Acircumflexsmall\",\"Atildesmall\",\"Adieresissmall\",\"Aringsmall\",\"AEsmall\",\"Ccedillasmall\",\"Egravesmall\",\"Eacutesmall\",\"Ecircumflexsmall\",\"Edieresissmall\",\"Igravesmall\",\"Iacutesmall\",\"Icircumflexsmall\",\"Idieresissmall\",\"Ethsmall\",\"Ntildesmall\",\"Ogravesmall\",\"Oacutesmall\",\"Ocircumflexsmall\",\"Otildesmall\",\"Odieresissmall\",\"OEsmall\",\"Oslashsmall\",\"Ugravesmall\",\"Uacutesmall\",\"Ucircumflexsmall\",\"Udieresissmall\",\"Yacutesmall\",\"Thornsmall\",\"Ydieresissmall\",\"001.000\",\"001.001\",\"001.002\",\"001.003\",\"Black\",\"Bold\",\"Book\",\"Light\",\"Medium\",\"Regular\",\"Roman\",\"Semibold\"],Na=[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quoteright\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"quoteleft\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"exclamdown\",\"cent\",\"sterling\",\"fraction\",\"yen\",\"florin\",\"section\",\"currency\",\"quotesingle\",\"quotedblleft\",\"guillemotleft\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"\",\"endash\",\"dagger\",\"daggerdbl\",\"periodcentered\",\"\",\"paragraph\",\"bullet\",\"quotesinglbase\",\"quotedblbase\",\"quotedblright\",\"guillemotright\",\"ellipsis\",\"perthousand\",\"\",\"questiondown\",\"\",\"grave\",\"acute\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"dieresis\",\"\",\"ring\",\"cedilla\",\"\",\"hungarumlaut\",\"ogonek\",\"caron\",\"emdash\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"AE\",\"\",\"ordfeminine\",\"\",\"\",\"\",\"\",\"Lslash\",\"Oslash\",\"OE\",\"ordmasculine\",\"\",\"\",\"\",\"\",\"\",\"ae\",\"\",\"\",\"\",\"dotlessi\",\"\",\"\",\"lslash\",\"oslash\",\"oe\",\"germandbls\"],Ha=[\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"space\",\"exclamsmall\",\"Hungarumlautsmall\",\"\",\"dollaroldstyle\",\"dollarsuperior\",\"ampersandsmall\",\"Acutesmall\",\"parenleftsuperior\",\"parenrightsuperior\",\"twodotenleader\",\"onedotenleader\",\"comma\",\"hyphen\",\"period\",\"fraction\",\"zerooldstyle\",\"oneoldstyle\",\"twooldstyle\",\"threeoldstyle\",\"fouroldstyle\",\"fiveoldstyle\",\"sixoldstyle\",\"sevenoldstyle\",\"eightoldstyle\",\"nineoldstyle\",\"colon\",\"semicolon\",\"commasuperior\",\"threequartersemdash\",\"periodsuperior\",\"questionsmall\",\"\",\"asuperior\",\"bsuperior\",\"centsuperior\",\"dsuperior\",\"esuperior\",\"\",\"\",\"isuperior\",\"\",\"\",\"lsuperior\",\"msuperior\",\"nsuperior\",\"osuperior\",\"\",\"\",\"rsuperior\",\"ssuperior\",\"tsuperior\",\"\",\"ff\",\"fi\",\"fl\",\"ffi\",\"ffl\",\"parenleftinferior\",\"\",\"parenrightinferior\",\"Circumflexsmall\",\"hyphensuperior\",\"Gravesmall\",\"Asmall\",\"Bsmall\",\"Csmall\",\"Dsmall\",\"Esmall\",\"Fsmall\",\"Gsmall\",\"Hsmall\",\"Ismall\",\"Jsmall\",\"Ksmall\",\"Lsmall\",\"Msmall\",\"Nsmall\",\"Osmall\",\"Psmall\",\"Qsmall\",\"Rsmall\",\"Ssmall\",\"Tsmall\",\"Usmall\",\"Vsmall\",\"Wsmall\",\"Xsmall\",\"Ysmall\",\"Zsmall\",\"colonmonetary\",\"onefitted\",\"rupiah\",\"Tildesmall\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"\",\"exclamdownsmall\",\"centoldstyle\",\"Lslashsmall\",\"\",\"\",\"Scaronsmall\",\"Zcaronsmall\",\"Dieresissmall\",\"Brevesmall\",\"Caronsmall\",\"\",\"Dotaccentsmall\",\"\",\"\",\"Macronsmall\",\"\",\"\",\"figuredash\",\"hypheninferior\",\"\",\"\",\"Ogoneksmall\",\"Ringsmall\",\"Cedillasmall\",\"\",\"\",\"\",\"onequarter\",\"onehalf\",\"threequarters\",\"questiondownsmall\",\"oneeighth\",\"threeeighths\",\"fiveeighths\",\"seveneighths\",\"onethird\",\"twothirds\",\"\",\"\",\"zerosuperior\",\"onesuperior\",\"twosuperior\",\"threesuperior\",\"foursuperior\",\"fivesuperior\",\"sixsuperior\",\"sevensuperior\",\"eightsuperior\",\"ninesuperior\",\"zeroinferior\",\"oneinferior\",\"twoinferior\",\"threeinferior\",\"fourinferior\",\"fiveinferior\",\"sixinferior\",\"seveninferior\",\"eightinferior\",\"nineinferior\",\"centinferior\",\"dollarinferior\",\"periodinferior\",\"commainferior\",\"Agravesmall\",\"Aacutesmall\",\"Acircumflexsmall\",\"Atildesmall\",\"Adieresissmall\",\"Aringsmall\",\"AEsmall\",\"Ccedillasmall\",\"Egravesmall\",\"Eacutesmall\",\"Ecircumflexsmall\",\"Edieresissmall\",\"Igravesmall\",\"Iacutesmall\",\"Icircumflexsmall\",\"Idieresissmall\",\"Ethsmall\",\"Ntildesmall\",\"Ogravesmall\",\"Oacutesmall\",\"Ocircumflexsmall\",\"Otildesmall\",\"Odieresissmall\",\"OEsmall\",\"Oslashsmall\",\"Ugravesmall\",\"Uacutesmall\",\"Ucircumflexsmall\",\"Udieresissmall\",\"Yacutesmall\",\"Thornsmall\",\"Ydieresissmall\"],xe=[\".notdef\",\".null\",\"nonmarkingreturn\",\"space\",\"exclam\",\"quotedbl\",\"numbersign\",\"dollar\",\"percent\",\"ampersand\",\"quotesingle\",\"parenleft\",\"parenright\",\"asterisk\",\"plus\",\"comma\",\"hyphen\",\"period\",\"slash\",\"zero\",\"one\",\"two\",\"three\",\"four\",\"five\",\"six\",\"seven\",\"eight\",\"nine\",\"colon\",\"semicolon\",\"less\",\"equal\",\"greater\",\"question\",\"at\",\"A\",\"B\",\"C\",\"D\",\"E\",\"F\",\"G\",\"H\",\"I\",\"J\",\"K\",\"L\",\"M\",\"N\",\"O\",\"P\",\"Q\",\"R\",\"S\",\"T\",\"U\",\"V\",\"W\",\"X\",\"Y\",\"Z\",\"bracketleft\",\"backslash\",\"bracketright\",\"asciicircum\",\"underscore\",\"grave\",\"a\",\"b\",\"c\",\"d\",\"e\",\"f\",\"g\",\"h\",\"i\",\"j\",\"k\",\"l\",\"m\",\"n\",\"o\",\"p\",\"q\",\"r\",\"s\",\"t\",\"u\",\"v\",\"w\",\"x\",\"y\",\"z\",\"braceleft\",\"bar\",\"braceright\",\"asciitilde\",\"Adieresis\",\"Aring\",\"Ccedilla\",\"Eacute\",\"Ntilde\",\"Odieresis\",\"Udieresis\",\"aacute\",\"agrave\",\"acircumflex\",\"adieresis\",\"atilde\",\"aring\",\"ccedilla\",\"eacute\",\"egrave\",\"ecircumflex\",\"edieresis\",\"iacute\",\"igrave\",\"icircumflex\",\"idieresis\",\"ntilde\",\"oacute\",\"ograve\",\"ocircumflex\",\"odieresis\",\"otilde\",\"uacute\",\"ugrave\",\"ucircumflex\",\"udieresis\",\"dagger\",\"degree\",\"cent\",\"sterling\",\"section\",\"bullet\",\"paragraph\",\"germandbls\",\"registered\",\"copyright\",\"trademark\",\"acute\",\"dieresis\",\"notequal\",\"AE\",\"Oslash\",\"infinity\",\"plusminus\",\"lessequal\",\"greaterequal\",\"yen\",\"mu\",\"partialdiff\",\"summation\",\"product\",\"pi\",\"integral\",\"ordfeminine\",\"ordmasculine\",\"Omega\",\"ae\",\"oslash\",\"questiondown\",\"exclamdown\",\"logicalnot\",\"radical\",\"florin\",\"approxequal\",\"Delta\",\"guillemotleft\",\"guillemotright\",\"ellipsis\",\"nonbreakingspace\",\"Agrave\",\"Atilde\",\"Otilde\",\"OE\",\"oe\",\"endash\",\"emdash\",\"quotedblleft\",\"quotedblright\",\"quoteleft\",\"quoteright\",\"divide\",\"lozenge\",\"ydieresis\",\"Ydieresis\",\"fraction\",\"currency\",\"guilsinglleft\",\"guilsinglright\",\"fi\",\"fl\",\"daggerdbl\",\"periodcentered\",\"quotesinglbase\",\"quotedblbase\",\"perthousand\",\"Acircumflex\",\"Ecircumflex\",\"Aacute\",\"Edieresis\",\"Egrave\",\"Iacute\",\"Icircumflex\",\"Idieresis\",\"Igrave\",\"Oacute\",\"Ocircumflex\",\"apple\",\"Ograve\",\"Uacute\",\"Ucircumflex\",\"Ugrave\",\"dotlessi\",\"circumflex\",\"tilde\",\"macron\",\"breve\",\"dotaccent\",\"ring\",\"cedilla\",\"hungarumlaut\",\"ogonek\",\"caron\",\"Lslash\",\"lslash\",\"Scaron\",\"scaron\",\"Zcaron\",\"zcaron\",\"brokenbar\",\"Eth\",\"eth\",\"Yacute\",\"yacute\",\"Thorn\",\"thorn\",\"minus\",\"multiply\",\"onesuperior\",\"twosuperior\",\"threesuperior\",\"onehalf\",\"onequarter\",\"threequarters\",\"franc\",\"Gbreve\",\"gbreve\",\"Idotaccent\",\"Scedilla\",\"scedilla\",\"Cacute\",\"cacute\",\"Ccaron\",\"ccaron\",\"dcroat\"];function Ot(e){this.font=e}Ot.prototype.charToGlyphIndex=function(e){var r=e.codePointAt(0),t=this.font.glyphs;if(t){for(var a=0;a<t.length;a+=1)for(var n=t.get(a),s=0;s<n.unicodes.length;s+=1)if(n.unicodes[s]===r)return a}return null};function Lt(e){this.cmap=e}Lt.prototype.charToGlyphIndex=function(e){return this.cmap.glyphIndexMap[e.codePointAt(0)]||0};function je(e,r){this.encoding=e,this.charset=r}je.prototype.charToGlyphIndex=function(e){var r=e.codePointAt(0),t=this.encoding[r];return this.charset.indexOf(t)};function Ur(e){switch(e.version){case 1:this.names=xe.slice();break;case 2:this.names=new Array(e.numberOfGlyphs);for(var r=0;r<e.numberOfGlyphs;r++)e.glyphNameIndex[r]<xe.length?this.names[r]=xe[e.glyphNameIndex[r]]:this.names[r]=e.names[e.glyphNameIndex[r]-xe.length];break;case 2.5:this.names=new Array(e.numberOfGlyphs);for(var t=0;t<e.numberOfGlyphs;t++)this.names[t]=xe[t+e.glyphNameIndex[t]];break;case 3:this.names=[];break;default:this.names=[];break}}Ur.prototype.nameToGlyphIndex=function(e){return this.names.indexOf(e)};Ur.prototype.glyphIndexToName=function(e){return this.names[e]};function za(e){for(var r,t=e.tables.cmap.glyphIndexMap,a=Object.keys(t),n=0;n<a.length;n+=1){var s=a[n],i=t[s];r=e.glyphs.get(i),r.addUnicode(parseInt(s))}for(var u=0;u<e.glyphs.length;u+=1)r=e.glyphs.get(u),e.cffEncoding?e.isCIDFont?r.name=\"gid\"+u:r.name=e.cffEncoding.charset[u]:e.glyphNames.names&&(r.name=e.glyphNames.glyphIndexToName(u))}function Wa(e){e._IndexToUnicodeMap={};for(var r=e.tables.cmap.glyphIndexMap,t=Object.keys(r),a=0;a<t.length;a+=1){var n=t[a],s=r[n];e._IndexToUnicodeMap[s]===void 0?e._IndexToUnicodeMap[s]={unicodes:[parseInt(n)]}:e._IndexToUnicodeMap[s].unicodes.push(parseInt(n))}}function _a(e,r){r.lowMemory?Wa(e):za(e)}function Va(e,r,t,a,n){e.beginPath(),e.moveTo(r,t),e.lineTo(a,n),e.stroke()}var ye={line:Va};function qa(e,r){var t=r||new P;return{configurable:!0,get:function(){return typeof t==\"function\"&&(t=t()),t},set:function(a){t=a}}}function Q(e){this.bindConstructorValues(e)}Q.prototype.bindConstructorValues=function(e){this.index=e.index||0,this.name=e.name||null,this.unicode=e.unicode||void 0,this.unicodes=e.unicodes||e.unicode!==void 0?[e.unicode]:[],\"xMin\"in e&&(this.xMin=e.xMin),\"yMin\"in e&&(this.yMin=e.yMin),\"xMax\"in e&&(this.xMax=e.xMax),\"yMax\"in e&&(this.yMax=e.yMax),\"advanceWidth\"in e&&(this.advanceWidth=e.advanceWidth),Object.defineProperty(this,\"path\",qa(this,e.path))};Q.prototype.addUnicode=function(e){this.unicodes.length===0&&(this.unicode=e),this.unicodes.push(e)};Q.prototype.getBoundingBox=function(){return this.path.getBoundingBox()};Q.prototype.getPath=function(e,r,t,a,n){e=e!==void 0?e:0,r=r!==void 0?r:0,t=t!==void 0?t:72;var s,i;a||(a={});var u=a.xScale,o=a.yScale;if(a.hinting&&n&&n.hinting&&(i=this.path&&n.hinting.exec(this,t)),i)s=n.hinting.getCommands(i),e=Math.round(e),r=Math.round(r),u=o=1;else{s=this.path.commands;var l=1/(this.path.unitsPerEm||1e3)*t;u===void 0&&(u=l),o===void 0&&(o=l)}for(var f=new P,h=0;h<s.length;h+=1){var p=s[h];p.type===\"M\"?f.moveTo(e+p.x*u,r+-p.y*o):p.type===\"L\"?f.lineTo(e+p.x*u,r+-p.y*o):p.type===\"Q\"?f.quadraticCurveTo(e+p.x1*u,r+-p.y1*o,e+p.x*u,r+-p.y*o):p.type===\"C\"?f.curveTo(e+p.x1*u,r+-p.y1*o,e+p.x2*u,r+-p.y2*o,e+p.x*u,r+-p.y*o):p.type===\"Z\"&&f.closePath()}return f};Q.prototype.getContours=function(){if(this.points===void 0)return[];for(var e=[],r=[],t=0;t<this.points.length;t+=1){var a=this.points[t];r.push(a),a.lastPointOfContour&&(e.push(r),r=[])}return U.argument(r.length===0,\"There are still points left in the current contour.\"),e};Q.prototype.getMetrics=function(){for(var e=this.path.commands,r=[],t=[],a=0;a<e.length;a+=1){var n=e[a];n.type!==\"Z\"&&(r.push(n.x),t.push(n.y)),(n.type===\"Q\"||n.type===\"C\")&&(r.push(n.x1),t.push(n.y1)),n.type===\"C\"&&(r.push(n.x2),t.push(n.y2))}var s={xMin:Math.min.apply(null,r),yMin:Math.min.apply(null,t),xMax:Math.max.apply(null,r),yMax:Math.max.apply(null,t),leftSideBearing:this.leftSideBearing};return isFinite(s.xMin)||(s.xMin=0),isFinite(s.xMax)||(s.xMax=this.advanceWidth),isFinite(s.yMin)||(s.yMin=0),isFinite(s.yMax)||(s.yMax=0),s.rightSideBearing=this.advanceWidth-s.leftSideBearing-(s.xMax-s.xMin),s};Q.prototype.draw=function(e,r,t,a,n){this.getPath(r,t,a,n).draw(e)};Q.prototype.drawPoints=function(e,r,t,a){function n(h,p,c,d){e.beginPath();for(var x=0;x<h.length;x+=1)e.moveTo(p+h[x].x*d,c+h[x].y*d),e.arc(p+h[x].x*d,c+h[x].y*d,2,0,Math.PI*2,!1);e.closePath(),e.fill()}r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:24;for(var s=1/this.path.unitsPerEm*a,i=[],u=[],o=this.path,l=0;l<o.commands.length;l+=1){var f=o.commands[l];f.x!==void 0&&i.push({x:f.x,y:-f.y}),f.x1!==void 0&&u.push({x:f.x1,y:-f.y1}),f.x2!==void 0&&u.push({x:f.x2,y:-f.y2})}e.fillStyle=\"blue\",n(i,r,t,s),e.fillStyle=\"red\",n(u,r,t,s)};Q.prototype.drawMetrics=function(e,r,t,a){var n;r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:24,n=1/this.path.unitsPerEm*a,e.lineWidth=1,e.strokeStyle=\"black\",ye.line(e,r,-1e4,r,1e4),ye.line(e,-1e4,t,1e4,t);var s=this.xMin||0,i=this.yMin||0,u=this.xMax||0,o=this.yMax||0,l=this.advanceWidth||0;e.strokeStyle=\"blue\",ye.line(e,r+s*n,-1e4,r+s*n,1e4),ye.line(e,r+u*n,-1e4,r+u*n,1e4),ye.line(e,-1e4,t+-i*n,1e4,t+-i*n),ye.line(e,-1e4,t+-o*n,1e4,t+-o*n),e.strokeStyle=\"green\",ye.line(e,r+l*n,-1e4,r+l*n,1e4)};function _e(e,r,t){Object.defineProperty(e,r,{get:function(){return e.path,e[t]},set:function(a){e[t]=a},enumerable:!0,configurable:!0})}function Cr(e,r){if(this.font=e,this.glyphs={},Array.isArray(r))for(var t=0;t<r.length;t++){var a=r[t];a.path.unitsPerEm=e.unitsPerEm,this.glyphs[t]=a}this.length=r&&r.length||0}Cr.prototype.get=function(e){if(this.glyphs[e]===void 0){this.font._push(e),typeof this.glyphs[e]==\"function\"&&(this.glyphs[e]=this.glyphs[e]());var r=this.glyphs[e],t=this.font._IndexToUnicodeMap[e];if(t)for(var a=0;a<t.unicodes.length;a++)r.addUnicode(t.unicodes[a]);this.font.cffEncoding?this.font.isCIDFont?r.name=\"gid\"+e:r.name=this.font.cffEncoding.charset[e]:this.font.glyphNames.names&&(r.name=this.font.glyphNames.glyphIndexToName(e)),this.glyphs[e].advanceWidth=this.font._hmtxTableData[e].advanceWidth,this.glyphs[e].leftSideBearing=this.font._hmtxTableData[e].leftSideBearing}else typeof this.glyphs[e]==\"function\"&&(this.glyphs[e]=this.glyphs[e]());return this.glyphs[e]};Cr.prototype.push=function(e,r){this.glyphs[e]=r,this.length++};function Xa(e,r){return new Q({index:r,font:e})}function Ya(e,r,t,a,n,s){return function(){var i=new Q({index:r,font:e});return i.path=function(){t(i,a,n);var u=s(e.glyphs,i);return u.unitsPerEm=e.unitsPerEm,u},_e(i,\"xMin\",\"_xMin\"),_e(i,\"xMax\",\"_xMax\"),_e(i,\"yMin\",\"_yMin\"),_e(i,\"yMax\",\"_yMax\"),i}}function Za(e,r,t,a){return function(){var n=new Q({index:r,font:e});return n.path=function(){var s=t(e,n,a);return s.unitsPerEm=e.unitsPerEm,s},n}}var ue={GlyphSet:Cr,glyphLoader:Xa,ttfGlyphLoader:Ya,cffGlyphLoader:Za};function Rt(e,r){if(e===r)return!0;if(Array.isArray(e)&&Array.isArray(r)){if(e.length!==r.length)return!1;for(var t=0;t<e.length;t+=1)if(!Rt(e[t],r[t]))return!1;return!0}else return!1}function gr(e){var r;return e.length<1240?r=107:e.length<33900?r=1131:r=32768,r}function ve(e,r,t){var a=[],n=[],s=k.getCard16(e,r),i,u;if(s!==0){var o=k.getByte(e,r+2);i=r+(s+1)*o+2;for(var l=r+3,f=0;f<s+1;f+=1)a.push(k.getOffset(e,l,o)),l+=o;u=i+a[s]}else u=r+2;for(var h=0;h<a.length-1;h+=1){var p=k.getBytes(e,i+a[h],i+a[h+1]);t&&(p=t(p)),n.push(p)}return{objects:n,startOffset:r,endOffset:u}}function Qa(e,r){var t=[],a=k.getCard16(e,r),n,s;if(a!==0){var i=k.getByte(e,r+2);n=r+(a+1)*i+2;for(var u=r+3,o=0;o<a+1;o+=1)t.push(k.getOffset(e,u,i)),u+=i;s=n+t[a]}else s=r+2;return{offsets:t,startOffset:r,endOffset:s}}function Ka(e,r,t,a,n){var s=k.getCard16(t,a),i=0;if(s!==0){var u=k.getByte(t,a+2);i=a+(s+1)*u+2}var o=k.getBytes(t,i+r[e],i+r[e+1]);return n&&(o=n(o)),o}function Ja(e){for(var r=\"\",t=15,a=[\"0\",\"1\",\"2\",\"3\",\"4\",\"5\",\"6\",\"7\",\"8\",\"9\",\".\",\"E\",\"E-\",null,\"-\"];;){var n=e.parseByte(),s=n>>4,i=n&15;if(s===t||(r+=a[s],i===t))break;r+=a[i]}return parseFloat(r)}function ja(e,r){var t,a,n,s;if(r===28)return t=e.parseByte(),a=e.parseByte(),t<<8|a;if(r===29)return t=e.parseByte(),a=e.parseByte(),n=e.parseByte(),s=e.parseByte(),t<<24|a<<16|n<<8|s;if(r===30)return Ja(e);if(r>=32&&r<=246)return r-139;if(r>=247&&r<=250)return t=e.parseByte(),(r-247)*256+t+108;if(r>=251&&r<=254)return t=e.parseByte(),-(r-251)*256-t-108;throw new Error(\"Invalid b0 \"+r)}function $a(e){for(var r={},t=0;t<e.length;t+=1){var a=e[t][0],n=e[t][1],s=void 0;if(n.length===1?s=n[0]:s=n,r.hasOwnProperty(a)&&!isNaN(r[a]))throw new Error(\"Object \"+r+\" already has key \"+a);r[a]=s}return r}function wt(e,r,t){r=r!==void 0?r:0;var a=new k.Parser(e,r),n=[],s=[];for(t=t!==void 0?t:e.length;a.relativeOffset<t;){var i=a.parseByte();i<=21?(i===12&&(i=1200+a.parseByte()),n.push([i,s]),s=[]):s.push(ja(a,i))}return $a(n)}function Oe(e,r){return r<=390?r=qe[r]:r=e[r-391],r}function Dt(e,r,t){for(var a={},n,s=0;s<r.length;s+=1){var i=r[s];if(Array.isArray(i.type)){var u=[];u.length=i.type.length;for(var o=0;o<i.type.length;o++)n=e[i.op]!==void 0?e[i.op][o]:void 0,n===void 0&&(n=i.value!==void 0&&i.value[o]!==void 0?i.value[o]:null),i.type[o]===\"SID\"&&(n=Oe(t,n)),u[o]=n;a[i.name]=u}else n=e[i.op],n===void 0&&(n=i.value!==void 0?i.value:null),i.type===\"SID\"&&(n=Oe(t,n)),a[i.name]=n}return a}function en(e,r){var t={};return t.formatMajor=k.getCard8(e,r),t.formatMinor=k.getCard8(e,r+1),t.size=k.getCard8(e,r+2),t.offsetSize=k.getCard8(e,r+3),t.startOffset=r,t.endOffset=r+4,t}var At=[{name:\"version\",op:0,type:\"SID\"},{name:\"notice\",op:1,type:\"SID\"},{name:\"copyright\",op:1200,type:\"SID\"},{name:\"fullName\",op:2,type:\"SID\"},{name:\"familyName\",op:3,type:\"SID\"},{name:\"weight\",op:4,type:\"SID\"},{name:\"isFixedPitch\",op:1201,type:\"number\",value:0},{name:\"italicAngle\",op:1202,type:\"number\",value:0},{name:\"underlinePosition\",op:1203,type:\"number\",value:-100},{name:\"underlineThickness\",op:1204,type:\"number\",value:50},{name:\"paintType\",op:1205,type:\"number\",value:0},{name:\"charstringType\",op:1206,type:\"number\",value:2},{name:\"fontMatrix\",op:1207,type:[\"real\",\"real\",\"real\",\"real\",\"real\",\"real\"],value:[.001,0,0,.001,0,0]},{name:\"uniqueId\",op:13,type:\"number\"},{name:\"fontBBox\",op:5,type:[\"number\",\"number\",\"number\",\"number\"],value:[0,0,0,0]},{name:\"strokeWidth\",op:1208,type:\"number\",value:0},{name:\"xuid\",op:14,type:[],value:null},{name:\"charset\",op:15,type:\"offset\",value:0},{name:\"encoding\",op:16,type:\"offset\",value:0},{name:\"charStrings\",op:17,type:\"offset\",value:0},{name:\"private\",op:18,type:[\"number\",\"offset\"],value:[0,0]},{name:\"ros\",op:1230,type:[\"SID\",\"SID\",\"number\"]},{name:\"cidFontVersion\",op:1231,type:\"number\",value:0},{name:\"cidFontRevision\",op:1232,type:\"number\",value:0},{name:\"cidFontType\",op:1233,type:\"number\",value:0},{name:\"cidCount\",op:1234,type:\"number\",value:8720},{name:\"uidBase\",op:1235,type:\"number\"},{name:\"fdArray\",op:1236,type:\"offset\"},{name:\"fdSelect\",op:1237,type:\"offset\"},{name:\"fontName\",op:1238,type:\"SID\"}],Bt=[{name:\"subrs\",op:19,type:\"offset\",value:0},{name:\"defaultWidthX\",op:20,type:\"number\",value:0},{name:\"nominalWidthX\",op:21,type:\"number\",value:0}];function rn(e,r){var t=wt(e,0,e.byteLength);return Dt(t,At,r)}function It(e,r,t,a){var n=wt(e,r,t);return Dt(n,Bt,a)}function Wr(e,r,t,a){for(var n=[],s=0;s<t.length;s+=1){var i=new DataView(new Uint8Array(t[s]).buffer),u=rn(i,a);u._subrs=[],u._subrsBias=0,u._defaultWidthX=0,u._nominalWidthX=0;var o=u.private[0],l=u.private[1];if(o!==0&&l!==0){var f=It(e,l+r,o,a);if(u._defaultWidthX=f.defaultWidthX,u._nominalWidthX=f.nominalWidthX,f.subrs!==0){var h=l+f.subrs,p=ve(e,h+r);u._subrs=p.objects,u._subrsBias=gr(u._subrs)}u._privateDict=f}n.push(u)}return n}function tn(e,r,t,a){var n,s,i=new k.Parser(e,r);t-=1;var u=[\".notdef\"],o=i.parseCard8();if(o===0)for(var l=0;l<t;l+=1)n=i.parseSID(),u.push(Oe(a,n));else if(o===1)for(;u.length<=t;){n=i.parseSID(),s=i.parseCard8();for(var f=0;f<=s;f+=1)u.push(Oe(a,n)),n+=1}else if(o===2)for(;u.length<=t;){n=i.parseSID(),s=i.parseCard16();for(var h=0;h<=s;h+=1)u.push(Oe(a,n)),n+=1}else throw new Error(\"Unknown charset format \"+o);return u}function an(e,r,t){var a,n={},s=new k.Parser(e,r),i=s.parseCard8();if(i===0)for(var u=s.parseCard8(),o=0;o<u;o+=1)a=s.parseCard8(),n[a]=o;else if(i===1){var l=s.parseCard8();a=1;for(var f=0;f<l;f+=1)for(var h=s.parseCard8(),p=s.parseCard8(),c=h;c<=h+p;c+=1)n[c]=a,a+=1}else throw new Error(\"Unknown encoding format \"+i);return new je(n,t)}function _r(e,r,t){var a,n,s,i,u=new P,o=[],l=0,f=!1,h=!1,p=0,c=0,d,x,m,y;if(e.isCIDFont){var C=e.tables.cff.topDict._fdSelect[r.index],S=e.tables.cff.topDict._fdArray[C];d=S._subrs,x=S._subrsBias,m=S._defaultWidthX,y=S._nominalWidthX}else d=e.tables.cff.topDict._subrs,x=e.tables.cff.topDict._subrsBias,m=e.tables.cff.topDict._defaultWidthX,y=e.tables.cff.topDict._nominalWidthX;var R=m;function O(F,G){h&&u.closePath(),u.moveTo(F,G),h=!0}function D(){var F;F=o.length%2!==0,F&&!f&&(R=o.shift()+y),l+=o.length>>1,o.length=0,f=!0}function L(F){for(var G,Y,Z,j,$,M,N,W,_,V,H,X,A=0;A<F.length;){var q=F[A];switch(A+=1,q){case 1:D();break;case 3:D();break;case 4:o.length>1&&!f&&(R=o.shift()+y,f=!0),c+=o.pop(),O(p,c);break;case 5:for(;o.length>0;)p+=o.shift(),c+=o.shift(),u.lineTo(p,c);break;case 6:for(;o.length>0&&(p+=o.shift(),u.lineTo(p,c),o.length!==0);)c+=o.shift(),u.lineTo(p,c);break;case 7:for(;o.length>0&&(c+=o.shift(),u.lineTo(p,c),o.length!==0);)p+=o.shift(),u.lineTo(p,c);break;case 8:for(;o.length>0;)a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 10:$=o.pop()+x,M=d[$],M&&L(M);break;case 11:return;case 12:switch(q=F[A],A+=1,q){case 35:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i+o.shift(),_=N+o.shift(),V=W+o.shift(),H=_+o.shift(),X=V+o.shift(),p=H+o.shift(),c=X+o.shift(),o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 34:a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i,_=N+o.shift(),V=i,H=_+o.shift(),X=c,p=H+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 36:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i,_=N+o.shift(),V=i,H=_+o.shift(),X=V+o.shift(),p=H+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;case 37:a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),N=s+o.shift(),W=i+o.shift(),_=N+o.shift(),V=W+o.shift(),H=_+o.shift(),X=V+o.shift(),Math.abs(H-p)>Math.abs(X-c)?p=H+o.shift():c=X+o.shift(),u.curveTo(a,n,s,i,N,W),u.curveTo(_,V,H,X,p,c);break;default:console.log(\"Glyph \"+r.index+\": unknown operator \"+1200+q),o.length=0}break;case 14:o.length>0&&!f&&(R=o.shift()+y,f=!0),h&&(u.closePath(),h=!1);break;case 18:D();break;case 19:case 20:D(),A+=l+7>>3;break;case 21:o.length>2&&!f&&(R=o.shift()+y,f=!0),c+=o.pop(),p+=o.pop(),O(p,c);break;case 22:o.length>1&&!f&&(R=o.shift()+y,f=!0),p+=o.pop(),O(p,c);break;case 23:D();break;case 24:for(;o.length>2;)a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);p+=o.shift(),c+=o.shift(),u.lineTo(p,c);break;case 25:for(;o.length>6;)p+=o.shift(),c+=o.shift(),u.lineTo(p,c);a=p+o.shift(),n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 26:for(o.length%2&&(p+=o.shift());o.length>0;)a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s,c=i+o.shift(),u.curveTo(a,n,s,i,p,c);break;case 27:for(o.length%2&&(c+=o.shift());o.length>0;)a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i,u.curveTo(a,n,s,i,p,c);break;case 28:G=F[A],Y=F[A+1],o.push((G<<24|Y<<16)>>16),A+=2;break;case 29:$=o.pop()+e.gsubrsBias,M=e.gsubrs[$],M&&L(M);break;case 30:for(;o.length>0&&(a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c),o.length!==0);)a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),c=i+o.shift(),p=s+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c);break;case 31:for(;o.length>0&&(a=p+o.shift(),n=c,s=a+o.shift(),i=n+o.shift(),c=i+o.shift(),p=s+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c),o.length!==0);)a=p,n=c+o.shift(),s=a+o.shift(),i=n+o.shift(),p=s+o.shift(),c=i+(o.length===1?o.shift():0),u.curveTo(a,n,s,i,p,c);break;default:q<32?console.log(\"Glyph \"+r.index+\": unknown operator \"+q):q<247?o.push(q-139):q<251?(G=F[A],A+=1,o.push((q-247)*256+G+108)):q<255?(G=F[A],A+=1,o.push(-(q-251)*256-G-108)):(G=F[A],Y=F[A+1],Z=F[A+2],j=F[A+3],A+=4,o.push((G<<24|Y<<16|Z<<8|j)/65536))}}}return L(t),r.advanceWidth=R,u}function nn(e,r,t,a){var n=[],s,i=new k.Parser(e,r),u=i.parseCard8();if(u===0)for(var o=0;o<t;o++){if(s=i.parseCard8(),s>=a)throw new Error(\"CFF table CID Font FDSelect has bad FD index value \"+s+\" (FD count \"+a+\")\");n.push(s)}else if(u===3){var l=i.parseCard16(),f=i.parseCard16();if(f!==0)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad initial GID \"+f);for(var h,p=0;p<l;p++){if(s=i.parseCard8(),h=i.parseCard16(),s>=a)throw new Error(\"CFF table CID Font FDSelect has bad FD index value \"+s+\" (FD count \"+a+\")\");if(h>t)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad GID \"+h);for(;f<h;f++)n.push(s);f=h}if(h!==t)throw new Error(\"CFF Table CID Font FDSelect format 3 range has bad final GID \"+h)}else throw new Error(\"CFF Table CID Font FDSelect table has unsupported format \"+u);return n}function sn(e,r,t,a){t.tables.cff={};var n=en(e,r),s=ve(e,n.endOffset,k.bytesToString),i=ve(e,s.endOffset),u=ve(e,i.endOffset,k.bytesToString),o=ve(e,u.endOffset);t.gsubrs=o.objects,t.gsubrsBias=gr(t.gsubrs);var l=Wr(e,r,i.objects,u.objects);if(l.length!==1)throw new Error(\"CFF table has too many fonts in 'FontSet' - count of fonts NameIndex.length = \"+l.length);var f=l[0];if(t.tables.cff.topDict=f,f._privateDict&&(t.defaultWidthX=f._privateDict.defaultWidthX,t.nominalWidthX=f._privateDict.nominalWidthX),f.ros[0]!==void 0&&f.ros[1]!==void 0&&(t.isCIDFont=!0),t.isCIDFont){var h=f.fdArray,p=f.fdSelect;if(h===0||p===0)throw new Error(\"Font is marked as a CID font, but FDArray and/or FDSelect information is missing\");h+=r;var c=ve(e,h),d=Wr(e,r,c.objects,u.objects);f._fdArray=d,p+=r,f._fdSelect=nn(e,p,t.numGlyphs,d.length)}var x=r+f.private[1],m=It(e,x,f.private[0],u.objects);if(t.defaultWidthX=m.defaultWidthX,t.nominalWidthX=m.nominalWidthX,m.subrs!==0){var y=x+m.subrs,C=ve(e,y);t.subrs=C.objects,t.subrsBias=gr(t.subrs)}else t.subrs=[],t.subrsBias=0;var S;a.lowMemory?(S=Qa(e,r+f.charStrings),t.nGlyphs=S.offsets.length):(S=ve(e,r+f.charStrings),t.nGlyphs=S.objects.length);var R=tn(e,r+f.charset,t.nGlyphs,u.objects);if(f.encoding===0?t.cffEncoding=new je(Na,R):f.encoding===1?t.cffEncoding=new je(Ha,R):t.cffEncoding=an(e,r+f.encoding,R),t.encoding=t.encoding||t.cffEncoding,t.glyphs=new ue.GlyphSet(t),a.lowMemory)t._push=function(L){var F=Ka(L,S.offsets,e,r+f.charStrings);t.glyphs.push(L,ue.cffGlyphLoader(t,L,_r,F))};else for(var O=0;O<t.nGlyphs;O+=1){var D=S.objects[O];t.glyphs.push(O,ue.cffGlyphLoader(t,O,_r,D))}}function Mt(e,r){var t,a=qe.indexOf(e);return a>=0&&(t=a),a=r.indexOf(e),a>=0?t=a+qe.length:(t=qe.length+r.length,r.push(e)),t}function on(){return new b.Record(\"Header\",[{name:\"major\",type:\"Card8\",value:1},{name:\"minor\",type:\"Card8\",value:0},{name:\"hdrSize\",type:\"Card8\",value:4},{name:\"major\",type:\"Card8\",value:1}])}function un(e){var r=new b.Record(\"Name INDEX\",[{name:\"names\",type:\"INDEX\",value:[]}]);r.names=[];for(var t=0;t<e.length;t+=1)r.names.push({name:\"name_\"+t,type:\"NAME\",value:e[t]});return r}function Pt(e,r,t){for(var a={},n=0;n<e.length;n+=1){var s=e[n],i=r[s.name];i!==void 0&&!Rt(i,s.value)&&(s.type===\"SID\"&&(i=Mt(i,t)),a[s.op]={name:s.name,type:s.type,value:i})}return a}function Vr(e,r){var t=new b.Record(\"Top DICT\",[{name:\"dict\",type:\"DICT\",value:{}}]);return t.dict=Pt(At,e,r),t}function qr(e){var r=new b.Record(\"Top DICT INDEX\",[{name:\"topDicts\",type:\"INDEX\",value:[]}]);return r.topDicts=[{name:\"topDict_0\",type:\"TABLE\",value:e}],r}function ln(e){var r=new b.Record(\"String INDEX\",[{name:\"strings\",type:\"INDEX\",value:[]}]);r.strings=[];for(var t=0;t<e.length;t+=1)r.strings.push({name:\"string_\"+t,type:\"STRING\",value:e[t]});return r}function fn(){return new b.Record(\"Global Subr INDEX\",[{name:\"subrs\",type:\"INDEX\",value:[]}])}function pn(e,r){for(var t=new b.Record(\"Charsets\",[{name:\"format\",type:\"Card8\",value:0}]),a=0;a<e.length;a+=1){var n=e[a],s=Mt(n,r);t.fields.push({name:\"glyph_\"+a,type:\"SID\",value:s})}return t}function hn(e){var r=[],t=e.path;r.push({name:\"width\",type:\"NUMBER\",value:e.advanceWidth});for(var a=0,n=0,s=0;s<t.commands.length;s+=1){var i=void 0,u=void 0,o=t.commands[s];if(o.type===\"Q\"){var l=.3333333333333333,f=2/3;o={type:\"C\",x:o.x,y:o.y,x1:Math.round(l*a+f*o.x1),y1:Math.round(l*n+f*o.y1),x2:Math.round(l*o.x+f*o.x1),y2:Math.round(l*o.y+f*o.y1)}}if(o.type===\"M\")i=Math.round(o.x-a),u=Math.round(o.y-n),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rmoveto\",type:\"OP\",value:21}),a=Math.round(o.x),n=Math.round(o.y);else if(o.type===\"L\")i=Math.round(o.x-a),u=Math.round(o.y-n),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rlineto\",type:\"OP\",value:5}),a=Math.round(o.x),n=Math.round(o.y);else if(o.type===\"C\"){var h=Math.round(o.x1-a),p=Math.round(o.y1-n),c=Math.round(o.x2-o.x1),d=Math.round(o.y2-o.y1);i=Math.round(o.x-o.x2),u=Math.round(o.y-o.y2),r.push({name:\"dx1\",type:\"NUMBER\",value:h}),r.push({name:\"dy1\",type:\"NUMBER\",value:p}),r.push({name:\"dx2\",type:\"NUMBER\",value:c}),r.push({name:\"dy2\",type:\"NUMBER\",value:d}),r.push({name:\"dx\",type:\"NUMBER\",value:i}),r.push({name:\"dy\",type:\"NUMBER\",value:u}),r.push({name:\"rrcurveto\",type:\"OP\",value:8}),a=Math.round(o.x),n=Math.round(o.y)}}return r.push({name:\"endchar\",type:\"OP\",value:14}),r}function cn(e){for(var r=new b.Record(\"CharStrings INDEX\",[{name:\"charStrings\",type:\"INDEX\",value:[]}]),t=0;t<e.length;t+=1){var a=e.get(t),n=hn(a);r.charStrings.push({name:a.name,type:\"CHARSTRING\",value:n})}return r}function vn(e,r){var t=new b.Record(\"Private DICT\",[{name:\"dict\",type:\"DICT\",value:{}}]);return t.dict=Pt(Bt,e,r),t}function dn(e,r){for(var t=new b.Table(\"CFF \",[{name:\"header\",type:\"RECORD\"},{name:\"nameIndex\",type:\"RECORD\"},{name:\"topDictIndex\",type:\"RECORD\"},{name:\"stringIndex\",type:\"RECORD\"},{name:\"globalSubrIndex\",type:\"RECORD\"},{name:\"charsets\",type:\"RECORD\"},{name:\"charStringsIndex\",type:\"RECORD\"},{name:\"privateDict\",type:\"RECORD\"}]),a=1/r.unitsPerEm,n={version:r.version,fullName:r.fullName,familyName:r.familyName,weight:r.weightName,fontBBox:r.fontBBox||[0,0,0,0],fontMatrix:[a,0,0,a,0,0],charset:999,encoding:0,charStrings:999,private:[0,999]},s={},i=[],u,o=1;o<e.length;o+=1)u=e.get(o),i.push(u.name);var l=[];t.header=on(),t.nameIndex=un([r.postScriptName]);var f=Vr(n,l);t.topDictIndex=qr(f),t.globalSubrIndex=fn(),t.charsets=pn(i,l),t.charStringsIndex=cn(e),t.privateDict=vn(s,l),t.stringIndex=ln(l);var h=t.header.sizeOf()+t.nameIndex.sizeOf()+t.topDictIndex.sizeOf()+t.stringIndex.sizeOf()+t.globalSubrIndex.sizeOf();return n.charset=h,n.encoding=0,n.charStrings=n.charset+t.charsets.sizeOf(),n.private[1]=n.charStrings+t.charStringsIndex.sizeOf(),f=Vr(n,l),t.topDictIndex=qr(f),t}var Gt={parse:sn,make:dn};function gn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.fontRevision=Math.round(a.parseFixed()*1e3)/1e3,t.checkSumAdjustment=a.parseULong(),t.magicNumber=a.parseULong(),U.argument(t.magicNumber===1594834165,\"Font header has wrong magic number.\"),t.flags=a.parseUShort(),t.unitsPerEm=a.parseUShort(),t.created=a.parseLongDateTime(),t.modified=a.parseLongDateTime(),t.xMin=a.parseShort(),t.yMin=a.parseShort(),t.xMax=a.parseShort(),t.yMax=a.parseShort(),t.macStyle=a.parseUShort(),t.lowestRecPPEM=a.parseUShort(),t.fontDirectionHint=a.parseShort(),t.indexToLocFormat=a.parseShort(),t.glyphDataFormat=a.parseShort(),t}function mn(e){var r=Math.round(new Date().getTime()/1e3)+2082844800,t=r;return e.createdTimestamp&&(t=e.createdTimestamp+2082844800),new b.Table(\"head\",[{name:\"version\",type:\"FIXED\",value:65536},{name:\"fontRevision\",type:\"FIXED\",value:65536},{name:\"checkSumAdjustment\",type:\"ULONG\",value:0},{name:\"magicNumber\",type:\"ULONG\",value:1594834165},{name:\"flags\",type:\"USHORT\",value:0},{name:\"unitsPerEm\",type:\"USHORT\",value:1e3},{name:\"created\",type:\"LONGDATETIME\",value:t},{name:\"modified\",type:\"LONGDATETIME\",value:r},{name:\"xMin\",type:\"SHORT\",value:0},{name:\"yMin\",type:\"SHORT\",value:0},{name:\"xMax\",type:\"SHORT\",value:0},{name:\"yMax\",type:\"SHORT\",value:0},{name:\"macStyle\",type:\"USHORT\",value:0},{name:\"lowestRecPPEM\",type:\"USHORT\",value:0},{name:\"fontDirectionHint\",type:\"SHORT\",value:2},{name:\"indexToLocFormat\",type:\"SHORT\",value:0},{name:\"glyphDataFormat\",type:\"SHORT\",value:0}],e)}var Nt={parse:gn,make:mn};function yn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.ascender=a.parseShort(),t.descender=a.parseShort(),t.lineGap=a.parseShort(),t.advanceWidthMax=a.parseUShort(),t.minLeftSideBearing=a.parseShort(),t.minRightSideBearing=a.parseShort(),t.xMaxExtent=a.parseShort(),t.caretSlopeRise=a.parseShort(),t.caretSlopeRun=a.parseShort(),t.caretOffset=a.parseShort(),a.relativeOffset+=8,t.metricDataFormat=a.parseShort(),t.numberOfHMetrics=a.parseUShort(),t}function xn(e){return new b.Table(\"hhea\",[{name:\"version\",type:\"FIXED\",value:65536},{name:\"ascender\",type:\"FWORD\",value:0},{name:\"descender\",type:\"FWORD\",value:0},{name:\"lineGap\",type:\"FWORD\",value:0},{name:\"advanceWidthMax\",type:\"UFWORD\",value:0},{name:\"minLeftSideBearing\",type:\"FWORD\",value:0},{name:\"minRightSideBearing\",type:\"FWORD\",value:0},{name:\"xMaxExtent\",type:\"FWORD\",value:0},{name:\"caretSlopeRise\",type:\"SHORT\",value:1},{name:\"caretSlopeRun\",type:\"SHORT\",value:0},{name:\"caretOffset\",type:\"SHORT\",value:0},{name:\"reserved1\",type:\"SHORT\",value:0},{name:\"reserved2\",type:\"SHORT\",value:0},{name:\"reserved3\",type:\"SHORT\",value:0},{name:\"reserved4\",type:\"SHORT\",value:0},{name:\"metricDataFormat\",type:\"SHORT\",value:0},{name:\"numberOfHMetrics\",type:\"USHORT\",value:0}],e)}var Ht={parse:yn,make:xn};function bn(e,r,t,a,n){for(var s,i,u=new k.Parser(e,r),o=0;o<a;o+=1){o<t&&(s=u.parseUShort(),i=u.parseShort());var l=n.get(o);l.advanceWidth=s,l.leftSideBearing=i}}function Sn(e,r,t,a,n){e._hmtxTableData={};for(var s,i,u=new k.Parser(r,t),o=0;o<n;o+=1)o<a&&(s=u.parseUShort(),i=u.parseShort()),e._hmtxTableData[o]={advanceWidth:s,leftSideBearing:i}}function Tn(e,r,t,a,n,s,i){i.lowMemory?Sn(e,r,t,a,n):bn(r,t,a,n,s)}function kn(e){for(var r=new b.Table(\"hmtx\",[]),t=0;t<e.length;t+=1){var a=e.get(t),n=a.advanceWidth||0,s=a.leftSideBearing||0;r.fields.push({name:\"advanceWidth_\"+t,type:\"USHORT\",value:n}),r.fields.push({name:\"leftSideBearing_\"+t,type:\"SHORT\",value:s})}return r}var zt={parse:Tn,make:kn};function Fn(e){for(var r=new b.Table(\"ltag\",[{name:\"version\",type:\"ULONG\",value:1},{name:\"flags\",type:\"ULONG\",value:0},{name:\"numTags\",type:\"ULONG\",value:e.length}]),t=\"\",a=12+e.length*4,n=0;n<e.length;++n){var s=t.indexOf(e[n]);s<0&&(s=t.length,t+=e[n]),r.fields.push({name:\"offset \"+n,type:\"USHORT\",value:a+s}),r.fields.push({name:\"length \"+n,type:\"USHORT\",value:e[n].length})}return r.fields.push({name:\"stringPool\",type:\"CHARARRAY\",value:t}),r}function Un(e,r){var t=new k.Parser(e,r),a=t.parseULong();U.argument(a===1,\"Unsupported ltag table version.\"),t.skip(\"uLong\",1);for(var n=t.parseULong(),s=[],i=0;i<n;i++){for(var u=\"\",o=r+t.parseUShort(),l=t.parseUShort(),f=o;f<o+l;++f)u+=String.fromCharCode(e.getInt8(f));s.push(u)}return s}var Wt={make:Fn,parse:Un};function Cn(e,r){var t={},a=new k.Parser(e,r);return t.version=a.parseVersion(),t.numGlyphs=a.parseUShort(),t.version===1&&(t.maxPoints=a.parseUShort(),t.maxContours=a.parseUShort(),t.maxCompositePoints=a.parseUShort(),t.maxCompositeContours=a.parseUShort(),t.maxZones=a.parseUShort(),t.maxTwilightPoints=a.parseUShort(),t.maxStorage=a.parseUShort(),t.maxFunctionDefs=a.parseUShort(),t.maxInstructionDefs=a.parseUShort(),t.maxStackElements=a.parseUShort(),t.maxSizeOfInstructions=a.parseUShort(),t.maxComponentElements=a.parseUShort(),t.maxComponentDepth=a.parseUShort()),t}function En(e){return new b.Table(\"maxp\",[{name:\"version\",type:\"FIXED\",value:20480},{name:\"numGlyphs\",type:\"USHORT\",value:e}])}var _t={parse:Cn,make:En},Vt=[\"copyright\",\"fontFamily\",\"fontSubfamily\",\"uniqueID\",\"fullName\",\"version\",\"postScriptName\",\"trademark\",\"manufacturer\",\"designer\",\"description\",\"manufacturerURL\",\"designerURL\",\"license\",\"licenseURL\",\"reserved\",\"preferredFamily\",\"preferredSubfamily\",\"compatibleFullName\",\"sampleText\",\"postScriptFindFontName\",\"wwsFamily\",\"wwsSubfamily\"],qt={0:\"en\",1:\"fr\",2:\"de\",3:\"it\",4:\"nl\",5:\"sv\",6:\"es\",7:\"da\",8:\"pt\",9:\"no\",10:\"he\",11:\"ja\",12:\"ar\",13:\"fi\",14:\"el\",15:\"is\",16:\"mt\",17:\"tr\",18:\"hr\",19:\"zh-Hant\",20:\"ur\",21:\"hi\",22:\"th\",23:\"ko\",24:\"lt\",25:\"pl\",26:\"hu\",27:\"es\",28:\"lv\",29:\"se\",30:\"fo\",31:\"fa\",32:\"ru\",33:\"zh\",34:\"nl-BE\",35:\"ga\",36:\"sq\",37:\"ro\",38:\"cz\",39:\"sk\",40:\"si\",41:\"yi\",42:\"sr\",43:\"mk\",44:\"bg\",45:\"uk\",46:\"be\",47:\"uz\",48:\"kk\",49:\"az-Cyrl\",50:\"az-Arab\",51:\"hy\",52:\"ka\",53:\"mo\",54:\"ky\",55:\"tg\",56:\"tk\",57:\"mn-CN\",58:\"mn\",59:\"ps\",60:\"ks\",61:\"ku\",62:\"sd\",63:\"bo\",64:\"ne\",65:\"sa\",66:\"mr\",67:\"bn\",68:\"as\",69:\"gu\",70:\"pa\",71:\"or\",72:\"ml\",73:\"kn\",74:\"ta\",75:\"te\",76:\"si\",77:\"my\",78:\"km\",79:\"lo\",80:\"vi\",81:\"id\",82:\"tl\",83:\"ms\",84:\"ms-Arab\",85:\"am\",86:\"ti\",87:\"om\",88:\"so\",89:\"sw\",90:\"rw\",91:\"rn\",92:\"ny\",93:\"mg\",94:\"eo\",128:\"cy\",129:\"eu\",130:\"ca\",131:\"la\",132:\"qu\",133:\"gn\",134:\"ay\",135:\"tt\",136:\"ug\",137:\"dz\",138:\"jv\",139:\"su\",140:\"gl\",141:\"af\",142:\"br\",143:\"iu\",144:\"gd\",145:\"gv\",146:\"ga\",147:\"to\",148:\"el-polyton\",149:\"kl\",150:\"az\",151:\"nn\"},On={0:0,1:0,2:0,3:0,4:0,5:0,6:0,7:0,8:0,9:0,10:5,11:1,12:4,13:0,14:6,15:0,16:0,17:0,18:0,19:2,20:4,21:9,22:21,23:3,24:29,25:29,26:29,27:29,28:29,29:0,30:0,31:4,32:7,33:25,34:0,35:0,36:0,37:0,38:29,39:29,40:0,41:5,42:7,43:7,44:7,45:7,46:7,47:7,48:7,49:7,50:4,51:24,52:23,53:7,54:7,55:7,56:7,57:27,58:7,59:4,60:4,61:4,62:4,63:26,64:9,65:9,66:9,67:13,68:13,69:11,70:10,71:12,72:17,73:16,74:14,75:15,76:18,77:19,78:20,79:22,80:30,81:0,82:0,83:0,84:4,85:28,86:28,87:28,88:0,89:0,90:0,91:0,92:0,93:0,94:0,128:0,129:0,130:0,131:0,132:0,133:0,134:0,135:7,136:4,137:26,138:0,139:0,140:0,141:0,142:0,143:28,144:0,145:0,146:0,147:0,148:6,149:0,150:0,151:0},Xt={1078:\"af\",1052:\"sq\",1156:\"gsw\",1118:\"am\",5121:\"ar-DZ\",15361:\"ar-BH\",3073:\"ar\",2049:\"ar-IQ\",11265:\"ar-JO\",13313:\"ar-KW\",12289:\"ar-LB\",4097:\"ar-LY\",6145:\"ary\",8193:\"ar-OM\",16385:\"ar-QA\",1025:\"ar-SA\",10241:\"ar-SY\",7169:\"aeb\",14337:\"ar-AE\",9217:\"ar-YE\",1067:\"hy\",1101:\"as\",2092:\"az-Cyrl\",1068:\"az\",1133:\"ba\",1069:\"eu\",1059:\"be\",2117:\"bn\",1093:\"bn-IN\",8218:\"bs-Cyrl\",5146:\"bs\",1150:\"br\",1026:\"bg\",1027:\"ca\",3076:\"zh-HK\",5124:\"zh-MO\",2052:\"zh\",4100:\"zh-SG\",1028:\"zh-TW\",1155:\"co\",1050:\"hr\",4122:\"hr-BA\",1029:\"cs\",1030:\"da\",1164:\"prs\",1125:\"dv\",2067:\"nl-BE\",1043:\"nl\",3081:\"en-AU\",10249:\"en-BZ\",4105:\"en-CA\",9225:\"en-029\",16393:\"en-IN\",6153:\"en-IE\",8201:\"en-JM\",17417:\"en-MY\",5129:\"en-NZ\",13321:\"en-PH\",18441:\"en-SG\",7177:\"en-ZA\",11273:\"en-TT\",2057:\"en-GB\",1033:\"en\",12297:\"en-ZW\",1061:\"et\",1080:\"fo\",1124:\"fil\",1035:\"fi\",2060:\"fr-BE\",3084:\"fr-CA\",1036:\"fr\",5132:\"fr-LU\",6156:\"fr-MC\",4108:\"fr-CH\",1122:\"fy\",1110:\"gl\",1079:\"ka\",3079:\"de-AT\",1031:\"de\",5127:\"de-LI\",4103:\"de-LU\",2055:\"de-CH\",1032:\"el\",1135:\"kl\",1095:\"gu\",1128:\"ha\",1037:\"he\",1081:\"hi\",1038:\"hu\",1039:\"is\",1136:\"ig\",1057:\"id\",1117:\"iu\",2141:\"iu-Latn\",2108:\"ga\",1076:\"xh\",1077:\"zu\",1040:\"it\",2064:\"it-CH\",1041:\"ja\",1099:\"kn\",1087:\"kk\",1107:\"km\",1158:\"quc\",1159:\"rw\",1089:\"sw\",1111:\"kok\",1042:\"ko\",1088:\"ky\",1108:\"lo\",1062:\"lv\",1063:\"lt\",2094:\"dsb\",1134:\"lb\",1071:\"mk\",2110:\"ms-BN\",1086:\"ms\",1100:\"ml\",1082:\"mt\",1153:\"mi\",1146:\"arn\",1102:\"mr\",1148:\"moh\",1104:\"mn\",2128:\"mn-CN\",1121:\"ne\",1044:\"nb\",2068:\"nn\",1154:\"oc\",1096:\"or\",1123:\"ps\",1045:\"pl\",1046:\"pt\",2070:\"pt-PT\",1094:\"pa\",1131:\"qu-BO\",2155:\"qu-EC\",3179:\"qu\",1048:\"ro\",1047:\"rm\",1049:\"ru\",9275:\"smn\",4155:\"smj-NO\",5179:\"smj\",3131:\"se-FI\",1083:\"se\",2107:\"se-SE\",8251:\"sms\",6203:\"sma-NO\",7227:\"sms\",1103:\"sa\",7194:\"sr-Cyrl-BA\",3098:\"sr\",6170:\"sr-Latn-BA\",2074:\"sr-Latn\",1132:\"nso\",1074:\"tn\",1115:\"si\",1051:\"sk\",1060:\"sl\",11274:\"es-AR\",16394:\"es-BO\",13322:\"es-CL\",9226:\"es-CO\",5130:\"es-CR\",7178:\"es-DO\",12298:\"es-EC\",17418:\"es-SV\",4106:\"es-GT\",18442:\"es-HN\",2058:\"es-MX\",19466:\"es-NI\",6154:\"es-PA\",15370:\"es-PY\",10250:\"es-PE\",20490:\"es-PR\",3082:\"es\",1034:\"es\",21514:\"es-US\",14346:\"es-UY\",8202:\"es-VE\",2077:\"sv-FI\",1053:\"sv\",1114:\"syr\",1064:\"tg\",2143:\"tzm\",1097:\"ta\",1092:\"tt\",1098:\"te\",1054:\"th\",1105:\"bo\",1055:\"tr\",1090:\"tk\",1152:\"ug\",1058:\"uk\",1070:\"hsb\",1056:\"ur\",2115:\"uz-Cyrl\",1091:\"uz\",1066:\"vi\",1106:\"cy\",1160:\"wo\",1157:\"sah\",1144:\"ii\",1130:\"yo\"};function Ln(e,r,t){switch(e){case 0:if(r===65535)return\"und\";if(t)return t[r];break;case 1:return qt[r];case 3:return Xt[r]}}var mr=\"utf-16\",Rn={0:\"macintosh\",1:\"x-mac-japanese\",2:\"x-mac-chinesetrad\",3:\"x-mac-korean\",6:\"x-mac-greek\",7:\"x-mac-cyrillic\",9:\"x-mac-devanagai\",10:\"x-mac-gurmukhi\",11:\"x-mac-gujarati\",12:\"x-mac-oriya\",13:\"x-mac-bengali\",14:\"x-mac-tamil\",15:\"x-mac-telugu\",16:\"x-mac-kannada\",17:\"x-mac-malayalam\",18:\"x-mac-sinhalese\",19:\"x-mac-burmese\",20:\"x-mac-khmer\",21:\"x-mac-thai\",22:\"x-mac-lao\",23:\"x-mac-georgian\",24:\"x-mac-armenian\",25:\"x-mac-chinesesimp\",26:\"x-mac-tibetan\",27:\"x-mac-mongolian\",28:\"x-mac-ethiopic\",29:\"x-mac-ce\",30:\"x-mac-vietnamese\",31:\"x-mac-extarabic\"},wn={15:\"x-mac-icelandic\",17:\"x-mac-turkish\",18:\"x-mac-croatian\",24:\"x-mac-ce\",25:\"x-mac-ce\",26:\"x-mac-ce\",27:\"x-mac-ce\",28:\"x-mac-ce\",30:\"x-mac-icelandic\",37:\"x-mac-romanian\",38:\"x-mac-ce\",39:\"x-mac-ce\",40:\"x-mac-ce\",143:\"x-mac-inuit\",146:\"x-mac-gaelic\"};function Yt(e,r,t){switch(e){case 0:return mr;case 1:return wn[t]||Rn[r];case 3:if(r===1||r===10)return mr;break}}function Dn(e,r,t){for(var a={},n=new k.Parser(e,r),s=n.parseUShort(),i=n.parseUShort(),u=n.offset+n.parseUShort(),o=0;o<i;o++){var l=n.parseUShort(),f=n.parseUShort(),h=n.parseUShort(),p=n.parseUShort(),c=Vt[p]||p,d=n.parseUShort(),x=n.parseUShort(),m=Ln(l,h,t),y=Yt(l,f,h);if(y!==void 0&&m!==void 0){var C=void 0;if(y===mr?C=Fe.UTF16(e,u+x,d):C=Fe.MACSTRING(e,u+x,d,y),C){var S=a[c];S===void 0&&(S=a[c]={}),S[m]=C}}}var R=0;return s===1&&(R=n.parseUShort()),a}function nr(e){var r={};for(var t in e)r[e[t]]=parseInt(t);return r}function Xr(e,r,t,a,n,s){return new b.Record(\"NameRecord\",[{name:\"platformID\",type:\"USHORT\",value:e},{name:\"encodingID\",type:\"USHORT\",value:r},{name:\"languageID\",type:\"USHORT\",value:t},{name:\"nameID\",type:\"USHORT\",value:a},{name:\"length\",type:\"USHORT\",value:n},{name:\"offset\",type:\"USHORT\",value:s}])}function An(e,r){var t=e.length,a=r.length-t+1;e:for(var n=0;n<a;n++)for(;n<a;n++){for(var s=0;s<t;s++)if(r[n+s]!==e[s])continue e;return n}return-1}function Yr(e,r){var t=An(e,r);if(t<0){t=r.length;for(var a=0,n=e.length;a<n;++a)r.push(e[a])}return t}function Bn(e,r){var t,a=[],n={},s=nr(Vt);for(var i in e){var u=s[i];if(u===void 0&&(u=i),t=parseInt(u),isNaN(t))throw new Error('Name table entry \"'+i+'\" does not exist, see nameTableNames for complete list.');n[t]=e[i],a.push(t)}for(var o=nr(qt),l=nr(Xt),f=[],h=[],p=0;p<a.length;p++){t=a[p];var c=n[t];for(var d in c){var x=c[d],m=1,y=o[d],C=On[y],S=Yt(m,C,y),R=g.MACSTRING(x,S);R===void 0&&(m=0,y=r.indexOf(d),y<0&&(y=r.length,r.push(d)),C=4,R=g.UTF16(x));var O=Yr(R,h);f.push(Xr(m,C,y,t,R.length,O));var D=l[d];if(D!==void 0){var L=g.UTF16(x),F=Yr(L,h);f.push(Xr(3,1,D,t,L.length,F))}}}f.sort(function(Z,j){return Z.platformID-j.platformID||Z.encodingID-j.encodingID||Z.languageID-j.languageID||Z.nameID-j.nameID});for(var G=new b.Table(\"name\",[{name:\"format\",type:\"USHORT\",value:0},{name:\"count\",type:\"USHORT\",value:f.length},{name:\"stringOffset\",type:\"USHORT\",value:6+f.length*12}]),Y=0;Y<f.length;Y++)G.fields.push({name:\"record_\"+Y,type:\"RECORD\",value:f[Y]});return G.fields.push({name:\"strings\",type:\"LITERAL\",value:h}),G}var Zt={parse:Dn,make:Bn},yr=[{begin:0,end:127},{begin:128,end:255},{begin:256,end:383},{begin:384,end:591},{begin:592,end:687},{begin:688,end:767},{begin:768,end:879},{begin:880,end:1023},{begin:11392,end:11519},{begin:1024,end:1279},{begin:1328,end:1423},{begin:1424,end:1535},{begin:42240,end:42559},{begin:1536,end:1791},{begin:1984,end:2047},{begin:2304,end:2431},{begin:2432,end:2559},{begin:2560,end:2687},{begin:2688,end:2815},{begin:2816,end:2943},{begin:2944,end:3071},{begin:3072,end:3199},{begin:3200,end:3327},{begin:3328,end:3455},{begin:3584,end:3711},{begin:3712,end:3839},{begin:4256,end:4351},{begin:6912,end:7039},{begin:4352,end:4607},{begin:7680,end:7935},{begin:7936,end:8191},{begin:8192,end:8303},{begin:8304,end:8351},{begin:8352,end:8399},{begin:8400,end:8447},{begin:8448,end:8527},{begin:8528,end:8591},{begin:8592,end:8703},{begin:8704,end:8959},{begin:8960,end:9215},{begin:9216,end:9279},{begin:9280,end:9311},{begin:9312,end:9471},{begin:9472,end:9599},{begin:9600,end:9631},{begin:9632,end:9727},{begin:9728,end:9983},{begin:9984,end:10175},{begin:12288,end:12351},{begin:12352,end:12447},{begin:12448,end:12543},{begin:12544,end:12591},{begin:12592,end:12687},{begin:43072,end:43135},{begin:12800,end:13055},{begin:13056,end:13311},{begin:44032,end:55215},{begin:55296,end:57343},{begin:67840,end:67871},{begin:19968,end:40959},{begin:57344,end:63743},{begin:12736,end:12783},{begin:64256,end:64335},{begin:64336,end:65023},{begin:65056,end:65071},{begin:65040,end:65055},{begin:65104,end:65135},{begin:65136,end:65279},{begin:65280,end:65519},{begin:65520,end:65535},{begin:3840,end:4095},{begin:1792,end:1871},{begin:1920,end:1983},{begin:3456,end:3583},{begin:4096,end:4255},{begin:4608,end:4991},{begin:5024,end:5119},{begin:5120,end:5759},{begin:5760,end:5791},{begin:5792,end:5887},{begin:6016,end:6143},{begin:6144,end:6319},{begin:10240,end:10495},{begin:40960,end:42127},{begin:5888,end:5919},{begin:66304,end:66351},{begin:66352,end:66383},{begin:66560,end:66639},{begin:118784,end:119039},{begin:119808,end:120831},{begin:1044480,end:1048573},{begin:65024,end:65039},{begin:917504,end:917631},{begin:6400,end:6479},{begin:6480,end:6527},{begin:6528,end:6623},{begin:6656,end:6687},{begin:11264,end:11359},{begin:11568,end:11647},{begin:19904,end:19967},{begin:43008,end:43055},{begin:65536,end:65663},{begin:65856,end:65935},{begin:66432,end:66463},{begin:66464,end:66527},{begin:66640,end:66687},{begin:66688,end:66735},{begin:67584,end:67647},{begin:68096,end:68191},{begin:119552,end:119647},{begin:73728,end:74751},{begin:119648,end:119679},{begin:7040,end:7103},{begin:7168,end:7247},{begin:7248,end:7295},{begin:43136,end:43231},{begin:43264,end:43311},{begin:43312,end:43359},{begin:43520,end:43615},{begin:65936,end:65999},{begin:66e3,end:66047},{begin:66208,end:66271},{begin:127024,end:127135}];function In(e){for(var r=0;r<yr.length;r+=1){var t=yr[r];if(e>=t.begin&&e<t.end)return r}return-1}function Mn(e,r){var t={},a=new k.Parser(e,r);t.version=a.parseUShort(),t.xAvgCharWidth=a.parseShort(),t.usWeightClass=a.parseUShort(),t.usWidthClass=a.parseUShort(),t.fsType=a.parseUShort(),t.ySubscriptXSize=a.parseShort(),t.ySubscriptYSize=a.parseShort(),t.ySubscriptXOffset=a.parseShort(),t.ySubscriptYOffset=a.parseShort(),t.ySuperscriptXSize=a.parseShort(),t.ySuperscriptYSize=a.parseShort(),t.ySuperscriptXOffset=a.parseShort(),t.ySuperscriptYOffset=a.parseShort(),t.yStrikeoutSize=a.parseShort(),t.yStrikeoutPosition=a.parseShort(),t.sFamilyClass=a.parseShort(),t.panose=[];for(var n=0;n<10;n++)t.panose[n]=a.parseByte();return t.ulUnicodeRange1=a.parseULong(),t.ulUnicodeRange2=a.parseULong(),t.ulUnicodeRange3=a.parseULong(),t.ulUnicodeRange4=a.parseULong(),t.achVendID=String.fromCharCode(a.parseByte(),a.parseByte(),a.parseByte(),a.parseByte()),t.fsSelection=a.parseUShort(),t.usFirstCharIndex=a.parseUShort(),t.usLastCharIndex=a.parseUShort(),t.sTypoAscender=a.parseShort(),t.sTypoDescender=a.parseShort(),t.sTypoLineGap=a.parseShort(),t.usWinAscent=a.parseUShort(),t.usWinDescent=a.parseUShort(),t.version>=1&&(t.ulCodePageRange1=a.parseULong(),t.ulCodePageRange2=a.parseULong()),t.version>=2&&(t.sxHeight=a.parseShort(),t.sCapHeight=a.parseShort(),t.usDefaultChar=a.parseUShort(),t.usBreakChar=a.parseUShort(),t.usMaxContent=a.parseUShort()),t}function Pn(e){return new b.Table(\"OS/2\",[{name:\"version\",type:\"USHORT\",value:3},{name:\"xAvgCharWidth\",type:\"SHORT\",value:0},{name:\"usWeightClass\",type:\"USHORT\",value:0},{name:\"usWidthClass\",type:\"USHORT\",value:0},{name:\"fsType\",type:\"USHORT\",value:0},{name:\"ySubscriptXSize\",type:\"SHORT\",value:650},{name:\"ySubscriptYSize\",type:\"SHORT\",value:699},{name:\"ySubscriptXOffset\",type:\"SHORT\",value:0},{name:\"ySubscriptYOffset\",type:\"SHORT\",value:140},{name:\"ySuperscriptXSize\",type:\"SHORT\",value:650},{name:\"ySuperscriptYSize\",type:\"SHORT\",value:699},{name:\"ySuperscriptXOffset\",type:\"SHORT\",value:0},{name:\"ySuperscriptYOffset\",type:\"SHORT\",value:479},{name:\"yStrikeoutSize\",type:\"SHORT\",value:49},{name:\"yStrikeoutPosition\",type:\"SHORT\",value:258},{name:\"sFamilyClass\",type:\"SHORT\",value:0},{name:\"bFamilyType\",type:\"BYTE\",value:0},{name:\"bSerifStyle\",type:\"BYTE\",value:0},{name:\"bWeight\",type:\"BYTE\",value:0},{name:\"bProportion\",type:\"BYTE\",value:0},{name:\"bContrast\",type:\"BYTE\",value:0},{name:\"bStrokeVariation\",type:\"BYTE\",value:0},{name:\"bArmStyle\",type:\"BYTE\",value:0},{name:\"bLetterform\",type:\"BYTE\",value:0},{name:\"bMidline\",type:\"BYTE\",value:0},{name:\"bXHeight\",type:\"BYTE\",value:0},{name:\"ulUnicodeRange1\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange2\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange3\",type:\"ULONG\",value:0},{name:\"ulUnicodeRange4\",type:\"ULONG\",value:0},{name:\"achVendID\",type:\"CHARARRAY\",value:\"XXXX\"},{name:\"fsSelection\",type:\"USHORT\",value:0},{name:\"usFirstCharIndex\",type:\"USHORT\",value:0},{name:\"usLastCharIndex\",type:\"USHORT\",value:0},{name:\"sTypoAscender\",type:\"SHORT\",value:0},{name:\"sTypoDescender\",type:\"SHORT\",value:0},{name:\"sTypoLineGap\",type:\"SHORT\",value:0},{name:\"usWinAscent\",type:\"USHORT\",value:0},{name:\"usWinDescent\",type:\"USHORT\",value:0},{name:\"ulCodePageRange1\",type:\"ULONG\",value:0},{name:\"ulCodePageRange2\",type:\"ULONG\",value:0},{name:\"sxHeight\",type:\"SHORT\",value:0},{name:\"sCapHeight\",type:\"SHORT\",value:0},{name:\"usDefaultChar\",type:\"USHORT\",value:0},{name:\"usBreakChar\",type:\"USHORT\",value:0},{name:\"usMaxContext\",type:\"USHORT\",value:0}],e)}var xr={parse:Mn,make:Pn,unicodeRanges:yr,getUnicodeRange:In};function Gn(e,r){var t={},a=new k.Parser(e,r);switch(t.version=a.parseVersion(),t.italicAngle=a.parseFixed(),t.underlinePosition=a.parseShort(),t.underlineThickness=a.parseShort(),t.isFixedPitch=a.parseULong(),t.minMemType42=a.parseULong(),t.maxMemType42=a.parseULong(),t.minMemType1=a.parseULong(),t.maxMemType1=a.parseULong(),t.version){case 1:t.names=xe.slice();break;case 2:t.numberOfGlyphs=a.parseUShort(),t.glyphNameIndex=new Array(t.numberOfGlyphs);for(var n=0;n<t.numberOfGlyphs;n++)t.glyphNameIndex[n]=a.parseUShort();t.names=[];for(var s=0;s<t.numberOfGlyphs;s++)if(t.glyphNameIndex[s]>=xe.length){var i=a.parseChar();t.names.push(a.parseString(i))}break;case 2.5:t.numberOfGlyphs=a.parseUShort(),t.offset=new Array(t.numberOfGlyphs);for(var u=0;u<t.numberOfGlyphs;u++)t.offset[u]=a.parseChar();break}return t}function Nn(){return new b.Table(\"post\",[{name:\"version\",type:\"FIXED\",value:196608},{name:\"italicAngle\",type:\"FIXED\",value:0},{name:\"underlinePosition\",type:\"FWORD\",value:0},{name:\"underlineThickness\",type:\"FWORD\",value:0},{name:\"isFixedPitch\",type:\"ULONG\",value:0},{name:\"minMemType42\",type:\"ULONG\",value:0},{name:\"maxMemType42\",type:\"ULONG\",value:0},{name:\"minMemType1\",type:\"ULONG\",value:0},{name:\"maxMemType1\",type:\"ULONG\",value:0}])}var Qt={parse:Gn,make:Nn},ee=new Array(9);ee[1]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:1,coverage:this.parsePointer(v.coverage),deltaGlyphId:this.parseUShort()};if(t===2)return{substFormat:2,coverage:this.parsePointer(v.coverage),substitute:this.parseOffset16List()};U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 1 format must be 1 or 2.\")};ee[2]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Multiple Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),sequences:this.parseListOfLists()}};ee[3]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Alternate Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),alternateSets:this.parseListOfLists()}};ee[4]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB ligature table identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),ligatureSets:this.parseListOfLists(function(){return{ligGlyph:this.parseUShort(),components:this.parseUShortList(this.parseUShort()-1)}})}};var ke={sequenceIndex:v.uShort,lookupListIndex:v.uShort};ee[5]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:t,coverage:this.parsePointer(v.coverage),ruleSets:this.parseListOfLists(function(){var s=this.parseUShort(),i=this.parseUShort();return{input:this.parseUShortList(s-1),lookupRecords:this.parseRecordList(i,ke)}})};if(t===2)return{substFormat:t,coverage:this.parsePointer(v.coverage),classDef:this.parsePointer(v.classDef),classSets:this.parseListOfLists(function(){var s=this.parseUShort(),i=this.parseUShort();return{classes:this.parseUShortList(s-1),lookupRecords:this.parseRecordList(i,ke)}})};if(t===3){var a=this.parseUShort(),n=this.parseUShort();return{substFormat:t,coverages:this.parseList(a,v.pointer(v.coverage)),lookupRecords:this.parseRecordList(n,ke)}}U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 5 format must be 1, 2 or 3.\")};ee[6]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{substFormat:1,coverage:this.parsePointer(v.coverage),chainRuleSets:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ke)}})};if(t===2)return{substFormat:2,coverage:this.parsePointer(v.coverage),backtrackClassDef:this.parsePointer(v.classDef),inputClassDef:this.parsePointer(v.classDef),lookaheadClassDef:this.parsePointer(v.classDef),chainClassSet:this.parseListOfLists(function(){return{backtrack:this.parseUShortList(),input:this.parseUShortList(this.parseShort()-1),lookahead:this.parseUShortList(),lookupRecords:this.parseRecordList(ke)}})};if(t===3)return{substFormat:3,backtrackCoverage:this.parseList(v.pointer(v.coverage)),inputCoverage:this.parseList(v.pointer(v.coverage)),lookaheadCoverage:this.parseList(v.pointer(v.coverage)),lookupRecords:this.parseRecordList(ke)};U.assert(!1,\"0x\"+r.toString(16)+\": lookup type 6 format must be 1, 2 or 3.\")};ee[7]=function(){var r=this.parseUShort();U.argument(r===1,\"GSUB Extension Substitution subtable identifier-format must be 1\");var t=this.parseUShort(),a=new v(this.data,this.offset+this.parseULong());return{substFormat:1,lookupType:t,extension:ee[t].call(a)}};ee[8]=function(){var r=this.parseUShort();return U.argument(r===1,\"GSUB Reverse Chaining Contextual Single Substitution Subtable identifier-format must be 1\"),{substFormat:r,coverage:this.parsePointer(v.coverage),backtrackCoverage:this.parseList(v.pointer(v.coverage)),lookaheadCoverage:this.parseList(v.pointer(v.coverage)),substitutes:this.parseUShortList()}};function Hn(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);return U.argument(a===1||a===1.1,\"Unsupported GSUB table version.\"),a===1?{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(ee)}:{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(ee),variations:t.parseFeatureVariationsList()}}var Ue=new Array(9);Ue[1]=function(r){return r.substFormat===1?new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)},{name:\"deltaGlyphID\",type:\"USHORT\",value:r.deltaGlyphId}]):new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:2},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.ushortList(\"substitute\",r.substitute)))};Ue[2]=function(r){return U.assert(r.substFormat===1,\"Lookup type 2 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"seqSet\",r.sequences,function(t){return new b.Table(\"sequenceSetTable\",b.ushortList(\"sequence\",t))})))};Ue[3]=function(r){return U.assert(r.substFormat===1,\"Lookup type 3 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"altSet\",r.alternateSets,function(t){return new b.Table(\"alternateSetTable\",b.ushortList(\"alternate\",t))})))};Ue[4]=function(r){return U.assert(r.substFormat===1,\"Lookup type 4 substFormat must be 1.\"),new b.Table(\"substitutionTable\",[{name:\"substFormat\",type:\"USHORT\",value:1},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"ligSet\",r.ligatureSets,function(t){return new b.Table(\"ligatureSetTable\",b.tableList(\"ligature\",t,function(a){return new b.Table(\"ligatureTable\",[{name:\"ligGlyph\",type:\"USHORT\",value:a.ligGlyph}].concat(b.ushortList(\"component\",a.components,a.components.length+1)))}))})))};Ue[6]=function(r){if(r.substFormat===1){var t=new b.Table(\"chainContextTable\",[{name:\"substFormat\",type:\"USHORT\",value:r.substFormat},{name:\"coverage\",type:\"TABLE\",value:new b.Coverage(r.coverage)}].concat(b.tableList(\"chainRuleSet\",r.chainRuleSets,function(s){return new b.Table(\"chainRuleSetTable\",b.tableList(\"chainRule\",s,function(i){var u=b.ushortList(\"backtrackGlyph\",i.backtrack,i.backtrack.length).concat(b.ushortList(\"inputGlyph\",i.input,i.input.length+1)).concat(b.ushortList(\"lookaheadGlyph\",i.lookahead,i.lookahead.length)).concat(b.ushortList(\"substitution\",[],i.lookupRecords.length));return i.lookupRecords.forEach(function(o,l){u=u.concat({name:\"sequenceIndex\"+l,type:\"USHORT\",value:o.sequenceIndex}).concat({name:\"lookupListIndex\"+l,type:\"USHORT\",value:o.lookupListIndex})}),new b.Table(\"chainRuleTable\",u)}))})));return t}else if(r.substFormat===2)U.assert(!1,\"lookup type 6 format 2 is not yet supported.\");else if(r.substFormat===3){var a=[{name:\"substFormat\",type:\"USHORT\",value:r.substFormat}];a.push({name:\"backtrackGlyphCount\",type:\"USHORT\",value:r.backtrackCoverage.length}),r.backtrackCoverage.forEach(function(s,i){a.push({name:\"backtrackCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"inputGlyphCount\",type:\"USHORT\",value:r.inputCoverage.length}),r.inputCoverage.forEach(function(s,i){a.push({name:\"inputCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"lookaheadGlyphCount\",type:\"USHORT\",value:r.lookaheadCoverage.length}),r.lookaheadCoverage.forEach(function(s,i){a.push({name:\"lookaheadCoverage\"+i,type:\"TABLE\",value:new b.Coverage(s)})}),a.push({name:\"substitutionCount\",type:\"USHORT\",value:r.lookupRecords.length}),r.lookupRecords.forEach(function(s,i){a=a.concat({name:\"sequenceIndex\"+i,type:\"USHORT\",value:s.sequenceIndex}).concat({name:\"lookupListIndex\"+i,type:\"USHORT\",value:s.lookupListIndex})});var n=new b.Table(\"chainContextTable\",a);return n}U.assert(!1,\"lookup type 6 format must be 1, 2 or 3.\")};function zn(e){return new b.Table(\"GSUB\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"scripts\",type:\"TABLE\",value:new b.ScriptList(e.scripts)},{name:\"features\",type:\"TABLE\",value:new b.FeatureList(e.features)},{name:\"lookups\",type:\"TABLE\",value:new b.LookupList(e.lookups,Ue)}])}var Kt={parse:Hn,make:zn};function Wn(e,r){var t=new k.Parser(e,r),a=t.parseULong();U.argument(a===1,\"Unsupported META table version.\"),t.parseULong(),t.parseULong();for(var n=t.parseULong(),s={},i=0;i<n;i++){var u=t.parseTag(),o=t.parseULong(),l=t.parseULong(),f=Fe.UTF8(e,r+o,l);s[u]=f}return s}function _n(e){var r=Object.keys(e).length,t=\"\",a=16+r*12,n=new b.Table(\"meta\",[{name:\"version\",type:\"ULONG\",value:1},{name:\"flags\",type:\"ULONG\",value:0},{name:\"offset\",type:\"ULONG\",value:a},{name:\"numTags\",type:\"ULONG\",value:r}]);for(var s in e){var i=t.length;t+=e[s],n.fields.push({name:\"tag \"+s,type:\"TAG\",value:s}),n.fields.push({name:\"offset \"+s,type:\"ULONG\",value:a+i}),n.fields.push({name:\"length \"+s,type:\"ULONG\",value:e[s].length})}return n.fields.push({name:\"stringPool\",type:\"CHARARRAY\",value:t}),n}var Jt={parse:Wn,make:_n};function Zr(e){return Math.log(e)/Math.log(2)|0}function Er(e){for(;e.length%4!==0;)e.push(0);for(var r=0,t=0;t<e.length;t+=4)r+=(e[t]<<24)+(e[t+1]<<16)+(e[t+2]<<8)+e[t+3];return r%=Math.pow(2,32),r}function Qr(e,r,t,a){return new b.Record(\"Table Record\",[{name:\"tag\",type:\"TAG\",value:e!==void 0?e:\"\"},{name:\"checkSum\",type:\"ULONG\",value:r!==void 0?r:0},{name:\"offset\",type:\"ULONG\",value:t!==void 0?t:0},{name:\"length\",type:\"ULONG\",value:a!==void 0?a:0}])}function jt(e){var r=new b.Table(\"sfnt\",[{name:\"version\",type:\"TAG\",value:\"OTTO\"},{name:\"numTables\",type:\"USHORT\",value:0},{name:\"searchRange\",type:\"USHORT\",value:0},{name:\"entrySelector\",type:\"USHORT\",value:0},{name:\"rangeShift\",type:\"USHORT\",value:0}]);r.tables=e,r.numTables=e.length;var t=Math.pow(2,Zr(r.numTables));r.searchRange=16*t,r.entrySelector=Zr(t),r.rangeShift=r.numTables*16-r.searchRange;for(var a=[],n=[],s=r.sizeOf()+Qr().sizeOf()*r.numTables;s%4!==0;)s+=1,n.push({name:\"padding\",type:\"BYTE\",value:0});for(var i=0;i<e.length;i+=1){var u=e[i];U.argument(u.tableName.length===4,\"Table name\"+u.tableName+\" is invalid.\");var o=u.sizeOf(),l=Qr(u.tableName,Er(u.encode()),s,o);for(a.push({name:l.tag+\" Table Record\",type:\"RECORD\",value:l}),n.push({name:u.tableName+\" table\",type:\"RECORD\",value:u}),s+=o,U.argument(!isNaN(s),\"Something went wrong calculating the offset.\");s%4!==0;)s+=1,n.push({name:\"padding\",type:\"BYTE\",value:0})}return a.sort(function(f,h){return f.value.tag>h.value.tag?1:-1}),r.fields=r.fields.concat(a),r.fields=r.fields.concat(n),r}function Kr(e,r,t){for(var a=0;a<r.length;a+=1){var n=e.charToGlyphIndex(r[a]);if(n>0){var s=e.glyphs.get(n);return s.getMetrics()}}return t}function Vn(e){for(var r=0,t=0;t<e.length;t+=1)r+=e[t];return r/e.length}function qn(e){for(var r=[],t=[],a=[],n=[],s=[],i=[],u=[],o,l=0,f=0,h=0,p=0,c=0,d=0;d<e.glyphs.length;d+=1){var x=e.glyphs.get(d),m=x.unicode|0;if(isNaN(x.advanceWidth))throw new Error(\"Glyph \"+x.name+\" (\"+d+\"): advanceWidth is not a number.\");(o>m||o===void 0)&&m>0&&(o=m),l<m&&(l=m);var y=xr.getUnicodeRange(m);if(y<32)f|=1<<y;else if(y<64)h|=1<<y-32;else if(y<96)p|=1<<y-64;else if(y<123)c|=1<<y-96;else throw new Error(\"Unicode ranges bits > 123 are reserved for internal usage\");if(x.name!==\".notdef\"){var C=x.getMetrics();r.push(C.xMin),t.push(C.yMin),a.push(C.xMax),n.push(C.yMax),i.push(C.leftSideBearing),u.push(C.rightSideBearing),s.push(x.advanceWidth)}}var S={xMin:Math.min.apply(null,r),yMin:Math.min.apply(null,t),xMax:Math.max.apply(null,a),yMax:Math.max.apply(null,n),advanceWidthMax:Math.max.apply(null,s),advanceWidthAvg:Vn(s),minLeftSideBearing:Math.min.apply(null,i),maxLeftSideBearing:Math.max.apply(null,i),minRightSideBearing:Math.min.apply(null,u)};S.ascender=e.ascender,S.descender=e.descender;var R=Nt.make({flags:3,unitsPerEm:e.unitsPerEm,xMin:S.xMin,yMin:S.yMin,xMax:S.xMax,yMax:S.yMax,lowestRecPPEM:3,createdTimestamp:e.createdTimestamp}),O=Ht.make({ascender:S.ascender,descender:S.descender,advanceWidthMax:S.advanceWidthMax,minLeftSideBearing:S.minLeftSideBearing,minRightSideBearing:S.minRightSideBearing,xMaxExtent:S.maxLeftSideBearing+(S.xMax-S.xMin),numberOfHMetrics:e.glyphs.length}),D=_t.make(e.glyphs.length),L=xr.make(Object.assign({xAvgCharWidth:Math.round(S.advanceWidthAvg),usFirstCharIndex:o,usLastCharIndex:l,ulUnicodeRange1:f,ulUnicodeRange2:h,ulUnicodeRange3:p,ulUnicodeRange4:c,sTypoAscender:S.ascender,sTypoDescender:S.descender,sTypoLineGap:0,usWinAscent:S.yMax,usWinDescent:Math.abs(S.yMin),ulCodePageRange1:1,sxHeight:Kr(e,\"xyvw\",{yMax:Math.round(S.ascender/2)}).yMax,sCapHeight:Kr(e,\"HIKLEFJMNTZBDPRAGOQSUVWXY\",S).yMax,usDefaultChar:e.hasChar(\" \")?32:0,usBreakChar:e.hasChar(\" \")?32:0},e.tables.os2)),F=zt.make(e.glyphs),G=Et.make(e.glyphs),Y=e.getEnglishName(\"fontFamily\"),Z=e.getEnglishName(\"fontSubfamily\"),j=Y+\" \"+Z,$=e.getEnglishName(\"postScriptName\");$||($=Y.replace(/\\s/g,\"\")+\"-\"+Z);var M={};for(var N in e.names)M[N]=e.names[N];M.uniqueID||(M.uniqueID={en:e.getEnglishName(\"manufacturer\")+\":\"+j}),M.postScriptName||(M.postScriptName={en:$}),M.preferredFamily||(M.preferredFamily=e.names.fontFamily),M.preferredSubfamily||(M.preferredSubfamily=e.names.fontSubfamily);var W=[],_=Zt.make(M,W),V=W.length>0?Wt.make(W):void 0,H=Qt.make(),X=Gt.make(e.glyphs,{version:e.getEnglishName(\"version\"),fullName:j,familyName:Y,weightName:Z,postScriptName:$,unitsPerEm:e.unitsPerEm,fontBBox:[0,S.yMin,S.ascender,S.advanceWidthMax]}),A=e.metas&&Object.keys(e.metas).length>0?Jt.make(e.metas):void 0,q=[R,O,D,L,_,G,H,X,F];V&&q.push(V),e.tables.gsub&&q.push(Kt.make(e.tables.gsub)),A&&q.push(A);for(var rr=jt(q),ha=rr.encode(),ca=Er(ha),tr=rr.fields,Ar=!1,Ge=0;Ge<tr.length;Ge+=1)if(tr[Ge].name===\"head table\"){tr[Ge].value.checkSumAdjustment=2981146554-ca,Ar=!0;break}if(!Ar)throw new Error(\"Could not find head table with checkSum to adjust.\");return rr}var Xn={make:jt,fontToTable:qn,computeCheckSum:Er};function sr(e,r){for(var t=0,a=e.length-1;t<=a;){var n=t+a>>>1,s=e[n].tag;if(s===r)return n;s<r?t=n+1:a=n-1}return-t-1}function Jr(e,r){for(var t=0,a=e.length-1;t<=a;){var n=t+a>>>1,s=e[n];if(s===r)return n;s<r?t=n+1:a=n-1}return-t-1}function jr(e,r){for(var t,a=0,n=e.length-1;a<=n;){var s=a+n>>>1;t=e[s];var i=t.start;if(i===r)return t;i<r?a=s+1:n=s-1}if(a>0)return t=e[a-1],r>t.end?0:t}function Ae(e,r){this.font=e,this.tableName=r}Ae.prototype={searchTag:sr,binSearch:Jr,getTable:function(e){var r=this.font.tables[this.tableName];return!r&&e&&(r=this.font.tables[this.tableName]=this.createDefaultTable()),r},getScriptNames:function(){var e=this.getTable();return e?e.scripts.map(function(r){return r.tag}):[]},getDefaultScriptName:function(){var e=this.getTable();if(!!e){for(var r=!1,t=0;t<e.scripts.length;t++){var a=e.scripts[t].tag;if(a===\"DFLT\")return a;a===\"latn\"&&(r=!0)}if(r)return\"latn\"}},getScriptTable:function(e,r){var t=this.getTable(r);if(t){e=e||\"DFLT\";var a=t.scripts,n=sr(t.scripts,e);if(n>=0)return a[n].script;if(r){var s={tag:e,script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}};return a.splice(-1-n,0,s),s.script}}},getLangSysTable:function(e,r,t){var a=this.getScriptTable(e,t);if(a){if(!r||r===\"dflt\"||r===\"DFLT\")return a.defaultLangSys;var n=sr(a.langSysRecords,r);if(n>=0)return a.langSysRecords[n].langSys;if(t){var s={tag:r,langSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]}};return a.langSysRecords.splice(-1-n,0,s),s.langSys}}},getFeatureTable:function(e,r,t,a){var n=this.getLangSysTable(e,r,a);if(n){for(var s,i=n.featureIndexes,u=this.font.tables[this.tableName].features,o=0;o<i.length;o++)if(s=u[i[o]],s.tag===t)return s.feature;if(a){var l=u.length;return U.assert(l===0||t>=u[l-1].tag,\"Features must be added in alphabetical order.\"),s={tag:t,feature:{params:0,lookupListIndexes:[]}},u.push(s),i.push(l),s.feature}}},getLookupTables:function(e,r,t,a,n){var s=this.getFeatureTable(e,r,t,n),i=[];if(s){for(var u,o=s.lookupListIndexes,l=this.font.tables[this.tableName].lookups,f=0;f<o.length;f++)u=l[o[f]],u.lookupType===a&&i.push(u);if(i.length===0&&n){u={lookupType:a,lookupFlag:0,subtables:[],markFilteringSet:void 0};var h=l.length;return l.push(u),o.push(h),[u]}}return i},getGlyphClass:function(e,r){switch(e.format){case 1:return e.startGlyph<=r&&r<e.startGlyph+e.classes.length?e.classes[r-e.startGlyph]:0;case 2:var t=jr(e.ranges,r);return t?t.classId:0}},getCoverageIndex:function(e,r){switch(e.format){case 1:var t=Jr(e.glyphs,r);return t>=0?t:-1;case 2:var a=jr(e.ranges,r);return a?a.index+r-a.start:-1}},expandCoverage:function(e){if(e.format===1)return e.glyphs;for(var r=[],t=e.ranges,a=0;a<t.length;a++)for(var n=t[a],s=n.start,i=n.end,u=s;u<=i;u++)r.push(u);return r}};function Be(e){Ae.call(this,e,\"gpos\")}Be.prototype=Ae.prototype;Be.prototype.init=function(){var e=this.getDefaultScriptName();this.defaultKerningTables=this.getKerningTables(e)};Be.prototype.getKerningValue=function(e,r,t){for(var a=0;a<e.length;a++)for(var n=e[a].subtables,s=0;s<n.length;s++){var i=n[s],u=this.getCoverageIndex(i.coverage,r);if(!(u<0))switch(i.posFormat){case 1:for(var o=i.pairSets[u],l=0;l<o.length;l++){var f=o[l];if(f.secondGlyph===t)return f.value1&&f.value1.xAdvance||0}break;case 2:var h=this.getGlyphClass(i.classDef1,r),p=this.getGlyphClass(i.classDef2,t),c=i.classRecords[h][p];return c.value1&&c.value1.xAdvance||0}}return 0};Be.prototype.getKerningTables=function(e,r){if(this.font.tables.gpos)return this.getLookupTables(e,r,\"kern\",2)};function K(e){Ae.call(this,e,\"gsub\")}function Yn(e,r){var t=e.length;if(t!==r.length)return!1;for(var a=0;a<t;a++)if(e[a]!==r[a])return!1;return!0}function Or(e,r,t){for(var a=e.subtables,n=0;n<a.length;n++){var s=a[n];if(s.substFormat===r)return s}if(t)return a.push(t),t}K.prototype=Ae.prototype;K.prototype.createDefaultTable=function(){return{version:1,scripts:[{tag:\"DFLT\",script:{defaultLangSys:{reserved:0,reqFeatureIndex:65535,featureIndexes:[]},langSysRecords:[]}}],features:[],lookups:[]}};K.prototype.getSingle=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,1),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++){var o=i[u],l=this.expandCoverage(o.coverage),f=void 0;if(o.substFormat===1){var h=o.deltaGlyphId;for(f=0;f<l.length;f++){var p=l[f];a.push({sub:p,by:p+h})}}else{var c=o.substitute;for(f=0;f<l.length;f++)a.push({sub:l[f],by:c[f]})}}return a};K.prototype.getMultiple=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,2),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++){var o=i[u],l=this.expandCoverage(o.coverage),f=void 0;for(f=0;f<l.length;f++){var h=l[f],p=o.sequences[f];a.push({sub:h,by:p})}}return a};K.prototype.getAlternates=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,3),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++)for(var o=i[u],l=this.expandCoverage(o.coverage),f=o.alternateSets,h=0;h<l.length;h++)a.push({sub:l[h],by:f[h]});return a};K.prototype.getLigatures=function(e,r,t){for(var a=[],n=this.getLookupTables(r,t,e,4),s=0;s<n.length;s++)for(var i=n[s].subtables,u=0;u<i.length;u++)for(var o=i[u],l=this.expandCoverage(o.coverage),f=o.ligatureSets,h=0;h<l.length;h++)for(var p=l[h],c=f[h],d=0;d<c.length;d++){var x=c[d];a.push({sub:[p].concat(x.components),by:x.ligGlyph})}return a};K.prototype.addSingle=function(e,r,t,a){var n=this.getLookupTables(t,a,e,1,!0)[0],s=Or(n,2,{substFormat:2,coverage:{format:1,glyphs:[]},substitute:[]});U.assert(s.coverage.format===1,\"Single: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.substitute.splice(u,0,0)),s.substitute[u]=r.by};K.prototype.addMultiple=function(e,r,t,a){U.assert(r.by instanceof Array&&r.by.length>1,'Multiple: \"by\" must be an array of two or more ids');var n=this.getLookupTables(t,a,e,2,!0)[0],s=Or(n,1,{substFormat:1,coverage:{format:1,glyphs:[]},sequences:[]});U.assert(s.coverage.format===1,\"Multiple: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.sequences.splice(u,0,0)),s.sequences[u]=r.by};K.prototype.addAlternate=function(e,r,t,a){var n=this.getLookupTables(t,a,e,3,!0)[0],s=Or(n,1,{substFormat:1,coverage:{format:1,glyphs:[]},alternateSets:[]});U.assert(s.coverage.format===1,\"Alternate: unable to modify coverage table format \"+s.coverage.format);var i=r.sub,u=this.binSearch(s.coverage.glyphs,i);u<0&&(u=-1-u,s.coverage.glyphs.splice(u,0,i),s.alternateSets.splice(u,0,0)),s.alternateSets[u]=r.by};K.prototype.addLigature=function(e,r,t,a){var n=this.getLookupTables(t,a,e,4,!0)[0],s=n.subtables[0];s||(s={substFormat:1,coverage:{format:1,glyphs:[]},ligatureSets:[]},n.subtables[0]=s),U.assert(s.coverage.format===1,\"Ligature: unable to modify coverage table format \"+s.coverage.format);var i=r.sub[0],u=r.sub.slice(1),o={ligGlyph:r.by,components:u},l=this.binSearch(s.coverage.glyphs,i);if(l>=0){for(var f=s.ligatureSets[l],h=0;h<f.length;h++)if(Yn(f[h].components,u))return;f.push(o)}else l=-1-l,s.coverage.glyphs.splice(l,0,i),s.ligatureSets.splice(l,0,[o])};K.prototype.getFeature=function(e,r,t){if(/ss\\d\\d/.test(e))return this.getSingle(e,r,t);switch(e){case\"aalt\":case\"salt\":return this.getSingle(e,r,t).concat(this.getAlternates(e,r,t));case\"dlig\":case\"liga\":case\"rlig\":return this.getLigatures(e,r,t);case\"ccmp\":return this.getMultiple(e,r,t).concat(this.getLigatures(e,r,t));case\"stch\":return this.getMultiple(e,r,t)}};K.prototype.add=function(e,r,t,a){if(/ss\\d\\d/.test(e))return this.addSingle(e,r,t,a);switch(e){case\"aalt\":case\"salt\":return typeof r.by==\"number\"?this.addSingle(e,r,t,a):this.addAlternate(e,r,t,a);case\"dlig\":case\"liga\":case\"rlig\":return this.addLigature(e,r,t,a);case\"ccmp\":return r.by instanceof Array?this.addMultiple(e,r,t,a):this.addLigature(e,r,t,a)}};function Zn(){return typeof window<\"u\"}function $t(e){for(var r=new ArrayBuffer(e.length),t=new Uint8Array(r),a=0;a<e.length;++a)t[a]=e[a];return r}function Qn(e){for(var r=new Buffer(e.byteLength),t=new Uint8Array(e),a=0;a<r.length;++a)r[a]=t[a];return r}function Ee(e,r){if(!e)throw r}function $r(e,r,t,a,n){var s;return(r&a)>0?(s=e.parseByte(),(r&n)===0&&(s=-s),s=t+s):(r&n)>0?s=t:s=t+e.parseShort(),s}function ea(e,r,t){var a=new k.Parser(r,t);e.numberOfContours=a.parseShort(),e._xMin=a.parseShort(),e._yMin=a.parseShort(),e._xMax=a.parseShort(),e._yMax=a.parseShort();var n,s;if(e.numberOfContours>0){for(var i=e.endPointIndices=[],u=0;u<e.numberOfContours;u+=1)i.push(a.parseUShort());e.instructionLength=a.parseUShort(),e.instructions=[];for(var o=0;o<e.instructionLength;o+=1)e.instructions.push(a.parseByte());var l=i[i.length-1]+1;n=[];for(var f=0;f<l;f+=1)if(s=a.parseByte(),n.push(s),(s&8)>0)for(var h=a.parseByte(),p=0;p<h;p+=1)n.push(s),f+=1;if(U.argument(n.length===l,\"Bad flags.\"),i.length>0){var c=[],d;if(l>0){for(var x=0;x<l;x+=1)s=n[x],d={},d.onCurve=!!(s&1),d.lastPointOfContour=i.indexOf(x)>=0,c.push(d);for(var m=0,y=0;y<l;y+=1)s=n[y],d=c[y],d.x=$r(a,s,m,2,16),m=d.x;for(var C=0,S=0;S<l;S+=1)s=n[S],d=c[S],d.y=$r(a,s,C,4,32),C=d.y}e.points=c}else e.points=[]}else if(e.numberOfContours===0)e.points=[];else{e.isComposite=!0,e.points=[],e.components=[];for(var R=!0;R;){n=a.parseUShort();var O={glyphIndex:a.parseUShort(),xScale:1,scale01:0,scale10:0,yScale:1,dx:0,dy:0};(n&1)>0?(n&2)>0?(O.dx=a.parseShort(),O.dy=a.parseShort()):O.matchedPoints=[a.parseUShort(),a.parseUShort()]:(n&2)>0?(O.dx=a.parseChar(),O.dy=a.parseChar()):O.matchedPoints=[a.parseByte(),a.parseByte()],(n&8)>0?O.xScale=O.yScale=a.parseF2Dot14():(n&64)>0?(O.xScale=a.parseF2Dot14(),O.yScale=a.parseF2Dot14()):(n&128)>0&&(O.xScale=a.parseF2Dot14(),O.scale01=a.parseF2Dot14(),O.scale10=a.parseF2Dot14(),O.yScale=a.parseF2Dot14()),e.components.push(O),R=!!(n&32)}if(n&256){e.instructionLength=a.parseUShort(),e.instructions=[];for(var D=0;D<e.instructionLength;D+=1)e.instructions.push(a.parseByte())}}}function ir(e,r){for(var t=[],a=0;a<e.length;a+=1){var n=e[a],s={x:r.xScale*n.x+r.scale01*n.y+r.dx,y:r.scale10*n.x+r.yScale*n.y+r.dy,onCurve:n.onCurve,lastPointOfContour:n.lastPointOfContour};t.push(s)}return t}function Kn(e){for(var r=[],t=[],a=0;a<e.length;a+=1){var n=e[a];t.push(n),n.lastPointOfContour&&(r.push(t),t=[])}return U.argument(t.length===0,\"There are still points left in the current contour.\"),r}function ra(e){var r=new P;if(!e)return r;for(var t=Kn(e),a=0;a<t.length;++a){var n=t[a],s=null,i=n[n.length-1],u=n[0];if(i.onCurve)r.moveTo(i.x,i.y);else if(u.onCurve)r.moveTo(u.x,u.y);else{var o={x:(i.x+u.x)*.5,y:(i.y+u.y)*.5};r.moveTo(o.x,o.y)}for(var l=0;l<n.length;++l)if(s=i,i=u,u=n[(l+1)%n.length],i.onCurve)r.lineTo(i.x,i.y);else{var f=s,h=u;s.onCurve||(f={x:(i.x+s.x)*.5,y:(i.y+s.y)*.5}),u.onCurve||(h={x:(i.x+u.x)*.5,y:(i.y+u.y)*.5}),r.quadraticCurveTo(i.x,i.y,h.x,h.y)}r.closePath()}return r}function ta(e,r){if(r.isComposite)for(var t=0;t<r.components.length;t+=1){var a=r.components[t],n=e.get(a.glyphIndex);if(n.getPath(),n.points){var s=void 0;if(a.matchedPoints===void 0)s=ir(n.points,a);else{if(a.matchedPoints[0]>r.points.length-1||a.matchedPoints[1]>n.points.length-1)throw Error(\"Matched points out of range in \"+r.name);var i=r.points[a.matchedPoints[0]],u=n.points[a.matchedPoints[1]],o={xScale:a.xScale,scale01:a.scale01,scale10:a.scale10,yScale:a.yScale,dx:0,dy:0};u=ir([u],o)[0],o.dx=i.x-u.x,o.dy=i.y-u.y,s=ir(n.points,o)}r.points=r.points.concat(s)}}return ra(r.points)}function Jn(e,r,t,a){for(var n=new ue.GlyphSet(a),s=0;s<t.length-1;s+=1){var i=t[s],u=t[s+1];i!==u?n.push(s,ue.ttfGlyphLoader(a,s,ea,e,r+i,ta)):n.push(s,ue.glyphLoader(a,s))}return n}function jn(e,r,t,a){var n=new ue.GlyphSet(a);return a._push=function(s){var i=t[s],u=t[s+1];i!==u?n.push(s,ue.ttfGlyphLoader(a,s,ea,e,r+i,ta)):n.push(s,ue.glyphLoader(a,s))},n}function $n(e,r,t,a,n){return n.lowMemory?jn(e,r,t,a):Jn(e,r,t,a)}var aa={getPath:ra,parse:$n},na,Se,sa,br;function ia(e){this.font=e,this.getCommands=function(r){return aa.getPath(r).commands},this._fpgmState=this._prepState=void 0,this._errorState=0}function es(e){return e}function oa(e){return Math.sign(e)*Math.round(Math.abs(e))}function rs(e){return Math.sign(e)*Math.round(Math.abs(e*2))/2}function ts(e){return Math.sign(e)*(Math.round(Math.abs(e)+.5)-.5)}function as(e){return Math.sign(e)*Math.ceil(Math.abs(e))}function ns(e){return Math.sign(e)*Math.floor(Math.abs(e))}var ua=function(e){var r=this.srPeriod,t=this.srPhase,a=this.srThreshold,n=1;return e<0&&(e=-e,n=-1),e+=a-t,e=Math.trunc(e/r)*r,e+=t,e<0?t*n:e*n},oe={x:1,y:0,axis:\"x\",distance:function(e,r,t,a){return(t?e.xo:e.x)-(a?r.xo:r.x)},interpolate:function(e,r,t,a){var n,s,i,u,o,l,f;if(!a||a===this){if(n=e.xo-r.xo,s=e.xo-t.xo,o=r.x-r.xo,l=t.x-t.xo,i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){e.x=e.xo+(o+l)/2;return}e.x=e.xo+(o*u+l*i)/f;return}if(n=a.distance(e,r,!0,!0),s=a.distance(e,t,!0,!0),o=a.distance(r,r,!1,!0),l=a.distance(t,t,!1,!0),i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){oe.setRelative(e,e,(o+l)/2,a,!0);return}oe.setRelative(e,e,(o*u+l*i)/f,a,!0)},normalSlope:Number.NEGATIVE_INFINITY,setRelative:function(e,r,t,a,n){if(!a||a===this){e.x=(n?r.xo:r.x)+t;return}var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y;e.x=u+(e.y-o)/a.normalSlope},slope:0,touch:function(e){e.xTouched=!0},touched:function(e){return e.xTouched},untouch:function(e){e.xTouched=!1}},le={x:0,y:1,axis:\"y\",distance:function(e,r,t,a){return(t?e.yo:e.y)-(a?r.yo:r.y)},interpolate:function(e,r,t,a){var n,s,i,u,o,l,f;if(!a||a===this){if(n=e.yo-r.yo,s=e.yo-t.yo,o=r.y-r.yo,l=t.y-t.yo,i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){e.y=e.yo+(o+l)/2;return}e.y=e.yo+(o*u+l*i)/f;return}if(n=a.distance(e,r,!0,!0),s=a.distance(e,t,!0,!0),o=a.distance(r,r,!1,!0),l=a.distance(t,t,!1,!0),i=Math.abs(n),u=Math.abs(s),f=i+u,f===0){le.setRelative(e,e,(o+l)/2,a,!0);return}le.setRelative(e,e,(o*u+l*i)/f,a,!0)},normalSlope:0,setRelative:function(e,r,t,a,n){if(!a||a===this){e.y=(n?r.yo:r.y)+t;return}var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y;e.y=o+a.normalSlope*(e.x-u)},slope:Number.POSITIVE_INFINITY,touch:function(e){e.yTouched=!0},touched:function(e){return e.yTouched},untouch:function(e){e.yTouched=!1}};Object.freeze(oe);Object.freeze(le);function Ie(e,r){this.x=e,this.y=r,this.axis=void 0,this.slope=r/e,this.normalSlope=-e/r,Object.freeze(this)}Ie.prototype.distance=function(e,r,t,a){return this.x*oe.distance(e,r,t,a)+this.y*le.distance(e,r,t,a)};Ie.prototype.interpolate=function(e,r,t,a){var n,s,i,u,o,l,f;if(i=a.distance(e,r,!0,!0),u=a.distance(e,t,!0,!0),n=a.distance(r,r,!1,!0),s=a.distance(t,t,!1,!0),o=Math.abs(i),l=Math.abs(u),f=o+l,f===0){this.setRelative(e,e,(n+s)/2,a,!0);return}this.setRelative(e,e,(n*l+s*o)/f,a,!0)};Ie.prototype.setRelative=function(e,r,t,a,n){a=a||this;var s=n?r.xo:r.x,i=n?r.yo:r.y,u=s+t*a.x,o=i+t*a.y,l=a.normalSlope,f=this.slope,h=e.x,p=e.y;e.x=(f*h-l*u+o-p)/(f-l),e.y=f*(e.x-h)+p};Ie.prototype.touch=function(e){e.xTouched=!0,e.yTouched=!0};function Me(e,r){var t=Math.sqrt(e*e+r*r);return e/=t,r/=t,e===1&&r===0?oe:e===0&&r===1?le:new Ie(e,r)}function fe(e,r,t,a){this.x=this.xo=Math.round(e*64)/64,this.y=this.yo=Math.round(r*64)/64,this.lastPointOfContour=t,this.onCurve=a,this.prevPointOnContour=void 0,this.nextPointOnContour=void 0,this.xTouched=!1,this.yTouched=!1,Object.preventExtensions(this)}fe.prototype.nextTouched=function(e){for(var r=this.nextPointOnContour;!e.touched(r)&&r!==this;)r=r.nextPointOnContour;return r};fe.prototype.prevTouched=function(e){for(var r=this.prevPointOnContour;!e.touched(r)&&r!==this;)r=r.prevPointOnContour;return r};var De=Object.freeze(new fe(0,0)),ss={cvCutIn:17/16,deltaBase:9,deltaShift:.125,loop:1,minDis:1,autoFlip:!0};function de(e,r){switch(this.env=e,this.stack=[],this.prog=r,e){case\"glyf\":this.zp0=this.zp1=this.zp2=1,this.rp0=this.rp1=this.rp2=0;case\"prep\":this.fv=this.pv=this.dpv=oe,this.round=oa}}ia.prototype.exec=function(e,r){if(typeof r!=\"number\")throw new Error(\"Point size is not a number!\");if(!(this._errorState>2)){var t=this.font,a=this._prepState;if(!a||a.ppem!==r){var n=this._fpgmState;if(!n){de.prototype=ss,n=this._fpgmState=new de(\"fpgm\",t.tables.fpgm),n.funcs=[],n.font=t,exports.DEBUG&&(console.log(\"---EXEC FPGM---\"),n.step=-1);try{Se(n)}catch(l){console.log(\"Hinting error in FPGM:\"+l),this._errorState=3;return}}de.prototype=n,a=this._prepState=new de(\"prep\",t.tables.prep),a.ppem=r;var s=t.tables.cvt;if(s)for(var i=a.cvt=new Array(s.length),u=r/t.unitsPerEm,o=0;o<s.length;o++)i[o]=s[o]*u;else a.cvt=[];exports.DEBUG&&(console.log(\"---EXEC PREP---\"),a.step=-1);try{Se(a)}catch(l){this._errorState<2&&console.log(\"Hinting error in PREP:\"+l),this._errorState=2}}if(!(this._errorState>1))try{return sa(e,a)}catch(l){this._errorState<1&&(console.log(\"Hinting error:\"+l),console.log(\"Note: further hinting errors are silenced\")),this._errorState=1;return}}};sa=function(e,r){var t=r.ppem/r.font.unitsPerEm,a=t,n=e.components,s,i,u;if(de.prototype=r,!n)u=new de(\"glyf\",e.instructions),exports.DEBUG&&(console.log(\"---EXEC GLYPH---\"),u.step=-1),br(e,u,t,a),i=u.gZone;else{var o=r.font;i=[],s=[];for(var l=0;l<n.length;l++){var f=n[l],h=o.glyphs.get(f.glyphIndex);u=new de(\"glyf\",h.instructions),exports.DEBUG&&(console.log(\"---EXEC COMP \"+l+\"---\"),u.step=-1),br(h,u,t,a);for(var p=Math.round(f.dx*t),c=Math.round(f.dy*a),d=u.gZone,x=u.contours,m=0;m<d.length;m++){var y=d[m];y.xTouched=y.yTouched=!1,y.xo=y.x=y.x+p,y.yo=y.y=y.y+c}var C=i.length;i.push.apply(i,d);for(var S=0;S<x.length;S++)s.push(x[S]+C)}e.instructions&&!u.inhibitGridFit&&(u=new de(\"glyf\",e.instructions),u.gZone=u.z0=u.z1=u.z2=i,u.contours=s,i.push(new fe(0,0),new fe(Math.round(e.advanceWidth*t),0)),exports.DEBUG&&(console.log(\"---EXEC COMPOSITE---\"),u.step=-1),Se(u),i.length-=2)}return i};br=function(e,r,t,a){for(var n=e.points||[],s=n.length,i=r.gZone=r.z0=r.z1=r.z2=[],u=r.contours=[],o,l=0;l<s;l++)o=n[l],i[l]=new fe(o.x*t,o.y*a,o.lastPointOfContour,o.onCurve);for(var f,h,p=0;p<s;p++)o=i[p],f||(f=o,u.push(p)),o.lastPointOfContour?(o.nextPointOnContour=f,f.prevPointOnContour=o,f=void 0):(h=i[p+1],o.nextPointOnContour=h,h.prevPointOnContour=o);if(!r.inhibitGridFit){if(exports.DEBUG){console.log(\"PROCESSING GLYPH\",r.stack);for(var c=0;c<s;c++)console.log(c,i[c].x,i[c].y)}if(i.push(new fe(0,0),new fe(Math.round(e.advanceWidth*t),0)),Se(r),i.length-=2,exports.DEBUG){console.log(\"FINISHED GLYPH\",r.stack);for(var d=0;d<s;d++)console.log(d,i[d].x,i[d].y)}}};Se=function(e){var r=e.prog;if(!!r){var t=r.length,a;for(e.ip=0;e.ip<t;e.ip++){if(exports.DEBUG&&e.step++,a=na[r[e.ip]],!a)throw new Error(\"unknown instruction: 0x\"+Number(r[e.ip]).toString(16));a(e)}}};function $e(e){for(var r=e.tZone=new Array(e.gZone.length),t=0;t<r.length;t++)r[t]=new fe(0,0)}function la(e,r){var t=e.prog,a=e.ip,n=1,s;do if(s=t[++a],s===88)n++;else if(s===89)n--;else if(s===64)a+=t[a+1]+1;else if(s===65)a+=2*t[a+1]+1;else if(s>=176&&s<=183)a+=s-176+1;else if(s>=184&&s<=191)a+=(s-184+1)*2;else if(r&&n===1&&s===27)break;while(n>0);e.ip=a}function et(e,r){exports.DEBUG&&console.log(r.step,\"SVTCA[\"+e.axis+\"]\"),r.fv=r.pv=r.dpv=e}function rt(e,r){exports.DEBUG&&console.log(r.step,\"SPVTCA[\"+e.axis+\"]\"),r.pv=r.dpv=e}function tt(e,r){exports.DEBUG&&console.log(r.step,\"SFVTCA[\"+e.axis+\"]\"),r.fv=e}function at(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(\"SPVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.pv=r.dpv=Me(u,o)}function nt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(\"SFVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.fv=Me(u,o)}function is(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SPVFS[]\",t,a),e.pv=e.dpv=Me(a,t)}function os(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SPVFS[]\",t,a),e.fv=Me(a,t)}function us(e){var r=e.stack,t=e.pv;exports.DEBUG&&console.log(e.step,\"GPV[]\"),r.push(t.x*16384),r.push(t.y*16384)}function ls(e){var r=e.stack,t=e.fv;exports.DEBUG&&console.log(e.step,\"GFV[]\"),r.push(t.x*16384),r.push(t.y*16384)}function fs(e){e.fv=e.pv,exports.DEBUG&&console.log(e.step,\"SFVTPV[]\")}function ps(e){var r=e.stack,t=r.pop(),a=r.pop(),n=r.pop(),s=r.pop(),i=r.pop(),u=e.z0,o=e.z1,l=u[t],f=u[a],h=o[n],p=o[s],c=e.z2[i];exports.DEBUG&&console.log(\"ISECT[], \",t,a,n,s,i);var d=l.x,x=l.y,m=f.x,y=f.y,C=h.x,S=h.y,R=p.x,O=p.y,D=(d-m)*(S-O)-(x-y)*(C-R),L=d*y-x*m,F=C*O-S*R;c.x=(L*(C-R)-F*(d-m))/D,c.y=(L*(S-O)-F*(x-y))/D}function hs(e){e.rp0=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP0[]\",e.rp0)}function cs(e){e.rp1=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP1[]\",e.rp1)}function vs(e){e.rp2=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SRP2[]\",e.rp2)}function ds(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP0[]\",r),e.zp0=r,r){case 0:e.tZone||$e(e),e.z0=e.tZone;break;case 1:e.z0=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function gs(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP1[]\",r),e.zp1=r,r){case 0:e.tZone||$e(e),e.z1=e.tZone;break;case 1:e.z1=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function ms(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZP2[]\",r),e.zp2=r,r){case 0:e.tZone||$e(e),e.z2=e.tZone;break;case 1:e.z2=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function ys(e){var r=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"SZPS[]\",r),e.zp0=e.zp1=e.zp2=r,r){case 0:e.tZone||$e(e),e.z0=e.z1=e.z2=e.tZone;break;case 1:e.z0=e.z1=e.z2=e.gZone;break;default:throw new Error(\"Invalid zone pointer\")}}function xs(e){e.loop=e.stack.pop(),exports.DEBUG&&console.log(e.step,\"SLOOP[]\",e.loop)}function bs(e){exports.DEBUG&&console.log(e.step,\"RTG[]\"),e.round=oa}function Ss(e){exports.DEBUG&&console.log(e.step,\"RTHG[]\"),e.round=ts}function Ts(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SMD[]\",r),e.minDis=r/64}function ks(e){exports.DEBUG&&console.log(e.step,\"ELSE[]\"),la(e,!1)}function Fs(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"JMPR[]\",r),e.ip+=r-1}function Us(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCVTCI[]\",r),e.cvCutIn=r/64}function Cs(e){var r=e.stack;exports.DEBUG&&console.log(e.step,\"DUP[]\"),r.push(r[r.length-1])}function or(e){exports.DEBUG&&console.log(e.step,\"POP[]\"),e.stack.pop()}function Es(e){exports.DEBUG&&console.log(e.step,\"CLEAR[]\"),e.stack.length=0}function Os(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SWAP[]\"),r.push(t),r.push(a)}function Ls(e){var r=e.stack;exports.DEBUG&&console.log(e.step,\"DEPTH[]\"),r.push(r.length)}function Rs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LOOPCALL[]\",t,a);var n=e.ip,s=e.prog;e.prog=e.funcs[t];for(var i=0;i<a;i++)Se(e),exports.DEBUG&&console.log(++e.step,i+1<a?\"next loopcall\":\"done loopcall\",i);e.ip=n,e.prog=s}function ws(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"CALL[]\",r);var t=e.ip,a=e.prog;e.prog=e.funcs[r],Se(e),e.ip=t,e.prog=a,exports.DEBUG&&console.log(++e.step,\"returning from\",r)}function Ds(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"CINDEX[]\",t),r.push(r[r.length-t])}function As(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"MINDEX[]\",t),r.push(r.splice(r.length-t,1)[0])}function Bs(e){if(e.env!==\"fpgm\")throw new Error(\"FDEF not allowed here\");var r=e.stack,t=e.prog,a=e.ip,n=r.pop(),s=a;for(exports.DEBUG&&console.log(e.step,\"FDEF[]\",n);t[++a]!==45;);e.ip=a,e.funcs[n]=t.slice(s+1,a)}function st(e,r){var t=r.stack.pop(),a=r.z0[t],n=r.fv,s=r.pv;exports.DEBUG&&console.log(r.step,\"MDAP[\"+e+\"]\",t);var i=s.distance(a,De);e&&(i=r.round(i)),n.setRelative(a,De,i,s),n.touch(a),r.rp0=r.rp1=t}function it(e,r){var t=r.z2,a=t.length-2,n,s,i;exports.DEBUG&&console.log(r.step,\"IUP[\"+e.axis+\"]\");for(var u=0;u<a;u++)n=t[u],!e.touched(n)&&(s=n.prevTouched(e),s!==n&&(i=n.nextTouched(e),s===i&&e.setRelative(n,n,e.distance(s,s,!1,!0),e,!0),e.interpolate(n,s,i,e)))}function ot(e,r){for(var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=r.loop,o=r.z2;u--;){var l=t.pop(),f=o[l],h=i.distance(n,n,!1,!0);s.setRelative(f,f,h,i),s.touch(f),exports.DEBUG&&console.log(r.step,(r.loop>1?\"loop \"+(r.loop-u)+\": \":\"\")+\"SHP[\"+(e?\"rp1\":\"rp2\")+\"]\",l)}r.loop=1}function ut(e,r){var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=t.pop(),o=r.z2[r.contours[u]],l=o;exports.DEBUG&&console.log(r.step,\"SHC[\"+e+\"]\",u);var f=i.distance(n,n,!1,!0);do l!==n&&s.setRelative(l,l,f,i),l=l.nextPointOnContour;while(l!==o)}function lt(e,r){var t=r.stack,a=e?r.rp1:r.rp2,n=(e?r.z0:r.z1)[a],s=r.fv,i=r.pv,u=t.pop();exports.DEBUG&&console.log(r.step,\"SHZ[\"+e+\"]\",u);var o;switch(u){case 0:o=r.tZone;break;case 1:o=r.gZone;break;default:throw new Error(\"Invalid zone\")}for(var l,f=i.distance(n,n,!1,!0),h=o.length-2,p=0;p<h;p++)l=o[p],s.setRelative(l,l,f,i)}function Is(e){for(var r=e.stack,t=e.loop,a=e.fv,n=r.pop()/64,s=e.z2;t--;){var i=r.pop(),u=s[i];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-t)+\": \":\"\")+\"SHPIX[]\",i,n),a.setRelative(u,u,n),a.touch(u)}e.loop=1}function Ms(e){for(var r=e.stack,t=e.rp1,a=e.rp2,n=e.loop,s=e.z0[t],i=e.z1[a],u=e.fv,o=e.dpv,l=e.z2;n--;){var f=r.pop(),h=l[f];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-n)+\": \":\"\")+\"IP[]\",f,t,\"<->\",a),u.interpolate(h,s,i,o),u.touch(h)}e.loop=1}function ft(e,r){var t=r.stack,a=t.pop()/64,n=t.pop(),s=r.z1[n],i=r.z0[r.rp0],u=r.fv,o=r.pv;u.setRelative(s,i,a,o),u.touch(s),exports.DEBUG&&console.log(r.step,\"MSIRP[\"+e+\"]\",a,n),r.rp1=r.rp0,r.rp2=n,e&&(r.rp0=n)}function Ps(e){for(var r=e.stack,t=e.rp0,a=e.z0[t],n=e.loop,s=e.fv,i=e.pv,u=e.z1;n--;){var o=r.pop(),l=u[o];exports.DEBUG&&console.log(e.step,(e.loop>1?\"loop \"+(e.loop-n)+\": \":\"\")+\"ALIGNRP[]\",o),s.setRelative(l,a,0,i),s.touch(l)}e.loop=1}function Gs(e){exports.DEBUG&&console.log(e.step,\"RTDG[]\"),e.round=rs}function pt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z0[n],i=r.fv,u=r.pv,o=r.cvt[a];exports.DEBUG&&console.log(r.step,\"MIAP[\"+e+\"]\",a,\"(\",o,\")\",n);var l=u.distance(s,De);e&&(Math.abs(l-o)<r.cvCutIn&&(l=o),l=r.round(l)),i.setRelative(s,De,l,u),r.zp0===0&&(s.xo=s.x,s.yo=s.y),i.touch(s),r.rp0=r.rp1=n}function Ns(e){var r=e.prog,t=e.ip,a=e.stack,n=r[++t];exports.DEBUG&&console.log(e.step,\"NPUSHB[]\",n);for(var s=0;s<n;s++)a.push(r[++t]);e.ip=t}function Hs(e){var r=e.ip,t=e.prog,a=e.stack,n=t[++r];exports.DEBUG&&console.log(e.step,\"NPUSHW[]\",n);for(var s=0;s<n;s++){var i=t[++r]<<8|t[++r];i&32768&&(i=-((i^65535)+1)),a.push(i)}e.ip=r}function zs(e){var r=e.stack,t=e.store;t||(t=e.store=[]);var a=r.pop(),n=r.pop();exports.DEBUG&&console.log(e.step,\"WS\",a,n),t[n]=a}function Ws(e){var r=e.stack,t=e.store,a=r.pop();exports.DEBUG&&console.log(e.step,\"RS\",a);var n=t&&t[a]||0;r.push(n)}function _s(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"WCVTP\",t,a),e.cvt[a]=t/64}function Vs(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"RCVT\",t),r.push(e.cvt[t]*64)}function ht(e,r){var t=r.stack,a=t.pop(),n=r.z2[a];exports.DEBUG&&console.log(r.step,\"GC[\"+e+\"]\",a),t.push(r.dpv.distance(n,De,e,!1)*64)}function ct(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z1[a],i=r.z0[n],u=r.dpv.distance(i,s,e,e);exports.DEBUG&&console.log(r.step,\"MD[\"+e+\"]\",a,n,\"->\",u),r.stack.push(Math.round(u*64))}function qs(e){exports.DEBUG&&console.log(e.step,\"MPPEM[]\"),e.stack.push(e.ppem)}function Xs(e){exports.DEBUG&&console.log(e.step,\"FLIPON[]\"),e.autoFlip=!0}function Ys(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LT[]\",t,a),r.push(a<t?1:0)}function Zs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"LTEQ[]\",t,a),r.push(a<=t?1:0)}function Qs(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"GT[]\",t,a),r.push(a>t?1:0)}function Ks(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"GTEQ[]\",t,a),r.push(a>=t?1:0)}function Js(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"EQ[]\",t,a),r.push(t===a?1:0)}function js(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"NEQ[]\",t,a),r.push(t!==a?1:0)}function $s(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"ODD[]\",t),r.push(Math.trunc(t)%2?1:0)}function ei(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"EVEN[]\",t),r.push(Math.trunc(t)%2?0:1)}function ri(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"IF[]\",r),r||(la(e,!0),exports.DEBUG&&console.log(e.step,\"EIF[]\"))}function ti(e){exports.DEBUG&&console.log(e.step,\"EIF[]\")}function ai(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"AND[]\",t,a),r.push(t&&a?1:0)}function ni(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"OR[]\",t,a),r.push(t||a?1:0)}function si(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"NOT[]\",t),r.push(t?0:1)}function ur(e,r){var t=r.stack,a=t.pop(),n=r.fv,s=r.pv,i=r.ppem,u=r.deltaBase+(e-1)*16,o=r.deltaShift,l=r.z0;exports.DEBUG&&console.log(r.step,\"DELTAP[\"+e+\"]\",a,t);for(var f=0;f<a;f++){var h=t.pop(),p=t.pop(),c=u+((p&240)>>4);if(c===i){var d=(p&15)-8;d>=0&&d++,exports.DEBUG&&console.log(r.step,\"DELTAPFIX\",h,\"by\",d*o);var x=l[h];n.setRelative(x,x,d*o,s)}}}function ii(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"SDB[]\",t),e.deltaBase=t}function oi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"SDS[]\",t),e.deltaShift=Math.pow(.5,t)}function ui(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"ADD[]\",t,a),r.push(a+t)}function li(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"SUB[]\",t,a),r.push(a-t)}function fi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"DIV[]\",t,a),r.push(a*64/t)}function pi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MUL[]\",t,a),r.push(a*t/64)}function hi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"ABS[]\",t),r.push(Math.abs(t))}function ci(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"NEG[]\",t),r.push(-t)}function vi(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"FLOOR[]\",t),r.push(Math.floor(t/64)*64)}function di(e){var r=e.stack,t=r.pop();exports.DEBUG&&console.log(e.step,\"CEILING[]\",t),r.push(Math.ceil(t/64)*64)}function Ve(e,r){var t=r.stack,a=t.pop();exports.DEBUG&&console.log(r.step,\"ROUND[]\"),t.push(r.round(a/64)*64)}function gi(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"WCVTF[]\",t,a),e.cvt[a]=t*e.ppem/e.font.unitsPerEm}function lr(e,r){var t=r.stack,a=t.pop(),n=r.ppem,s=r.deltaBase+(e-1)*16,i=r.deltaShift;exports.DEBUG&&console.log(r.step,\"DELTAC[\"+e+\"]\",a,t);for(var u=0;u<a;u++){var o=t.pop(),l=t.pop(),f=s+((l&240)>>4);if(f===n){var h=(l&15)-8;h>=0&&h++;var p=h*i;exports.DEBUG&&console.log(r.step,\"DELTACFIX\",o,\"by\",p),r.cvt[o]+=p}}}function mi(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SROUND[]\",r),e.round=ua;var t;switch(r&192){case 0:t=.5;break;case 64:t=1;break;case 128:t=2;break;default:throw new Error(\"invalid SROUND value\")}switch(e.srPeriod=t,r&48){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error(\"invalid SROUND value\")}r&=15,r===0?e.srThreshold=0:e.srThreshold=(r/8-.5)*t}function yi(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"S45ROUND[]\",r),e.round=ua;var t;switch(r&192){case 0:t=Math.sqrt(2)/2;break;case 64:t=Math.sqrt(2);break;case 128:t=2*Math.sqrt(2);break;default:throw new Error(\"invalid S45ROUND value\")}switch(e.srPeriod=t,r&48){case 0:e.srPhase=0;break;case 16:e.srPhase=.25*t;break;case 32:e.srPhase=.5*t;break;case 48:e.srPhase=.75*t;break;default:throw new Error(\"invalid S45ROUND value\")}r&=15,r===0?e.srThreshold=0:e.srThreshold=(r/8-.5)*t}function xi(e){exports.DEBUG&&console.log(e.step,\"ROFF[]\"),e.round=es}function bi(e){exports.DEBUG&&console.log(e.step,\"RUTG[]\"),e.round=as}function Si(e){exports.DEBUG&&console.log(e.step,\"RDTG[]\"),e.round=ns}function Ti(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCANCTRL[]\",r)}function vt(e,r){var t=r.stack,a=t.pop(),n=t.pop(),s=r.z2[a],i=r.z1[n];exports.DEBUG&&console.log(r.step,\"SDPVTL[\"+e+\"]\",a,n);var u,o;e?(u=s.y-i.y,o=i.x-s.x):(u=i.x-s.x,o=i.y-s.y),r.dpv=Me(u,o)}function ki(e){var r=e.stack,t=r.pop(),a=0;exports.DEBUG&&console.log(e.step,\"GETINFO[]\",t),t&1&&(a=35),t&32&&(a|=4096),r.push(a)}function Fi(e){var r=e.stack,t=r.pop(),a=r.pop(),n=r.pop();exports.DEBUG&&console.log(e.step,\"ROLL[]\"),r.push(a),r.push(t),r.push(n)}function Ui(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MAX[]\",t,a),r.push(Math.max(a,t))}function Ci(e){var r=e.stack,t=r.pop(),a=r.pop();exports.DEBUG&&console.log(e.step,\"MIN[]\",t,a),r.push(Math.min(a,t))}function Ei(e){var r=e.stack.pop();exports.DEBUG&&console.log(e.step,\"SCANTYPE[]\",r)}function Oi(e){var r=e.stack.pop(),t=e.stack.pop();switch(exports.DEBUG&&console.log(e.step,\"INSTCTRL[]\",r,t),r){case 1:e.inhibitGridFit=!!t;return;case 2:e.ignoreCvt=!!t;return;default:throw new Error(\"invalid INSTCTRL[] selector\")}}function he(e,r){var t=r.stack,a=r.prog,n=r.ip;exports.DEBUG&&console.log(r.step,\"PUSHB[\"+e+\"]\");for(var s=0;s<e;s++)t.push(a[++n]);r.ip=n}function ce(e,r){var t=r.ip,a=r.prog,n=r.stack;exports.DEBUG&&console.log(r.ip,\"PUSHW[\"+e+\"]\");for(var s=0;s<e;s++){var i=a[++t]<<8|a[++t];i&32768&&(i=-((i^65535)+1)),n.push(i)}r.ip=t}function T(e,r,t,a,n,s){var i=s.stack,u=e&&i.pop(),o=i.pop(),l=s.rp0,f=s.z0[l],h=s.z1[o],p=s.minDis,c=s.fv,d=s.dpv,x,m,y,C;m=x=d.distance(h,f,!0,!0),y=m>=0?1:-1,m=Math.abs(m),e&&(C=s.cvt[u],a&&Math.abs(m-C)<s.cvCutIn&&(m=C)),t&&m<p&&(m=p),a&&(m=s.round(m)),c.setRelative(h,f,y*m,d),c.touch(h),exports.DEBUG&&console.log(s.step,(e?\"MIRP[\":\"MDRP[\")+(r?\"M\":\"m\")+(t?\">\":\"_\")+(a?\"R\":\"_\")+(n===0?\"Gr\":n===1?\"Bl\":n===2?\"Wh\":\"\")+\"]\",e?u+\"(\"+s.cvt[u]+\",\"+C+\")\":\"\",o,\"(d =\",x,\"->\",y*m,\")\"),s.rp1=s.rp0,s.rp2=o,r&&(s.rp0=o)}na=[et.bind(void 0,le),et.bind(void 0,oe),rt.bind(void 0,le),rt.bind(void 0,oe),tt.bind(void 0,le),tt.bind(void 0,oe),at.bind(void 0,0),at.bind(void 0,1),nt.bind(void 0,0),nt.bind(void 0,1),is,os,us,ls,fs,ps,hs,cs,vs,ds,gs,ms,ys,xs,bs,Ss,Ts,ks,Fs,Us,void 0,void 0,Cs,or,Es,Os,Ls,Ds,As,void 0,void 0,void 0,Rs,ws,Bs,void 0,st.bind(void 0,0),st.bind(void 0,1),it.bind(void 0,le),it.bind(void 0,oe),ot.bind(void 0,0),ot.bind(void 0,1),ut.bind(void 0,0),ut.bind(void 0,1),lt.bind(void 0,0),lt.bind(void 0,1),Is,Ms,ft.bind(void 0,0),ft.bind(void 0,1),Ps,Gs,pt.bind(void 0,0),pt.bind(void 0,1),Ns,Hs,zs,Ws,_s,Vs,ht.bind(void 0,0),ht.bind(void 0,1),void 0,ct.bind(void 0,0),ct.bind(void 0,1),qs,void 0,Xs,void 0,void 0,Ys,Zs,Qs,Ks,Js,js,$s,ei,ri,ti,ai,ni,si,ur.bind(void 0,1),ii,oi,ui,li,fi,pi,hi,ci,vi,di,Ve.bind(void 0,0),Ve.bind(void 0,1),Ve.bind(void 0,2),Ve.bind(void 0,3),void 0,void 0,void 0,void 0,gi,ur.bind(void 0,2),ur.bind(void 0,3),lr.bind(void 0,1),lr.bind(void 0,2),lr.bind(void 0,3),mi,yi,void 0,void 0,xi,void 0,bi,Si,or,or,void 0,void 0,void 0,void 0,void 0,Ti,vt.bind(void 0,0),vt.bind(void 0,1),ki,void 0,Fi,Ui,Ci,Ei,Oi,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,void 0,he.bind(void 0,1),he.bind(void 0,2),he.bind(void 0,3),he.bind(void 0,4),he.bind(void 0,5),he.bind(void 0,6),he.bind(void 0,7),he.bind(void 0,8),ce.bind(void 0,1),ce.bind(void 0,2),ce.bind(void 0,3),ce.bind(void 0,4),ce.bind(void 0,5),ce.bind(void 0,6),ce.bind(void 0,7),ce.bind(void 0,8),T.bind(void 0,0,0,0,0,0),T.bind(void 0,0,0,0,0,1),T.bind(void 0,0,0,0,0,2),T.bind(void 0,0,0,0,0,3),T.bind(void 0,0,0,0,1,0),T.bind(void 0,0,0,0,1,1),T.bind(void 0,0,0,0,1,2),T.bind(void 0,0,0,0,1,3),T.bind(void 0,0,0,1,0,0),T.bind(void 0,0,0,1,0,1),T.bind(void 0,0,0,1,0,2),T.bind(void 0,0,0,1,0,3),T.bind(void 0,0,0,1,1,0),T.bind(void 0,0,0,1,1,1),T.bind(void 0,0,0,1,1,2),T.bind(void 0,0,0,1,1,3),T.bind(void 0,0,1,0,0,0),T.bind(void 0,0,1,0,0,1),T.bind(void 0,0,1,0,0,2),T.bind(void 0,0,1,0,0,3),T.bind(void 0,0,1,0,1,0),T.bind(void 0,0,1,0,1,1),T.bind(void 0,0,1,0,1,2),T.bind(void 0,0,1,0,1,3),T.bind(void 0,0,1,1,0,0),T.bind(void 0,0,1,1,0,1),T.bind(void 0,0,1,1,0,2),T.bind(void 0,0,1,1,0,3),T.bind(void 0,0,1,1,1,0),T.bind(void 0,0,1,1,1,1),T.bind(void 0,0,1,1,1,2),T.bind(void 0,0,1,1,1,3),T.bind(void 0,1,0,0,0,0),T.bind(void 0,1,0,0,0,1),T.bind(void 0,1,0,0,0,2),T.bind(void 0,1,0,0,0,3),T.bind(void 0,1,0,0,1,0),T.bind(void 0,1,0,0,1,1),T.bind(void 0,1,0,0,1,2),T.bind(void 0,1,0,0,1,3),T.bind(void 0,1,0,1,0,0),T.bind(void 0,1,0,1,0,1),T.bind(void 0,1,0,1,0,2),T.bind(void 0,1,0,1,0,3),T.bind(void 0,1,0,1,1,0),T.bind(void 0,1,0,1,1,1),T.bind(void 0,1,0,1,1,2),T.bind(void 0,1,0,1,1,3),T.bind(void 0,1,1,0,0,0),T.bind(void 0,1,1,0,0,1),T.bind(void 0,1,1,0,0,2),T.bind(void 0,1,1,0,0,3),T.bind(void 0,1,1,0,1,0),T.bind(void 0,1,1,0,1,1),T.bind(void 0,1,1,0,1,2),T.bind(void 0,1,1,0,1,3),T.bind(void 0,1,1,1,0,0),T.bind(void 0,1,1,1,0,1),T.bind(void 0,1,1,1,0,2),T.bind(void 0,1,1,1,0,3),T.bind(void 0,1,1,1,1,0),T.bind(void 0,1,1,1,1,1),T.bind(void 0,1,1,1,1,2),T.bind(void 0,1,1,1,1,3)];function Ce(e){this.char=e,this.state={},this.activeState=null}function Lr(e,r,t){this.contextName=t,this.startIndex=e,this.endOffset=r}function Li(e,r,t){this.contextName=e,this.openRange=null,this.ranges=[],this.checkStart=r,this.checkEnd=t}function re(e,r){this.context=e,this.index=r,this.length=e.length,this.current=e[r],this.backtrack=e.slice(0,r),this.lookahead=e.slice(r+1)}function er(e){this.eventId=e,this.subscribers=[]}function Ri(e){var r=this,t=[\"start\",\"end\",\"next\",\"newToken\",\"contextStart\",\"contextEnd\",\"insertToken\",\"removeToken\",\"removeRange\",\"replaceToken\",\"replaceRange\",\"composeRUD\",\"updateContextsRanges\"];t.forEach(function(n){Object.defineProperty(r.events,n,{value:new er(n)})}),e&&t.forEach(function(n){var s=e[n];typeof s==\"function\"&&r.events[n].subscribe(s)});var a=[\"insertToken\",\"removeToken\",\"removeRange\",\"replaceToken\",\"replaceRange\",\"composeRUD\"];a.forEach(function(n){r.events[n].subscribe(r.updateContextsRanges)})}function B(e){this.tokens=[],this.registeredContexts={},this.contextCheckers=[],this.events={},this.registeredModifiers=[],Ri.call(this,e)}Ce.prototype.setState=function(e,r){return this.state[e]=r,this.activeState={key:e,value:this.state[e]},this.activeState};Ce.prototype.getState=function(e){return this.state[e]||null};B.prototype.inboundIndex=function(e){return e>=0&&e<this.tokens.length};B.prototype.composeRUD=function(e){var r=this,t=!0,a=e.map(function(s){return r[s[0]].apply(r,s.slice(1).concat(t))}),n=function(s){return typeof s==\"object\"&&s.hasOwnProperty(\"FAIL\")};if(a.every(n))return{FAIL:\"composeRUD: one or more operations hasn't completed successfully\",report:a.filter(n)};this.dispatch(\"composeRUD\",[a.filter(function(s){return!n(s)})])};B.prototype.replaceRange=function(e,r,t,a){r=r!==null?r:this.tokens.length;var n=t.every(function(i){return i instanceof Ce});if(!isNaN(e)&&this.inboundIndex(e)&&n){var s=this.tokens.splice.apply(this.tokens,[e,r].concat(t));return a||this.dispatch(\"replaceToken\",[e,r,t]),[s,t]}else return{FAIL:\"replaceRange: invalid tokens or startIndex.\"}};B.prototype.replaceToken=function(e,r,t){if(!isNaN(e)&&this.inboundIndex(e)&&r instanceof Ce){var a=this.tokens.splice(e,1,r);return t||this.dispatch(\"replaceToken\",[e,r]),[a[0],r]}else return{FAIL:\"replaceToken: invalid token or index.\"}};B.prototype.removeRange=function(e,r,t){r=isNaN(r)?this.tokens.length:r;var a=this.tokens.splice(e,r);return t||this.dispatch(\"removeRange\",[a,e,r]),a};B.prototype.removeToken=function(e,r){if(!isNaN(e)&&this.inboundIndex(e)){var t=this.tokens.splice(e,1);return r||this.dispatch(\"removeToken\",[t,e]),t}else return{FAIL:\"removeToken: invalid token index.\"}};B.prototype.insertToken=function(e,r,t){var a=e.every(function(n){return n instanceof Ce});return a?(this.tokens.splice.apply(this.tokens,[r,0].concat(e)),t||this.dispatch(\"insertToken\",[e,r]),e):{FAIL:\"insertToken: invalid token(s).\"}};B.prototype.registerModifier=function(e,r,t){this.events.newToken.subscribe(function(a,n){var s=[a,n],i=r===null||r.apply(this,s)===!0,u=[a,n];if(i){var o=t.apply(this,u);a.setState(e,o)}}),this.registeredModifiers.push(e)};er.prototype.subscribe=function(e){return typeof e==\"function\"?this.subscribers.push(e)-1:{FAIL:\"invalid '\"+this.eventId+\"' event handler\"}};er.prototype.unsubscribe=function(e){this.subscribers.splice(e,1)};re.prototype.setCurrentIndex=function(e){this.index=e,this.current=this.context[e],this.backtrack=this.context.slice(0,e),this.lookahead=this.context.slice(e+1)};re.prototype.get=function(e){switch(!0){case e===0:return this.current;case(e<0&&Math.abs(e)<=this.backtrack.length):return this.backtrack.slice(e)[0];case(e>0&&e<=this.lookahead.length):return this.lookahead[e-1];default:return null}};B.prototype.rangeToText=function(e){if(e instanceof Lr)return this.getRangeTokens(e).map(function(r){return r.char}).join(\"\")};B.prototype.getText=function(){return this.tokens.map(function(e){return e.char}).join(\"\")};B.prototype.getContext=function(e){var r=this.registeredContexts[e];return r||null};B.prototype.on=function(e,r){var t=this.events[e];return t?t.subscribe(r):null};B.prototype.dispatch=function(e,r){var t=this,a=this.events[e];a instanceof er&&a.subscribers.forEach(function(n){n.apply(t,r||[])})};B.prototype.registerContextChecker=function(e,r,t){if(this.getContext(e))return{FAIL:\"context name '\"+e+\"' is already registered.\"};if(typeof r!=\"function\")return{FAIL:\"missing context start check.\"};if(typeof t!=\"function\")return{FAIL:\"missing context end check.\"};var a=new Li(e,r,t);return this.registeredContexts[e]=a,this.contextCheckers.push(a),a};B.prototype.getRangeTokens=function(e){var r=e.startIndex+e.endOffset;return[].concat(this.tokens.slice(e.startIndex,r))};B.prototype.getContextRanges=function(e){var r=this.getContext(e);return r?r.ranges:{FAIL:\"context checker '\"+e+\"' is not registered.\"}};B.prototype.resetContextsRanges=function(){var e=this.registeredContexts;for(var r in e)if(e.hasOwnProperty(r)){var t=e[r];t.ranges=[]}};B.prototype.updateContextsRanges=function(){this.resetContextsRanges();for(var e=this.tokens.map(function(a){return a.char}),r=0;r<e.length;r++){var t=new re(e,r);this.runContextCheck(t)}this.dispatch(\"updateContextsRanges\",[this.registeredContexts])};B.prototype.setEndOffset=function(e,r){var t=this.getContext(r).openRange.startIndex,a=new Lr(t,e,r),n=this.getContext(r).ranges;return a.rangeId=r+\".\"+n.length,n.push(a),this.getContext(r).openRange=null,a};B.prototype.runContextCheck=function(e){var r=this,t=e.index;this.contextCheckers.forEach(function(a){var n=a.contextName,s=r.getContext(n).openRange;if(!s&&a.checkStart(e)&&(s=new Lr(t,null,n),r.getContext(n).openRange=s,r.dispatch(\"contextStart\",[n,t])),!!s&&a.checkEnd(e)){var i=t-s.startIndex+1,u=r.setEndOffset(i,n);r.dispatch(\"contextEnd\",[n,u])}})};B.prototype.tokenize=function(e){this.tokens=[],this.resetContextsRanges();var r=Array.from(e);this.dispatch(\"start\");for(var t=0;t<r.length;t++){var a=r[t],n=new re(r,t);this.dispatch(\"next\",[n]),this.runContextCheck(n);var s=new Ce(a);this.tokens.push(s),this.dispatch(\"newToken\",[s,n])}return this.dispatch(\"end\",[this.tokens]),this.tokens};function ge(e){return/[\\u0600-\\u065F\\u066A-\\u06D2\\u06FA-\\u06FF]/.test(e)}function fa(e){return/[\\u0630\\u0690\\u0621\\u0631\\u0661\\u0671\\u0622\\u0632\\u0672\\u0692\\u06C2\\u0623\\u0673\\u0693\\u06C3\\u0624\\u0694\\u06C4\\u0625\\u0675\\u0695\\u06C5\\u06E5\\u0676\\u0696\\u06C6\\u0627\\u0677\\u0697\\u06C7\\u0648\\u0688\\u0698\\u06C8\\u0689\\u0699\\u06C9\\u068A\\u06CA\\u066B\\u068B\\u06CB\\u068C\\u068D\\u06CD\\u06FD\\u068E\\u06EE\\u06FE\\u062F\\u068F\\u06CF\\u06EF]/.test(e)}function me(e){return/[\\u0600-\\u0605\\u060C-\\u060E\\u0610-\\u061B\\u061E\\u064B-\\u065F\\u0670\\u06D6-\\u06DC\\u06DF-\\u06E4\\u06E7\\u06E8\\u06EA-\\u06ED]/.test(e)}function Xe(e){return/[A-z]/.test(e)}function wi(e){return/\\s/.test(e)}function J(e){this.font=e,this.features={}}function be(e){this.id=e.id,this.tag=e.tag,this.substitution=e.substitution}function Pe(e,r){if(!e)return-1;switch(r.format){case 1:return r.glyphs.indexOf(e);case 2:for(var t=r.ranges,a=0;a<t.length;a++){var n=t[a];if(e>=n.start&&e<=n.end){var s=e-n.start;return n.index+s}}break;default:return-1}return-1}function Di(e,r){var t=Pe(e,r.coverage);return t===-1?null:e+r.deltaGlyphId}function Ai(e,r){var t=Pe(e,r.coverage);return t===-1?null:r.substitute[t]}function fr(e,r){for(var t=[],a=0;a<e.length;a++){var n=e[a],s=r.current;s=Array.isArray(s)?s[0]:s;var i=Pe(s,n);i!==-1&&t.push(i)}return t.length!==e.length?-1:t}function Bi(e,r){var t=r.inputCoverage.length+r.lookaheadCoverage.length+r.backtrackCoverage.length;if(e.context.length<t)return[];var a=fr(r.inputCoverage,e);if(a===-1)return[];var n=r.inputCoverage.length-1;if(e.lookahead.length<r.lookaheadCoverage.length)return[];for(var s=e.lookahead.slice(n);s.length&&me(s[0].char);)s.shift();var i=new re(s,0),u=fr(r.lookaheadCoverage,i),o=[].concat(e.backtrack);for(o.reverse();o.length&&me(o[0].char);)o.shift();if(o.length<r.backtrackCoverage.length)return[];var l=new re(o,0),f=fr(r.backtrackCoverage,l),h=a.length===r.inputCoverage.length&&u.length===r.lookaheadCoverage.length&&f.length===r.backtrackCoverage.length,p=[];if(h)for(var c=0;c<r.lookupRecords.length;c++)for(var d=r.lookupRecords[c],x=d.lookupListIndex,m=this.getLookupByIndex(x),y=0;y<m.subtables.length;y++){var C=m.subtables[y],S=this.getLookupMethod(m,C),R=this.getSubstitutionType(m,C);if(R===\"12\")for(var O=0;O<a.length;O++){var D=e.get(O),L=S(D);L&&p.push(L)}}return p}function Ii(e,r){var t=e.current,a=Pe(t,r.coverage);if(a===-1)return null;for(var n,s=r.ligatureSets[a],i=0;i<s.length;i++){n=s[i];for(var u=0;u<n.components.length;u++){var o=e.lookahead[u],l=n.components[u];if(o!==l)break;if(u===n.components.length-1)return n}}return null}function Mi(e,r){var t=Pe(e,r.coverage);return t===-1?null:r.sequences[t]}J.prototype.getDefaultScriptFeaturesIndexes=function(){for(var e=this.font.tables.gsub.scripts,r=0;r<e.length;r++){var t=e[r];if(t.tag===\"DFLT\")return t.script.defaultLangSys.featureIndexes}return[]};J.prototype.getScriptFeaturesIndexes=function(e){var r=this.font.tables;if(!r.gsub)return[];if(!e)return this.getDefaultScriptFeaturesIndexes();for(var t=this.font.tables.gsub.scripts,a=0;a<t.length;a++){var n=t[a];if(n.tag===e&&n.script.defaultLangSys)return n.script.defaultLangSys.featureIndexes;var s=n.langSysRecords;if(s)for(var i=0;i<s.length;i++){var u=s[i];if(u.tag===e){var o=u.langSys;return o.featureIndexes}}}return this.getDefaultScriptFeaturesIndexes()};J.prototype.mapTagsToFeatures=function(e,r){for(var t={},a=0;a<e.length;a++){var n=e[a].tag,s=e[a].feature;t[n]=s}this.features[r].tags=t};J.prototype.getScriptFeatures=function(e){var r=this.features[e];if(this.features.hasOwnProperty(e))return r;var t=this.getScriptFeaturesIndexes(e);if(!t)return null;var a=this.font.tables.gsub;return r=t.map(function(n){return a.features[n]}),this.features[e]=r,this.mapTagsToFeatures(r,e),r};J.prototype.getSubstitutionType=function(e,r){var t=e.lookupType.toString(),a=r.substFormat.toString();return t+a};J.prototype.getLookupMethod=function(e,r){var t=this,a=this.getSubstitutionType(e,r);switch(a){case\"11\":return function(n){return Di.apply(t,[n,r])};case\"12\":return function(n){return Ai.apply(t,[n,r])};case\"63\":return function(n){return Bi.apply(t,[n,r])};case\"41\":return function(n){return Ii.apply(t,[n,r])};case\"21\":return function(n){return Mi.apply(t,[n,r])};default:throw new Error(\"lookupType: \"+e.lookupType+\" - substFormat: \"+r.substFormat+\" is not yet supported\")}};J.prototype.lookupFeature=function(e){var r=e.contextParams,t=r.index,a=this.getFeature({tag:e.tag,script:e.script});if(!a)return new Error(\"font '\"+this.font.names.fullName.en+\"' doesn't support feature '\"+e.tag+\"' for script '\"+e.script+\"'.\");for(var n=this.getFeatureLookups(a),s=[].concat(r.context),i=0;i<n.length;i++)for(var u=n[i],o=this.getLookupSubtables(u),l=0;l<o.length;l++){var f=o[l],h=this.getSubstitutionType(u,f),p=this.getLookupMethod(u,f),c=void 0;switch(h){case\"11\":c=p(r.current),c&&s.splice(t,1,new be({id:11,tag:e.tag,substitution:c}));break;case\"12\":c=p(r.current),c&&s.splice(t,1,new be({id:12,tag:e.tag,substitution:c}));break;case\"63\":c=p(r),Array.isArray(c)&&c.length&&s.splice(t,1,new be({id:63,tag:e.tag,substitution:c}));break;case\"41\":c=p(r),c&&s.splice(t,1,new be({id:41,tag:e.tag,substitution:c}));break;case\"21\":c=p(r.current),c&&s.splice(t,1,new be({id:21,tag:e.tag,substitution:c}));break}r=new re(s,t),!(Array.isArray(c)&&!c.length)&&(c=null)}return s.length?s:null};J.prototype.supports=function(e){if(!e.script)return!1;this.getScriptFeatures(e.script);var r=this.features.hasOwnProperty(e.script);if(!e.tag)return r;var t=this.features[e.script].some(function(a){return a.tag===e.tag});return r&&t};J.prototype.getLookupSubtables=function(e){return e.subtables||null};J.prototype.getLookupByIndex=function(e){var r=this.font.tables.gsub.lookups;return r[e]||null};J.prototype.getFeatureLookups=function(e){return e.lookupListIndexes.map(this.getLookupByIndex.bind(this))};J.prototype.getFeature=function(r){if(!this.font)return{FAIL:\"No font was found\"};this.features.hasOwnProperty(r.script)||this.getScriptFeatures(r.script);var t=this.features[r.script];return t?t.tags[r.tag]?this.features[r.script].tags[r.tag]:null:{FAIL:\"No feature for script \"+r.script}};function Pi(e){var r=e.current,t=e.get(-1);return t===null&&ge(r)||!ge(t)&&ge(r)}function Gi(e){var r=e.get(1);return r===null||!ge(r)}var Ni={startCheck:Pi,endCheck:Gi};function Hi(e){var r=e.current,t=e.get(-1);return(ge(r)||me(r))&&!ge(t)}function zi(e){var r=e.get(1);switch(!0){case r===null:return!0;case(!ge(r)&&!me(r)):var t=wi(r);if(!t)return!0;if(t){var a=!1;if(a=e.lookahead.some(function(n){return ge(n)||me(n)}),!a)return!0}break;default:return!1}}var Wi={startCheck:Hi,endCheck:zi};function _i(e,r,t){r[t].setState(e.tag,e.substitution)}function Vi(e,r,t){r[t].setState(e.tag,e.substitution)}function qi(e,r,t){e.substitution.forEach(function(a,n){var s=r[t+n];s.setState(e.tag,a)})}function Xi(e,r,t){var a=r[t];a.setState(e.tag,e.substitution.ligGlyph);for(var n=e.substitution.components.length,s=0;s<n;s++)a=r[t+s+1],a.setState(\"deleted\",!0)}var dt={11:_i,12:Vi,63:qi,41:Xi};function Rr(e,r,t){e instanceof be&&dt[e.id]&&dt[e.id](e,r,t)}function Yi(e){for(var r=[].concat(e.backtrack),t=r.length-1;t>=0;t--){var a=r[t],n=fa(a),s=me(a);if(!n&&!s)return!0;if(n)return!1}return!1}function Zi(e){if(fa(e.current))return!1;for(var r=0;r<e.lookahead.length;r++){var t=e.lookahead[r],a=me(t);if(!a)return!0}return!1}function Qi(e){var r=this,t=\"arab\",a=this.featuresTags[t],n=this.tokenizer.getRangeTokens(e);if(n.length!==1){var s=new re(n.map(function(u){return u.getState(\"glyphIndex\")}),0),i=new re(n.map(function(u){return u.char}),0);n.forEach(function(u,o){if(!me(u.char)){s.setCurrentIndex(o),i.setCurrentIndex(o);var l=0;Yi(i)&&(l|=1),Zi(i)&&(l|=2);var f;switch(l){case 1:f=\"fina\";break;case 2:f=\"init\";break;case 3:f=\"medi\";break}if(a.indexOf(f)!==-1){var h=r.query.lookupFeature({tag:f,script:t,contextParams:s});if(h instanceof Error)return console.info(h.message);h.forEach(function(p,c){p instanceof be&&(Rr(p,n,c),s.context[c]=p.substitution)})}}})}}function gt(e,r){var t=e.map(function(a){return a.activeState.value});return new re(t,r||0)}function Ki(e){var r=this,t=\"arab\",a=this.tokenizer.getRangeTokens(e),n=gt(a);n.context.forEach(function(s,i){n.setCurrentIndex(i);var u=r.query.lookupFeature({tag:\"rlig\",script:t,contextParams:n});u.length&&(u.forEach(function(o){return Rr(o,a,i)}),n=gt(a))})}function Ji(e){var r=e.current,t=e.get(-1);return t===null&&Xe(r)||!Xe(t)&&Xe(r)}function ji(e){var r=e.get(1);return r===null||!Xe(r)}var $i={startCheck:Ji,endCheck:ji};function mt(e,r){var t=e.map(function(a){return a.activeState.value});return new re(t,r||0)}function eo(e){var r=this,t=\"latn\",a=this.tokenizer.getRangeTokens(e),n=mt(a);n.context.forEach(function(s,i){n.setCurrentIndex(i);var u=r.query.lookupFeature({tag:\"liga\",script:t,contextParams:n});u.length&&(u.forEach(function(o){return Rr(o,a,i)}),n=mt(a))})}function ne(e){this.baseDir=e||\"ltr\",this.tokenizer=new B,this.featuresTags={}}ne.prototype.setText=function(e){this.text=e};ne.prototype.contextChecks={latinWordCheck:$i,arabicWordCheck:Ni,arabicSentenceCheck:Wi};function pr(e){var r=this.contextChecks[e+\"Check\"];return this.tokenizer.registerContextChecker(e,r.startCheck,r.endCheck)}function ro(){return pr.call(this,\"latinWord\"),pr.call(this,\"arabicWord\"),pr.call(this,\"arabicSentence\"),this.tokenizer.tokenize(this.text)}function to(){var e=this,r=this.tokenizer.getContextRanges(\"arabicSentence\");r.forEach(function(t){var a=e.tokenizer.getRangeTokens(t);e.tokenizer.replaceRange(t.startIndex,t.endOffset,a.reverse())})}ne.prototype.registerFeatures=function(e,r){var t=this,a=r.filter(function(n){return t.query.supports({script:e,tag:n})});this.featuresTags.hasOwnProperty(e)?this.featuresTags[e]=this.featuresTags[e].concat(a):this.featuresTags[e]=a};ne.prototype.applyFeatures=function(e,r){if(!e)throw new Error(\"No valid font was provided to apply features\");this.query||(this.query=new J(e));for(var t=0;t<r.length;t++){var a=r[t];!this.query.supports({script:a.script})||this.registerFeatures(a.script,a.tags)}};ne.prototype.registerModifier=function(e,r,t){this.tokenizer.registerModifier(e,r,t)};function wr(){if(this.tokenizer.registeredModifiers.indexOf(\"glyphIndex\")===-1)throw new Error(\"glyphIndex modifier is required to apply arabic presentation features.\")}function ao(){var e=this,r=\"arab\";if(!!this.featuresTags.hasOwnProperty(r)){wr.call(this);var t=this.tokenizer.getContextRanges(\"arabicWord\");t.forEach(function(a){Qi.call(e,a)})}}function no(){var e=this,r=\"arab\";if(!!this.featuresTags.hasOwnProperty(r)){var t=this.featuresTags[r];if(t.indexOf(\"rlig\")!==-1){wr.call(this);var a=this.tokenizer.getContextRanges(\"arabicWord\");a.forEach(function(n){Ki.call(e,n)})}}}function so(){var e=this,r=\"latn\";if(!!this.featuresTags.hasOwnProperty(r)){var t=this.featuresTags[r];if(t.indexOf(\"liga\")!==-1){wr.call(this);var a=this.tokenizer.getContextRanges(\"latinWord\");a.forEach(function(n){eo.call(e,n)})}}}ne.prototype.checkContextReady=function(e){return!!this.tokenizer.getContext(e)};ne.prototype.applyFeaturesToContexts=function(){this.checkContextReady(\"arabicWord\")&&(ao.call(this),no.call(this)),this.checkContextReady(\"latinWord\")&&so.call(this),this.checkContextReady(\"arabicSentence\")&&to.call(this)};ne.prototype.processText=function(e){(!this.text||this.text!==e)&&(this.setText(e),ro.call(this),this.applyFeaturesToContexts())};ne.prototype.getBidiText=function(e){return this.processText(e),this.tokenizer.getText()};ne.prototype.getTextGlyphs=function(e){this.processText(e);for(var r=[],t=0;t<this.tokenizer.tokens.length;t++){var a=this.tokenizer.tokens[t];if(!a.state.deleted){var n=a.activeState.value;r.push(Array.isArray(n)?n[0]:n)}}return r};function w(e){e=e||{},e.tables=e.tables||{},e.empty||(Ee(e.familyName,\"When creating a new Font object, familyName is required.\"),Ee(e.styleName,\"When creating a new Font object, styleName is required.\"),Ee(e.unitsPerEm,\"When creating a new Font object, unitsPerEm is required.\"),Ee(e.ascender,\"When creating a new Font object, ascender is required.\"),Ee(e.descender<=0,\"When creating a new Font object, negative descender value is required.\"),this.names={fontFamily:{en:e.familyName||\" \"},fontSubfamily:{en:e.styleName||\" \"},fullName:{en:e.fullName||e.familyName+\" \"+e.styleName},postScriptName:{en:e.postScriptName||(e.familyName+e.styleName).replace(/\\s/g,\"\")},designer:{en:e.designer||\" \"},designerURL:{en:e.designerURL||\" \"},manufacturer:{en:e.manufacturer||\" \"},manufacturerURL:{en:e.manufacturerURL||\" \"},license:{en:e.license||\" \"},licenseURL:{en:e.licenseURL||\" \"},version:{en:e.version||\"Version 0.1\"},description:{en:e.description||\" \"},copyright:{en:e.copyright||\" \"},trademark:{en:e.trademark||\" \"}},this.unitsPerEm=e.unitsPerEm||1e3,this.ascender=e.ascender,this.descender=e.descender,this.createdTimestamp=e.createdTimestamp,this.tables=Object.assign(e.tables,{os2:Object.assign({usWeightClass:e.weightClass||this.usWeightClasses.MEDIUM,usWidthClass:e.widthClass||this.usWidthClasses.MEDIUM,fsSelection:e.fsSelection||this.fsSelectionValues.REGULAR},e.tables.os2)})),this.supported=!0,this.glyphs=new ue.GlyphSet(this,e.glyphs||[]),this.encoding=new Ot(this),this.position=new Be(this),this.substitution=new K(this),this.tables=this.tables||{},this._push=null,this._hmtxTableData={},Object.defineProperty(this,\"hinting\",{get:function(){if(this._hinting)return this._hinting;if(this.outlinesFormat===\"truetype\")return this._hinting=new ia(this)}})}w.prototype.hasChar=function(e){return this.encoding.charToGlyphIndex(e)!==null};w.prototype.charToGlyphIndex=function(e){return this.encoding.charToGlyphIndex(e)};w.prototype.charToGlyph=function(e){var r=this.charToGlyphIndex(e),t=this.glyphs.get(r);return t||(t=this.glyphs.get(0)),t};w.prototype.updateFeatures=function(e){return this.defaultRenderOptions.features.map(function(r){return r.script===\"latn\"?{script:\"latn\",tags:r.tags.filter(function(t){return e[t]})}:r})};w.prototype.stringToGlyphs=function(e,r){var t=this,a=new ne,n=function(h){return t.charToGlyphIndex(h.char)};a.registerModifier(\"glyphIndex\",null,n);var s=r?this.updateFeatures(r.features):this.defaultRenderOptions.features;a.applyFeatures(this,s);for(var i=a.getTextGlyphs(e),u=i.length,o=new Array(u),l=this.glyphs.get(0),f=0;f<u;f+=1)o[f]=this.glyphs.get(i[f])||l;return o};w.prototype.nameToGlyphIndex=function(e){return this.glyphNames.nameToGlyphIndex(e)};w.prototype.nameToGlyph=function(e){var r=this.nameToGlyphIndex(e),t=this.glyphs.get(r);return t||(t=this.glyphs.get(0)),t};w.prototype.glyphIndexToName=function(e){return this.glyphNames.glyphIndexToName?this.glyphNames.glyphIndexToName(e):\"\"};w.prototype.getKerningValue=function(e,r){e=e.index||e,r=r.index||r;var t=this.position.defaultKerningTables;return t?this.position.getKerningValue(t,e,r):this.kerningPairs[e+\",\"+r]||0};w.prototype.defaultRenderOptions={kerning:!0,features:[{script:\"arab\",tags:[\"init\",\"medi\",\"fina\",\"rlig\"]},{script:\"latn\",tags:[\"liga\",\"rlig\"]}]};w.prototype.forEachGlyph=function(e,r,t,a,n,s){r=r!==void 0?r:0,t=t!==void 0?t:0,a=a!==void 0?a:72,n=Object.assign({},this.defaultRenderOptions,n);var i=1/this.unitsPerEm*a,u=this.stringToGlyphs(e,n),o;if(n.kerning){var l=n.script||this.position.getDefaultScriptName();o=this.position.getKerningTables(l,n.language)}for(var f=0;f<u.length;f+=1){var h=u[f];if(s.call(this,h,r,t,a,n),h.advanceWidth&&(r+=h.advanceWidth*i),n.kerning&&f<u.length-1){var p=o?this.position.getKerningValue(o,h.index,u[f+1].index):this.getKerningValue(h,u[f+1]);r+=p*i}n.letterSpacing?r+=n.letterSpacing*a:n.tracking&&(r+=n.tracking/1e3*a)}return r};w.prototype.getPath=function(e,r,t,a,n){var s=new P;return this.forEachGlyph(e,r,t,a,n,function(i,u,o,l){var f=i.getPath(u,o,l,n,this);s.extend(f)}),s};w.prototype.getPaths=function(e,r,t,a,n){var s=[];return this.forEachGlyph(e,r,t,a,n,function(i,u,o,l){var f=i.getPath(u,o,l,n,this);s.push(f)}),s};w.prototype.getAdvanceWidth=function(e,r,t){return this.forEachGlyph(e,0,0,r,t,function(){})};w.prototype.draw=function(e,r,t,a,n,s){this.getPath(r,t,a,n,s).draw(e)};w.prototype.drawPoints=function(e,r,t,a,n,s){this.forEachGlyph(r,t,a,n,s,function(i,u,o,l){i.drawPoints(e,u,o,l)})};w.prototype.drawMetrics=function(e,r,t,a,n,s){this.forEachGlyph(r,t,a,n,s,function(i,u,o,l){i.drawMetrics(e,u,o,l)})};w.prototype.getEnglishName=function(e){var r=this.names[e];if(r)return r.en};w.prototype.validate=function(){var e=this;function r(a,n){}function t(a){var n=e.getEnglishName(a);n&&n.trim().length>0}t(\"fontFamily\"),t(\"weightName\"),t(\"manufacturer\"),t(\"copyright\"),t(\"version\"),this.unitsPerEm>0};w.prototype.toTables=function(){return Xn.fontToTable(this)};w.prototype.toBuffer=function(){return console.warn(\"Font.toBuffer is deprecated. Use Font.toArrayBuffer instead.\"),this.toArrayBuffer()};w.prototype.toArrayBuffer=function(){for(var e=this.toTables(),r=e.encode(),t=new ArrayBuffer(r.length),a=new Uint8Array(t),n=0;n<r.length;n++)a[n]=r[n];return t};w.prototype.download=function(e){var r=this.getEnglishName(\"fontFamily\"),t=this.getEnglishName(\"fontSubfamily\");e=e||r.replace(/\\s/g,\"\")+\"-\"+t+\".otf\";var a=this.toArrayBuffer();if(Zn())if(window.URL=window.URL||window.webkitURL,window.URL){var n=new DataView(a),s=new Blob([n],{type:\"font/opentype\"}),i=document.createElement(\"a\");i.href=window.URL.createObjectURL(s),i.download=e;var u=document.createEvent(\"MouseEvents\");u.initEvent(\"click\",!0,!1),i.dispatchEvent(u)}else console.warn(\"Font file could not be downloaded. Try using a different browser.\");else{var o=Ne(),l=Qn(a);o.writeFileSync(e,l)}};w.prototype.fsSelectionValues={ITALIC:1,UNDERSCORE:2,NEGATIVE:4,OUTLINED:8,STRIKEOUT:16,BOLD:32,REGULAR:64,USER_TYPO_METRICS:128,WWS:256,OBLIQUE:512};w.prototype.usWidthClasses={ULTRA_CONDENSED:1,EXTRA_CONDENSED:2,CONDENSED:3,SEMI_CONDENSED:4,MEDIUM:5,SEMI_EXPANDED:6,EXPANDED:7,EXTRA_EXPANDED:8,ULTRA_EXPANDED:9};w.prototype.usWeightClasses={THIN:100,EXTRA_LIGHT:200,LIGHT:300,NORMAL:400,MEDIUM:500,SEMI_BOLD:600,BOLD:700,EXTRA_BOLD:800,BLACK:900};function pa(e,r){var t=JSON.stringify(e),a=256;for(var n in r){var s=parseInt(n);if(!(!s||s<256)){if(JSON.stringify(r[n])===t)return s;a<=s&&(a=s+1)}}return r[a]=e,a}function io(e,r,t){var a=pa(r.name,t);return[{name:\"tag_\"+e,type:\"TAG\",value:r.tag},{name:\"minValue_\"+e,type:\"FIXED\",value:r.minValue<<16},{name:\"defaultValue_\"+e,type:\"FIXED\",value:r.defaultValue<<16},{name:\"maxValue_\"+e,type:\"FIXED\",value:r.maxValue<<16},{name:\"flags_\"+e,type:\"USHORT\",value:0},{name:\"nameID_\"+e,type:\"USHORT\",value:a}]}function oo(e,r,t){var a={},n=new k.Parser(e,r);return a.tag=n.parseTag(),a.minValue=n.parseFixed(),a.defaultValue=n.parseFixed(),a.maxValue=n.parseFixed(),n.skip(\"uShort\",1),a.name=t[n.parseUShort()]||{},a}function uo(e,r,t,a){for(var n=pa(r.name,a),s=[{name:\"nameID_\"+e,type:\"USHORT\",value:n},{name:\"flags_\"+e,type:\"USHORT\",value:0}],i=0;i<t.length;++i){var u=t[i].tag;s.push({name:\"axis_\"+e+\" \"+u,type:\"FIXED\",value:r.coordinates[u]<<16})}return s}function lo(e,r,t,a){var n={},s=new k.Parser(e,r);n.name=a[s.parseUShort()]||{},s.skip(\"uShort\",1),n.coordinates={};for(var i=0;i<t.length;++i)n.coordinates[t[i].tag]=s.parseFixed();return n}function fo(e,r){var t=new b.Table(\"fvar\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"offsetToData\",type:\"USHORT\",value:0},{name:\"countSizePairs\",type:\"USHORT\",value:2},{name:\"axisCount\",type:\"USHORT\",value:e.axes.length},{name:\"axisSize\",type:\"USHORT\",value:20},{name:\"instanceCount\",type:\"USHORT\",value:e.instances.length},{name:\"instanceSize\",type:\"USHORT\",value:4+e.axes.length*4}]);t.offsetToData=t.sizeOf();for(var a=0;a<e.axes.length;a++)t.fields=t.fields.concat(io(a,e.axes[a],r));for(var n=0;n<e.instances.length;n++)t.fields=t.fields.concat(uo(n,e.instances[n],e.axes,r));return t}function po(e,r,t){var a=new k.Parser(e,r),n=a.parseULong();U.argument(n===65536,\"Unsupported fvar table version.\");var s=a.parseOffset16();a.skip(\"uShort\",1);for(var i=a.parseUShort(),u=a.parseUShort(),o=a.parseUShort(),l=a.parseUShort(),f=[],h=0;h<i;h++)f.push(oo(e,r+s+h*u,t));for(var p=[],c=r+s+i*u,d=0;d<o;d++)p.push(lo(e,c+d*l,f,t));return{axes:f,instances:p}}var ho={make:fo,parse:po},co=function(){return{coverage:this.parsePointer(v.coverage),attachPoints:this.parseList(v.pointer(v.uShortList))}},vo=function(){var e=this.parseUShort();if(U.argument(e===1||e===2||e===3,\"Unsupported CaretValue table version.\"),e===1)return{coordinate:this.parseShort()};if(e===2)return{pointindex:this.parseShort()};if(e===3)return{coordinate:this.parseShort()}},go=function(){return this.parseList(v.pointer(vo))},mo=function(){return{coverage:this.parsePointer(v.coverage),ligGlyphs:this.parseList(v.pointer(go))}},yo=function(){return this.parseUShort(),this.parseList(v.pointer(v.coverage))};function xo(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);U.argument(a===1||a===1.2||a===1.3,\"Unsupported GDEF table version.\");var n={version:a,classDef:t.parsePointer(v.classDef),attachList:t.parsePointer(co),ligCaretList:t.parsePointer(mo),markAttachClassDef:t.parsePointer(v.classDef)};return a>=1.2&&(n.markGlyphSets=t.parsePointer(yo)),n}var bo={parse:xo},te=new Array(10);te[1]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();if(t===1)return{posFormat:1,coverage:this.parsePointer(v.coverage),value:this.parseValueRecord()};if(t===2)return{posFormat:2,coverage:this.parsePointer(v.coverage),values:this.parseValueRecordList()};U.assert(!1,\"0x\"+r.toString(16)+\": GPOS lookup type 1 format must be 1 or 2.\")};te[2]=function(){var r=this.offset+this.relativeOffset,t=this.parseUShort();U.assert(t===1||t===2,\"0x\"+r.toString(16)+\": GPOS lookup type 2 format must be 1 or 2.\");var a=this.parsePointer(v.coverage),n=this.parseUShort(),s=this.parseUShort();if(t===1)return{posFormat:t,coverage:a,valueFormat1:n,valueFormat2:s,pairSets:this.parseList(v.pointer(v.list(function(){return{secondGlyph:this.parseUShort(),value1:this.parseValueRecord(n),value2:this.parseValueRecord(s)}})))};if(t===2){var i=this.parsePointer(v.classDef),u=this.parsePointer(v.classDef),o=this.parseUShort(),l=this.parseUShort();return{posFormat:t,coverage:a,valueFormat1:n,valueFormat2:s,classDef1:i,classDef2:u,class1Count:o,class2Count:l,classRecords:this.parseList(o,v.list(l,function(){return{value1:this.parseValueRecord(n),value2:this.parseValueRecord(s)}}))}}};te[3]=function(){return{error:\"GPOS Lookup 3 not supported\"}};te[4]=function(){return{error:\"GPOS Lookup 4 not supported\"}};te[5]=function(){return{error:\"GPOS Lookup 5 not supported\"}};te[6]=function(){return{error:\"GPOS Lookup 6 not supported\"}};te[7]=function(){return{error:\"GPOS Lookup 7 not supported\"}};te[8]=function(){return{error:\"GPOS Lookup 8 not supported\"}};te[9]=function(){return{error:\"GPOS Lookup 9 not supported\"}};function So(e,r){r=r||0;var t=new v(e,r),a=t.parseVersion(1);return U.argument(a===1||a===1.1,\"Unsupported GPOS table version \"+a),a===1?{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(te)}:{version:a,scripts:t.parseScriptList(),features:t.parseFeatureList(),lookups:t.parseLookupList(te),variations:t.parseFeatureVariationsList()}}var To=new Array(10);function ko(e){return new b.Table(\"GPOS\",[{name:\"version\",type:\"ULONG\",value:65536},{name:\"scripts\",type:\"TABLE\",value:new b.ScriptList(e.scripts)},{name:\"features\",type:\"TABLE\",value:new b.FeatureList(e.features)},{name:\"lookups\",type:\"TABLE\",value:new b.LookupList(e.lookups,To)}])}var Fo={parse:So,make:ko};function Uo(e){var r={};e.skip(\"uShort\");var t=e.parseUShort();U.argument(t===0,\"Unsupported kern sub-table version.\"),e.skip(\"uShort\",2);var a=e.parseUShort();e.skip(\"uShort\",3);for(var n=0;n<a;n+=1){var s=e.parseUShort(),i=e.parseUShort(),u=e.parseShort();r[s+\",\"+i]=u}return r}function Co(e){var r={};e.skip(\"uShort\");var t=e.parseULong();t>1&&console.warn(\"Only the first kern subtable is supported.\"),e.skip(\"uLong\");var a=e.parseUShort(),n=a&255;if(e.skip(\"uShort\"),n===0){var s=e.parseUShort();e.skip(\"uShort\",3);for(var i=0;i<s;i+=1){var u=e.parseUShort(),o=e.parseUShort(),l=e.parseShort();r[u+\",\"+o]=l}}return r}function Eo(e,r){var t=new k.Parser(e,r),a=t.parseUShort();if(a===0)return Uo(t);if(a===1)return Co(t);throw new Error(\"Unsupported kern table version (\"+a+\").\")}var Oo={parse:Eo};function Lo(e,r,t,a){for(var n=new k.Parser(e,r),s=a?n.parseUShort:n.parseULong,i=[],u=0;u<t+1;u+=1){var o=s.call(n);a&&(o*=2),i.push(o)}return i}var Ro={parse:Lo};function wo(e,r){var t=Ne();t.readFile(e,function(a,n){if(a)return r(a.message);r(null,$t(n))})}function Do(e,r){var t=new XMLHttpRequest;t.open(\"get\",e,!0),t.responseType=\"arraybuffer\",t.onload=function(){return t.response?r(null,t.response):r(\"Font could not be loaded: \"+t.statusText)},t.onerror=function(){r(\"Font could not be loaded\")},t.send()}function yt(e,r){for(var t=[],a=12,n=0;n<r;n+=1){var s=k.getTag(e,a),i=k.getULong(e,a+4),u=k.getULong(e,a+8),o=k.getULong(e,a+12);t.push({tag:s,checksum:i,offset:u,length:o,compression:!1}),a+=16}return t}function Ao(e,r){for(var t=[],a=44,n=0;n<r;n+=1){var s=k.getTag(e,a),i=k.getULong(e,a+4),u=k.getULong(e,a+8),o=k.getULong(e,a+12),l=void 0;u<o?l=\"WOFF\":l=!1,t.push({tag:s,offset:i,compression:l,compressedLength:u,length:o}),a+=20}return t}function I(e,r){if(r.compression===\"WOFF\"){var t=new Uint8Array(e.buffer,r.offset+2,r.compressedLength-2),a=new Uint8Array(r.length);if(Ta(t,a),a.byteLength!==r.length)throw new Error(\"Decompression error: \"+r.tag+\" decompressed length doesn't match recorded length\");var n=new DataView(a.buffer,0);return{data:n,offset:0}}else return{data:e,offset:r.offset}}function Dr(e,r){r=r??{};var t,a,n=new w({empty:!0}),s=new DataView(e,0),i,u=[],o=k.getTag(s,0);if(o===String.fromCharCode(0,1,0,0)||o===\"true\"||o===\"typ1\")n.outlinesFormat=\"truetype\",i=k.getUShort(s,4),u=yt(s,i);else if(o===\"OTTO\")n.outlinesFormat=\"cff\",i=k.getUShort(s,4),u=yt(s,i);else if(o===\"wOFF\"){var l=k.getTag(s,4);if(l===String.fromCharCode(0,1,0,0))n.outlinesFormat=\"truetype\";else if(l===\"OTTO\")n.outlinesFormat=\"cff\";else throw new Error(\"Unsupported OpenType flavor \"+o);i=k.getUShort(s,12),u=Ao(s,i)}else throw new Error(\"Unsupported OpenType signature \"+o);for(var f,h,p,c,d,x,m,y,C,S,R,O,D=0;D<i;D+=1){var L=u[D],F=void 0;switch(L.tag){case\"cmap\":F=I(s,L),n.tables.cmap=Et.parse(F.data,F.offset),n.encoding=new Lt(n.tables.cmap);break;case\"cvt \":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.cvt=O.parseShortList(L.length/2);break;case\"fvar\":h=L;break;case\"fpgm\":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.fpgm=O.parseByteList(L.length);break;case\"head\":F=I(s,L),n.tables.head=Nt.parse(F.data,F.offset),n.unitsPerEm=n.tables.head.unitsPerEm,t=n.tables.head.indexToLocFormat;break;case\"hhea\":F=I(s,L),n.tables.hhea=Ht.parse(F.data,F.offset),n.ascender=n.tables.hhea.ascender,n.descender=n.tables.hhea.descender,n.numberOfHMetrics=n.tables.hhea.numberOfHMetrics;break;case\"hmtx\":m=L;break;case\"ltag\":F=I(s,L),a=Wt.parse(F.data,F.offset);break;case\"maxp\":F=I(s,L),n.tables.maxp=_t.parse(F.data,F.offset),n.numGlyphs=n.tables.maxp.numGlyphs;break;case\"name\":S=L;break;case\"OS/2\":F=I(s,L),n.tables.os2=xr.parse(F.data,F.offset);break;case\"post\":F=I(s,L),n.tables.post=Qt.parse(F.data,F.offset),n.glyphNames=new Ur(n.tables.post);break;case\"prep\":F=I(s,L),O=new k.Parser(F.data,F.offset),n.tables.prep=O.parseByteList(L.length);break;case\"glyf\":p=L;break;case\"loca\":C=L;break;case\"CFF \":f=L;break;case\"kern\":y=L;break;case\"GDEF\":c=L;break;case\"GPOS\":d=L;break;case\"GSUB\":x=L;break;case\"meta\":R=L;break}}var G=I(s,S);if(n.tables.name=Zt.parse(G.data,G.offset,a),n.names=n.tables.name,p&&C){var Y=t===0,Z=I(s,C),j=Ro.parse(Z.data,Z.offset,n.numGlyphs,Y),$=I(s,p);n.glyphs=aa.parse($.data,$.offset,j,n,r)}else if(f){var M=I(s,f);Gt.parse(M.data,M.offset,n,r)}else throw new Error(\"Font doesn't contain TrueType or CFF outlines.\");var N=I(s,m);if(zt.parse(n,N.data,N.offset,n.numberOfHMetrics,n.numGlyphs,n.glyphs,r),_a(n,r),y){var W=I(s,y);n.kerningPairs=Oo.parse(W.data,W.offset)}else n.kerningPairs={};if(c){var _=I(s,c);n.tables.gdef=bo.parse(_.data,_.offset)}if(d){var V=I(s,d);n.tables.gpos=Fo.parse(V.data,V.offset),n.position.init()}if(x){var H=I(s,x);n.tables.gsub=Kt.parse(H.data,H.offset)}if(h){var X=I(s,h);n.tables.fvar=ho.parse(X.data,X.offset,n.names)}if(R){var A=I(s,R);n.tables.meta=Jt.parse(A.data,A.offset),n.metas=n.tables.meta}return n}function Bo(e,r,t){t=t??{};var a=typeof window>\"u\",n=a&&!t.isUrl?wo:Do;return new Promise(function(s,i){n(e,function(u,o){if(u){if(r)return r(u);i(u)}var l;try{l=Dr(o,t)}catch(f){if(r)return r(f,null);i(f)}if(r)return r(null,l);s(l)})})}function Io(e,r){var t=Ne(),a=t.readFileSync(e);return Dr($t(a),r)}var Mo=Object.freeze({__proto__:null,Font:w,Glyph:Q,Path:P,BoundingBox:pe,_parse:k,parse:Dr,load:Bo,loadSync:Io}),Ho=Mo;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/opentype.js\n"));

/***/ })

}]);