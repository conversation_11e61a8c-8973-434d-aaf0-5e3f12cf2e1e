"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_ui_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/ui.js":
/*!******************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/ui.js ***!
  \******************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(pages-dir-browser)/./node_modules/process/browser.js\");\nvar Qr=(ae=>typeof require<\"u\"?require:typeof Proxy<\"u\"?new Proxy(ae,{get:(ge,m)=>(typeof require<\"u\"?require:ge)[m]}):ae)(function(ae){if(typeof require<\"u\")return require.apply(this,arguments);throw new Error('Dynamic require of \"'+ae+'\" is not supported')});var Kn=(ae,ge)=>()=>(ge||ae((ge={exports:{}}).exports,ge),ge.exports);var Jn=Kn((zr,Kt)=>{var Zr=(()=>{var ae=typeof document<\"u\"&&document.currentScript?document.currentScript.src:void 0;return typeof __filename<\"u\"&&(ae=ae||__filename),function(ge={}){var m=ge,Jt,et;m.ready=new Promise((e,t)=>{Jt=e,et=t}),function(e){e.Id=e.Id||[],e.Id.push(function(){e.MakeSWCanvasSurface=function(t){var r=t,i=typeof OffscreenCanvas<\"u\"&&r instanceof OffscreenCanvas;if(!(typeof HTMLCanvasElement<\"u\"&&r instanceof HTMLCanvasElement||i||(r=document.getElementById(t),r)))throw\"Canvas with id \"+t+\" was not found\";return(t=e.MakeSurface(r.width,r.height))&&(t.ie=r),t},e.MakeCanvasSurface||(e.MakeCanvasSurface=e.MakeSWCanvasSurface),e.MakeSurface=function(t,r){var i={width:t,height:r,colorType:e.ColorType.RGBA_8888,alphaType:e.AlphaType.Unpremul,colorSpace:e.ColorSpace.SRGB},o=t*r*4,s=e._malloc(o);return(i=e.Surface._makeRasterDirect(i,s,4*t))&&(i.ie=null,i.Pe=t,i.Me=r,i.Ne=o,i.re=s,i.getCanvas().clear(e.TRANSPARENT)),i},e.MakeRasterDirectSurface=function(t,r,i){return e.Surface._makeRasterDirect(t,r.byteOffset,i)},e.Surface.prototype.flush=function(t){if(e.Fd(this.Ed),this._flush(),this.ie){var r=new Uint8ClampedArray(e.HEAPU8.buffer,this.re,this.Ne);r=new ImageData(r,this.Pe,this.Me),t?this.ie.getContext(\"2d\").putImageData(r,0,0,t[0],t[1],t[2]-t[0],t[3]-t[1]):this.ie.getContext(\"2d\").putImageData(r,0,0)}},e.Surface.prototype.dispose=function(){this.re&&e._free(this.re),this.delete()},e.Fd=e.Fd||function(){},e.je=e.je||function(){return null}})}(m),function(e){e.Id=e.Id||[],e.Id.push(function(){function t(f,h,g){return f&&f.hasOwnProperty(h)?f[h]:g}function r(f){var h=ke(re);return re[h]=f,h}function i(f){return f.naturalHeight||f.videoHeight||f.displayHeight||f.height}function o(f){return f.naturalWidth||f.videoWidth||f.displayWidth||f.width}function s(f,h,g,P){return f.bindTexture(f.TEXTURE_2D,h),P||g.alphaType!==e.AlphaType.Premul||f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!0),h}function l(f,h,g){g||h.alphaType!==e.AlphaType.Premul||f.pixelStorei(f.UNPACK_PREMULTIPLY_ALPHA_WEBGL,!1),f.bindTexture(f.TEXTURE_2D,null)}e.GetWebGLContext=function(f,h){if(!f)throw\"null canvas passed into makeWebGLContext\";var g={alpha:t(h,\"alpha\",1),depth:t(h,\"depth\",1),stencil:t(h,\"stencil\",8),antialias:t(h,\"antialias\",0),premultipliedAlpha:t(h,\"premultipliedAlpha\",1),preserveDrawingBuffer:t(h,\"preserveDrawingBuffer\",0),preferLowPowerToHighPerformance:t(h,\"preferLowPowerToHighPerformance\",0),failIfMajorPerformanceCaveat:t(h,\"failIfMajorPerformanceCaveat\",0),enableExtensionsByDefault:t(h,\"enableExtensionsByDefault\",1),explicitSwapControl:t(h,\"explicitSwapControl\",0),renderViaOffscreenBackBuffer:t(h,\"renderViaOffscreenBackBuffer\",0)};if(g.majorVersion=h&&h.majorVersion?h.majorVersion:typeof WebGL2RenderingContext<\"u\"?2:1,g.explicitSwapControl)throw\"explicitSwapControl is not supported\";return f=Mn(f,g),f?(Ir(f),B.Qd.getExtension(\"WEBGL_debug_renderer_info\"),f):0},e.deleteContext=function(f){B===me[f]&&(B=null),typeof JSEvents==\"object\"&&JSEvents.tf(me[f].Qd.canvas),me[f]&&me[f].Qd.canvas&&(me[f].Qd.canvas.Ke=void 0),me[f]=null},e._setTextureCleanup({deleteTexture:function(f,h){var g=re[h];g&&me[f].Qd.deleteTexture(g),re[h]=null}}),e.MakeWebGLContext=function(f){if(!this.Fd(f))return null;var h=this._MakeGrContext();if(!h)return null;h.Ed=f;var g=h.delete.bind(h);return h.delete=function(){e.Fd(this.Ed),g()}.bind(h),B.te=h},e.MakeGrContext=e.MakeWebGLContext,e.GrDirectContext.prototype.getResourceCacheLimitBytes=function(){e.Fd(this.Ed),this._getResourceCacheLimitBytes()},e.GrDirectContext.prototype.getResourceCacheUsageBytes=function(){e.Fd(this.Ed),this._getResourceCacheUsageBytes()},e.GrDirectContext.prototype.releaseResourcesAndAbandonContext=function(){e.Fd(this.Ed),this._releaseResourcesAndAbandonContext()},e.GrDirectContext.prototype.setResourceCacheLimitBytes=function(f){e.Fd(this.Ed),this._setResourceCacheLimitBytes(f)},e.MakeOnScreenGLSurface=function(f,h,g,P,E,T){return!this.Fd(f.Ed)||(h=E===void 0||T===void 0?this._MakeOnScreenGLSurface(f,h,g,P):this._MakeOnScreenGLSurface(f,h,g,P,E,T),!h)?null:(h.Ed=f.Ed,h)},e.MakeRenderTarget=function(){var f=arguments[0];if(!this.Fd(f.Ed))return null;if(arguments.length===3){var h=this._MakeRenderTargetWH(f,arguments[1],arguments[2]);if(!h)return null}else if(arguments.length===2){if(h=this._MakeRenderTargetII(f,arguments[1]),!h)return null}else return null;return h.Ed=f.Ed,h},e.MakeWebGLCanvasSurface=function(f,h,g){h=h||null;var P=f,E=typeof OffscreenCanvas<\"u\"&&P instanceof OffscreenCanvas;if(!(typeof HTMLCanvasElement<\"u\"&&P instanceof HTMLCanvasElement||E||(P=document.getElementById(f),P)))throw\"Canvas with id \"+f+\" was not found\";if(f=this.GetWebGLContext(P,g),!f||0>f)throw\"failed to create webgl context: err \"+f;return f=this.MakeWebGLContext(f),h=this.MakeOnScreenGLSurface(f,P.width,P.height,h),h||(h=P.cloneNode(!0),P.parentNode.replaceChild(h,P),h.classList.add(\"ck-replaced\"),e.MakeSWCanvasSurface(h))},e.MakeCanvasSurface=e.MakeWebGLCanvasSurface,e.Surface.prototype.makeImageFromTexture=function(f,h){return e.Fd(this.Ed),f=r(f),(h=this._makeImageFromTexture(this.Ed,f,h))&&(h.de=f),h},e.Surface.prototype.makeImageFromTextureSource=function(f,h,g){h||(h={height:i(f),width:o(f),colorType:e.ColorType.RGBA_8888,alphaType:g?e.AlphaType.Premul:e.AlphaType.Unpremul}),h.colorSpace||(h.colorSpace=e.ColorSpace.SRGB),e.Fd(this.Ed);var P=B.Qd;return g=s(P,P.createTexture(),h,g),B.version===2?P.texImage2D(P.TEXTURE_2D,0,P.RGBA,h.width,h.height,0,P.RGBA,P.UNSIGNED_BYTE,f):P.texImage2D(P.TEXTURE_2D,0,P.RGBA,P.RGBA,P.UNSIGNED_BYTE,f),l(P,h),this._resetContext(),this.makeImageFromTexture(g,h)},e.Surface.prototype.updateTextureFromSource=function(f,h,g){if(f.de){e.Fd(this.Ed);var P=f.getImageInfo(),E=B.Qd,T=s(E,re[f.de],P,g);B.version===2?E.texImage2D(E.TEXTURE_2D,0,E.RGBA,o(h),i(h),0,E.RGBA,E.UNSIGNED_BYTE,h):E.texImage2D(E.TEXTURE_2D,0,E.RGBA,E.RGBA,E.UNSIGNED_BYTE,h),l(E,P,g),this._resetContext(),re[f.de]=null,f.de=r(T),P.colorSpace=f.getColorSpace(),h=this._makeImageFromTexture(this.Ed,f.de,P),g=f.Dd.Hd,E=f.Dd.Ld,f.Dd.Hd=h.Dd.Hd,f.Dd.Ld=h.Dd.Ld,h.Dd.Hd=g,h.Dd.Ld=E,h.delete(),P.colorSpace.delete()}},e.MakeLazyImageFromTextureSource=function(f,h,g){h||(h={height:i(f),width:o(f),colorType:e.ColorType.RGBA_8888,alphaType:g?e.AlphaType.Premul:e.AlphaType.Unpremul}),h.colorSpace||(h.colorSpace=e.ColorSpace.SRGB);var P={makeTexture:function(){var E=B,T=E.Qd,v=s(T,T.createTexture(),h,g);return E.version===2?T.texImage2D(T.TEXTURE_2D,0,T.RGBA,h.width,h.height,0,T.RGBA,T.UNSIGNED_BYTE,f):T.texImage2D(T.TEXTURE_2D,0,T.RGBA,T.RGBA,T.UNSIGNED_BYTE,f),l(T,h,g),r(v)},freeSrc:function(){}};return f.constructor.name===\"VideoFrame\"&&(P.freeSrc=function(){f.close()}),e.Image._makeFromGenerator(h,P)},e.Fd=function(f){return f?Ir(f):!1},e.je=function(){return B&&B.te&&!B.te.isDeleted()?B.te:null}})}(m),function(e){function t(a){return(o(255*a[3])<<24|o(255*a[0])<<16|o(255*a[1])<<8|o(255*a[2])<<0)>>>0}function r(a){if(a&&a._ck)return a;if(a instanceof Float32Array){for(var n=Math.floor(a.length/4),u=new Uint32Array(n),c=0;c<n;c++)u[c]=t(a.slice(4*c,4*(c+1)));return u}if(a instanceof Uint32Array)return a;if(a instanceof Array&&a[0]instanceof Float32Array)return a.map(t)}function i(a){if(a===void 0)return 1;var n=parseFloat(a);return a&&a.indexOf(\"%\")!==-1?n/100:n}function o(a){return Math.round(Math.max(0,Math.min(a||0,255)))}function s(a,n){n&&n._ck||e._free(a)}function l(a,n,u){if(!a||!a.length)return L;if(a&&a._ck)return a.byteOffset;var c=e[n].BYTES_PER_ELEMENT;return u||(u=e._malloc(a.length*c)),e[n].set(a,u/c),u}function f(a){var n={Nd:L,count:a.length,colorType:e.ColorType.RGBA_F32};if(a instanceof Float32Array)n.Nd=l(a,\"HEAPF32\"),n.count=a.length/4;else if(a instanceof Uint32Array)n.Nd=l(a,\"HEAPU32\"),n.colorType=e.ColorType.RGBA_8888;else if(a instanceof Array){if(a&&a.length){for(var u=e._malloc(16*a.length),c=0,y=u/4,_=0;_<a.length;_++)for(var C=0;4>C;C++)e.HEAPF32[y+c]=a[_][C],c++;a=u}else a=L;n.Nd=a}else throw\"Invalid argument to copyFlexibleColorArray, Not a color array \"+typeof a;return n}function h(a){if(!a)return L;var n=W.toTypedArray();if(a.length){if(a.length===6||a.length===9)return l(a,\"HEAPF32\",x),a.length===6&&e.HEAPF32.set(Xn,6+x/4),x;if(a.length===16)return n[0]=a[0],n[1]=a[1],n[2]=a[3],n[3]=a[4],n[4]=a[5],n[5]=a[7],n[6]=a[12],n[7]=a[13],n[8]=a[15],x;throw\"invalid matrix size\"}if(a.m11===void 0)throw\"invalid matrix argument\";return n[0]=a.m11,n[1]=a.m21,n[2]=a.m41,n[3]=a.m12,n[4]=a.m22,n[5]=a.m42,n[6]=a.m14,n[7]=a.m24,n[8]=a.m44,x}function g(a){if(!a)return L;var n=Y.toTypedArray();if(a.length){if(a.length!==16&&a.length!==6&&a.length!==9)throw\"invalid matrix size\";return a.length===16?l(a,\"HEAPF32\",J):(n.fill(0),n[0]=a[0],n[1]=a[1],n[3]=a[2],n[4]=a[3],n[5]=a[4],n[7]=a[5],n[10]=1,n[12]=a[6],n[13]=a[7],n[15]=a[8],a.length===6&&(n[12]=0,n[13]=0,n[15]=1),J)}if(a.m11===void 0)throw\"invalid matrix argument\";return n[0]=a.m11,n[1]=a.m21,n[2]=a.m31,n[3]=a.m41,n[4]=a.m12,n[5]=a.m22,n[6]=a.m32,n[7]=a.m42,n[8]=a.m13,n[9]=a.m23,n[10]=a.m33,n[11]=a.m43,n[12]=a.m14,n[13]=a.m24,n[14]=a.m34,n[15]=a.m44,J}function P(a,n){return l(a,\"HEAPF32\",n||_e)}function E(a,n,u,c){var y=Ue.toTypedArray();return y[0]=a,y[1]=n,y[2]=u,y[3]=c,_e}function T(a){for(var n=new Float32Array(4),u=0;4>u;u++)n[u]=e.HEAPF32[a/4+u];return n}function v(a,n){return l(a,\"HEAPF32\",n||N)}function M(a,n){return l(a,\"HEAPF32\",n||Nt)}e.Color=function(a,n,u,c){return c===void 0&&(c=1),e.Color4f(o(a)/255,o(n)/255,o(u)/255,c)},e.ColorAsInt=function(a,n,u,c){return c===void 0&&(c=255),(o(c)<<24|o(a)<<16|o(n)<<8|o(u)<<0&268435455)>>>0},e.Color4f=function(a,n,u,c){return c===void 0&&(c=1),Float32Array.of(a,n,u,c)},Object.defineProperty(e,\"TRANSPARENT\",{get:function(){return e.Color4f(0,0,0,0)}}),Object.defineProperty(e,\"BLACK\",{get:function(){return e.Color4f(0,0,0,1)}}),Object.defineProperty(e,\"WHITE\",{get:function(){return e.Color4f(1,1,1,1)}}),Object.defineProperty(e,\"RED\",{get:function(){return e.Color4f(1,0,0,1)}}),Object.defineProperty(e,\"GREEN\",{get:function(){return e.Color4f(0,1,0,1)}}),Object.defineProperty(e,\"BLUE\",{get:function(){return e.Color4f(0,0,1,1)}}),Object.defineProperty(e,\"YELLOW\",{get:function(){return e.Color4f(1,1,0,1)}}),Object.defineProperty(e,\"CYAN\",{get:function(){return e.Color4f(0,1,1,1)}}),Object.defineProperty(e,\"MAGENTA\",{get:function(){return e.Color4f(1,0,1,1)}}),e.getColorComponents=function(a){return[Math.floor(255*a[0]),Math.floor(255*a[1]),Math.floor(255*a[2]),a[3]]},e.parseColorString=function(a,n){if(a=a.toLowerCase(),a.startsWith(\"#\")){switch(n=255,a.length){case 9:n=parseInt(a.slice(7,9),16);case 7:var u=parseInt(a.slice(1,3),16),c=parseInt(a.slice(3,5),16),y=parseInt(a.slice(5,7),16);break;case 5:n=17*parseInt(a.slice(4,5),16);case 4:u=17*parseInt(a.slice(1,2),16),c=17*parseInt(a.slice(2,3),16),y=17*parseInt(a.slice(3,4),16)}return e.Color(u,c,y,n/255)}return a.startsWith(\"rgba\")?(a=a.slice(5,-1),a=a.split(\",\"),e.Color(+a[0],+a[1],+a[2],i(a[3]))):a.startsWith(\"rgb\")?(a=a.slice(4,-1),a=a.split(\",\"),e.Color(+a[0],+a[1],+a[2],i(a[3]))):a.startsWith(\"gray(\")||a.startsWith(\"hsl\")||!n||(a=n[a],a===void 0)?e.BLACK:a},e.multiplyByAlpha=function(a,n){return a=a.slice(),a[3]=Math.max(0,Math.min(a[3]*n,1)),a},e.Malloc=function(a,n){var u=e._malloc(n*a.BYTES_PER_ELEMENT);return{_ck:!0,length:n,byteOffset:u,Xd:null,subarray:function(c,y){return c=this.toTypedArray().subarray(c,y),c._ck=!0,c},toTypedArray:function(){return this.Xd&&this.Xd.length?this.Xd:(this.Xd=new a(e.HEAPU8.buffer,u,n),this.Xd._ck=!0,this.Xd)}}},e.Free=function(a){e._free(a.byteOffset),a.byteOffset=L,a.toTypedArray=null,a.Xd=null};var x=L,W,J=L,Y,_e=L,Ue,de,N=L,Sr,Fe=L,Vr,St=L,Nr,Vt=L,Et,ze=L,Yr,Nt=L,Xr,Kr=L,Xn=Float32Array.of(0,0,1),L=0;e.onRuntimeInitialized=function(){function a(n,u,c,y,_,C,F){C||(C=4*y.width,y.colorType===e.ColorType.RGBA_F16?C*=2:y.colorType===e.ColorType.RGBA_F32&&(C*=4));var D=C*y.height,I=_?_.byteOffset:e._malloc(D);if(F?!n._readPixels(y,I,C,u,c,F):!n._readPixels(y,I,C,u,c))return _||e._free(I),null;if(_)return _.toTypedArray();switch(y.colorType){case e.ColorType.RGBA_8888:case e.ColorType.RGBA_F16:n=new Uint8Array(e.HEAPU8.buffer,I,D).slice();break;case e.ColorType.RGBA_F32:n=new Float32Array(e.HEAPU8.buffer,I,D).slice();break;default:return null}return e._free(I),n}Ue=e.Malloc(Float32Array,4),_e=Ue.byteOffset,Y=e.Malloc(Float32Array,16),J=Y.byteOffset,W=e.Malloc(Float32Array,9),x=W.byteOffset,Yr=e.Malloc(Float32Array,12),Nt=Yr.byteOffset,Xr=e.Malloc(Float32Array,12),Kr=Xr.byteOffset,de=e.Malloc(Float32Array,4),N=de.byteOffset,Sr=e.Malloc(Float32Array,4),Fe=Sr.byteOffset,Vr=e.Malloc(Float32Array,3),St=Vr.byteOffset,Nr=e.Malloc(Float32Array,3),Vt=Nr.byteOffset,Et=e.Malloc(Int32Array,4),ze=Et.byteOffset,e.ColorSpace.SRGB=e.ColorSpace._MakeSRGB(),e.ColorSpace.DISPLAY_P3=e.ColorSpace._MakeDisplayP3(),e.ColorSpace.ADOBE_RGB=e.ColorSpace._MakeAdobeRGB(),e.GlyphRunFlags={IsWhiteSpace:e._GlyphRunFlags_isWhiteSpace},e.Path.MakeFromCmds=function(n){var u=l(n,\"HEAPF32\"),c=e.Path._MakeFromCmds(u,n.length);return s(u,n),c},e.Path.MakeFromVerbsPointsWeights=function(n,u,c){var y=l(n,\"HEAPU8\"),_=l(u,\"HEAPF32\"),C=l(c,\"HEAPF32\"),F=e.Path._MakeFromVerbsPointsWeights(y,n.length,_,u.length,C,c&&c.length||0);return s(y,n),s(_,u),s(C,c),F},e.Path.prototype.addArc=function(n,u,c){return n=v(n),this._addArc(n,u,c),this},e.Path.prototype.addCircle=function(n,u,c,y){return this._addCircle(n,u,c,!!y),this},e.Path.prototype.addOval=function(n,u,c){return c===void 0&&(c=1),n=v(n),this._addOval(n,!!u,c),this},e.Path.prototype.addPath=function(){var n=Array.prototype.slice.call(arguments),u=n[0],c=!1;if(typeof n[n.length-1]==\"boolean\"&&(c=n.pop()),n.length===1)this._addPath(u,1,0,0,0,1,0,0,0,1,c);else if(n.length===2)n=n[1],this._addPath(u,n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1,c);else if(n.length===7||n.length===10)this._addPath(u,n[1],n[2],n[3],n[4],n[5],n[6],n[7]||0,n[8]||0,n[9]||1,c);else return null;return this},e.Path.prototype.addPoly=function(n,u){var c=l(n,\"HEAPF32\");return this._addPoly(c,n.length/2,u),s(c,n),this},e.Path.prototype.addRect=function(n,u){return n=v(n),this._addRect(n,!!u),this},e.Path.prototype.addRRect=function(n,u){return n=M(n),this._addRRect(n,!!u),this},e.Path.prototype.addVerbsPointsWeights=function(n,u,c){var y=l(n,\"HEAPU8\"),_=l(u,\"HEAPF32\"),C=l(c,\"HEAPF32\");this._addVerbsPointsWeights(y,n.length,_,u.length,C,c&&c.length||0),s(y,n),s(_,u),s(C,c)},e.Path.prototype.arc=function(n,u,c,y,_,C){return n=e.LTRBRect(n-c,u-c,n+c,u+c),_=(_-y)/Math.PI*180-360*!!C,C=new e.Path,C.addArc(n,y/Math.PI*180,_),this.addPath(C,!0),C.delete(),this},e.Path.prototype.arcToOval=function(n,u,c,y){return n=v(n),this._arcToOval(n,u,c,y),this},e.Path.prototype.arcToRotated=function(n,u,c,y,_,C,F){return this._arcToRotated(n,u,c,!!y,!!_,C,F),this},e.Path.prototype.arcToTangent=function(n,u,c,y,_){return this._arcToTangent(n,u,c,y,_),this},e.Path.prototype.close=function(){return this._close(),this},e.Path.prototype.conicTo=function(n,u,c,y,_){return this._conicTo(n,u,c,y,_),this},e.Path.prototype.computeTightBounds=function(n){this._computeTightBounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Path.prototype.cubicTo=function(n,u,c,y,_,C){return this._cubicTo(n,u,c,y,_,C),this},e.Path.prototype.dash=function(n,u,c){return this._dash(n,u,c)?this:null},e.Path.prototype.getBounds=function(n){this._getBounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Path.prototype.lineTo=function(n,u){return this._lineTo(n,u),this},e.Path.prototype.moveTo=function(n,u){return this._moveTo(n,u),this},e.Path.prototype.offset=function(n,u){return this._transform(1,0,n,0,1,u,0,0,1),this},e.Path.prototype.quadTo=function(n,u,c,y){return this._quadTo(n,u,c,y),this},e.Path.prototype.rArcTo=function(n,u,c,y,_,C,F){return this._rArcTo(n,u,c,y,_,C,F),this},e.Path.prototype.rConicTo=function(n,u,c,y,_){return this._rConicTo(n,u,c,y,_),this},e.Path.prototype.rCubicTo=function(n,u,c,y,_,C){return this._rCubicTo(n,u,c,y,_,C),this},e.Path.prototype.rLineTo=function(n,u){return this._rLineTo(n,u),this},e.Path.prototype.rMoveTo=function(n,u){return this._rMoveTo(n,u),this},e.Path.prototype.rQuadTo=function(n,u,c,y){return this._rQuadTo(n,u,c,y),this},e.Path.prototype.stroke=function(n){return n=n||{},n.width=n.width||1,n.miter_limit=n.miter_limit||4,n.cap=n.cap||e.StrokeCap.Butt,n.join=n.join||e.StrokeJoin.Miter,n.precision=n.precision||1,this._stroke(n)?this:null},e.Path.prototype.transform=function(){if(arguments.length===1){var n=arguments[0];this._transform(n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1)}else if(arguments.length===6||arguments.length===9)n=arguments,this._transform(n[0],n[1],n[2],n[3],n[4],n[5],n[6]||0,n[7]||0,n[8]||1);else throw\"transform expected to take 1 or 9 arguments. Got \"+arguments.length;return this},e.Path.prototype.trim=function(n,u,c){return this._trim(n,u,!!c)?this:null},e.Image.prototype.encodeToBytes=function(n,u){var c=e.je();return n=n||e.ImageFormat.PNG,u=u||100,c?this._encodeToBytes(n,u,c):this._encodeToBytes(n,u)},e.Image.prototype.makeShaderCubic=function(n,u,c,y,_){return _=h(_),this._makeShaderCubic(n,u,c,y,_)},e.Image.prototype.makeShaderOptions=function(n,u,c,y,_){return _=h(_),this._makeShaderOptions(n,u,c,y,_)},e.Image.prototype.readPixels=function(n,u,c,y,_){var C=e.je();return a(this,n,u,c,y,_,C)},e.Canvas.prototype.clear=function(n){e.Fd(this.Ed),n=P(n),this._clear(n)},e.Canvas.prototype.clipRRect=function(n,u,c){e.Fd(this.Ed),n=M(n),this._clipRRect(n,u,c)},e.Canvas.prototype.clipRect=function(n,u,c){e.Fd(this.Ed),n=v(n),this._clipRect(n,u,c)},e.Canvas.prototype.concat=function(n){e.Fd(this.Ed),n=g(n),this._concat(n)},e.Canvas.prototype.drawArc=function(n,u,c,y,_){e.Fd(this.Ed),n=v(n),this._drawArc(n,u,c,y,_)},e.Canvas.prototype.drawAtlas=function(n,u,c,y,_,C,F){if(n&&y&&u&&c&&u.length===c.length){e.Fd(this.Ed),_||(_=e.BlendMode.SrcOver);var D=l(u,\"HEAPF32\"),I=l(c,\"HEAPF32\"),$=c.length/4,V=l(r(C),\"HEAPU32\");if(F&&\"B\"in F&&\"C\"in F)this._drawAtlasCubic(n,I,D,V,$,_,F.B,F.C,y);else{let d=e.FilterMode.Linear,A=e.MipmapMode.None;F&&(d=F.filter,\"mipmap\"in F&&(A=F.mipmap)),this._drawAtlasOptions(n,I,D,V,$,_,d,A,y)}s(D,u),s(I,c),s(V,C)}},e.Canvas.prototype.drawCircle=function(n,u,c,y){e.Fd(this.Ed),this._drawCircle(n,u,c,y)},e.Canvas.prototype.drawColor=function(n,u){e.Fd(this.Ed),n=P(n),u!==void 0?this._drawColor(n,u):this._drawColor(n)},e.Canvas.prototype.drawColorInt=function(n,u){e.Fd(this.Ed),this._drawColorInt(n,u||e.BlendMode.SrcOver)},e.Canvas.prototype.drawColorComponents=function(n,u,c,y,_){e.Fd(this.Ed),n=E(n,u,c,y),_!==void 0?this._drawColor(n,_):this._drawColor(n)},e.Canvas.prototype.drawDRRect=function(n,u,c){e.Fd(this.Ed),n=M(n,Nt),u=M(u,Kr),this._drawDRRect(n,u,c)},e.Canvas.prototype.drawImage=function(n,u,c,y){e.Fd(this.Ed),this._drawImage(n,u,c,y||null)},e.Canvas.prototype.drawImageCubic=function(n,u,c,y,_,C){e.Fd(this.Ed),this._drawImageCubic(n,u,c,y,_,C||null)},e.Canvas.prototype.drawImageOptions=function(n,u,c,y,_,C){e.Fd(this.Ed),this._drawImageOptions(n,u,c,y,_,C||null)},e.Canvas.prototype.drawImageNine=function(n,u,c,y,_){e.Fd(this.Ed),u=l(u,\"HEAP32\",ze),c=v(c),this._drawImageNine(n,u,c,y,_||null)},e.Canvas.prototype.drawImageRect=function(n,u,c,y,_){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRect(n,N,Fe,y,!!_)},e.Canvas.prototype.drawImageRectCubic=function(n,u,c,y,_,C){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRectCubic(n,N,Fe,y,_,C||null)},e.Canvas.prototype.drawImageRectOptions=function(n,u,c,y,_,C){e.Fd(this.Ed),v(u,N),v(c,Fe),this._drawImageRectOptions(n,N,Fe,y,_,C||null)},e.Canvas.prototype.drawLine=function(n,u,c,y,_){e.Fd(this.Ed),this._drawLine(n,u,c,y,_)},e.Canvas.prototype.drawOval=function(n,u){e.Fd(this.Ed),n=v(n),this._drawOval(n,u)},e.Canvas.prototype.drawPaint=function(n){e.Fd(this.Ed),this._drawPaint(n)},e.Canvas.prototype.drawParagraph=function(n,u,c){e.Fd(this.Ed),this._drawParagraph(n,u,c)},e.Canvas.prototype.drawPatch=function(n,u,c,y,_){if(24>n.length)throw\"Need 12 cubic points\";if(u&&4>u.length)throw\"Need 4 colors\";if(c&&8>c.length)throw\"Need 4 shader coordinates\";e.Fd(this.Ed);let C=l(n,\"HEAPF32\"),F=u?l(r(u),\"HEAPU32\"):L,D=c?l(c,\"HEAPF32\"):L;y||(y=e.BlendMode.Modulate),this._drawPatch(C,F,D,y,_),s(D,c),s(F,u),s(C,n)},e.Canvas.prototype.drawPath=function(n,u){e.Fd(this.Ed),this._drawPath(n,u)},e.Canvas.prototype.drawPicture=function(n){e.Fd(this.Ed),this._drawPicture(n)},e.Canvas.prototype.drawPoints=function(n,u,c){e.Fd(this.Ed);var y=l(u,\"HEAPF32\");this._drawPoints(n,y,u.length/2,c),s(y,u)},e.Canvas.prototype.drawRRect=function(n,u){e.Fd(this.Ed),n=M(n),this._drawRRect(n,u)},e.Canvas.prototype.drawRect=function(n,u){e.Fd(this.Ed),n=v(n),this._drawRect(n,u)},e.Canvas.prototype.drawRect4f=function(n,u,c,y,_){e.Fd(this.Ed),this._drawRect4f(n,u,c,y,_)},e.Canvas.prototype.drawShadow=function(n,u,c,y,_,C,F){e.Fd(this.Ed);var D=l(_,\"HEAPF32\"),I=l(C,\"HEAPF32\");u=l(u,\"HEAPF32\",St),c=l(c,\"HEAPF32\",Vt),this._drawShadow(n,u,c,y,D,I,F),s(D,_),s(I,C)},e.getShadowLocalBounds=function(n,u,c,y,_,C,F){return n=h(n),c=l(c,\"HEAPF32\",St),y=l(y,\"HEAPF32\",Vt),this._getShadowLocalBounds(n,u,c,y,_,C,N)?(u=de.toTypedArray(),F?(F.set(u),F):u.slice()):null},e.Canvas.prototype.drawTextBlob=function(n,u,c,y){e.Fd(this.Ed),this._drawTextBlob(n,u,c,y)},e.Canvas.prototype.drawVertices=function(n,u,c){e.Fd(this.Ed),this._drawVertices(n,u,c)},e.Canvas.prototype.getDeviceClipBounds=function(n){this._getDeviceClipBounds(ze);var u=Et.toTypedArray();return n?n.set(u):n=u.slice(),n},e.Canvas.prototype.getLocalToDevice=function(){this._getLocalToDevice(J);for(var n=J,u=Array(16),c=0;16>c;c++)u[c]=e.HEAPF32[n/4+c];return u},e.Canvas.prototype.getTotalMatrix=function(){this._getTotalMatrix(x);for(var n=Array(9),u=0;9>u;u++)n[u]=e.HEAPF32[x/4+u];return n},e.Canvas.prototype.makeSurface=function(n){return n=this._makeSurface(n),n.Ed=this.Ed,n},e.Canvas.prototype.readPixels=function(n,u,c,y,_){return e.Fd(this.Ed),a(this,n,u,c,y,_)},e.Canvas.prototype.saveLayer=function(n,u,c,y){return u=v(u),this._saveLayer(n||null,u,c||null,y||0)},e.Canvas.prototype.writePixels=function(n,u,c,y,_,C,F,D){if(n.byteLength%(u*c))throw\"pixels length must be a multiple of the srcWidth * srcHeight\";e.Fd(this.Ed);var I=n.byteLength/(u*c);C=C||e.AlphaType.Unpremul,F=F||e.ColorType.RGBA_8888,D=D||e.ColorSpace.SRGB;var $=I*u;return I=l(n,\"HEAPU8\"),u=this._writePixels({width:u,height:c,colorType:F,alphaType:C,colorSpace:D},I,$,y,_),s(I,n),u},e.ColorFilter.MakeBlend=function(n,u,c){return n=P(n),c=c||e.ColorSpace.SRGB,e.ColorFilter._MakeBlend(n,u,c)},e.ColorFilter.MakeMatrix=function(n){if(!n||n.length!==20)throw\"invalid color matrix\";var u=l(n,\"HEAPF32\"),c=e.ColorFilter._makeMatrix(u);return s(u,n),c},e.ContourMeasure.prototype.getPosTan=function(n,u){return this._getPosTan(n,N),n=de.toTypedArray(),u?(u.set(n),u):n.slice()},e.ImageFilter.prototype.getOutputBounds=function(n,u,c){return n=v(n,N),u=h(u),this._getOutputBounds(n,u,ze),u=Et.toTypedArray(),c?(c.set(u),c):u.slice()},e.ImageFilter.MakeDropShadow=function(n,u,c,y,_,C){return _=P(_,_e),e.ImageFilter._MakeDropShadow(n,u,c,y,_,C)},e.ImageFilter.MakeDropShadowOnly=function(n,u,c,y,_,C){return _=P(_,_e),e.ImageFilter._MakeDropShadowOnly(n,u,c,y,_,C)},e.ImageFilter.MakeImage=function(n,u,c,y){if(c=v(c,N),y=v(y,Fe),\"B\"in u&&\"C\"in u)return e.ImageFilter._MakeImageCubic(n,u.B,u.C,c,y);let _=u.filter,C=e.MipmapMode.None;return\"mipmap\"in u&&(C=u.mipmap),e.ImageFilter._MakeImageOptions(n,_,C,c,y)},e.ImageFilter.MakeMatrixTransform=function(n,u,c){if(n=h(n),\"B\"in u&&\"C\"in u)return e.ImageFilter._MakeMatrixTransformCubic(n,u.B,u.C,c);let y=u.filter,_=e.MipmapMode.None;return\"mipmap\"in u&&(_=u.mipmap),e.ImageFilter._MakeMatrixTransformOptions(n,y,_,c)},e.Paint.prototype.getColor=function(){return this._getColor(_e),T(_e)},e.Paint.prototype.setColor=function(n,u){u=u||null,n=P(n),this._setColor(n,u)},e.Paint.prototype.setColorComponents=function(n,u,c,y,_){_=_||null,n=E(n,u,c,y),this._setColor(n,_)},e.Path.prototype.getPoint=function(n,u){return this._getPoint(n,N),n=de.toTypedArray(),u?(u[0]=n[0],u[1]=n[1],u):n.slice(0,2)},e.Picture.prototype.makeShader=function(n,u,c,y,_){return y=h(y),_=v(_),this._makeShader(n,u,c,y,_)},e.Picture.prototype.cullRect=function(n){this._cullRect(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.PictureRecorder.prototype.beginRecording=function(n,u){return n=v(n),this._beginRecording(n,!!u)},e.Surface.prototype.getCanvas=function(){var n=this._getCanvas();return n.Ed=this.Ed,n},e.Surface.prototype.makeImageSnapshot=function(n){return e.Fd(this.Ed),n=l(n,\"HEAP32\",ze),this._makeImageSnapshot(n)},e.Surface.prototype.makeSurface=function(n){return e.Fd(this.Ed),n=this._makeSurface(n),n.Ed=this.Ed,n},e.Surface.prototype.Oe=function(n,u){return this.ce||(this.ce=this.getCanvas()),requestAnimationFrame(function(){e.Fd(this.Ed),n(this.ce),this.flush(u)}.bind(this))},e.Surface.prototype.requestAnimationFrame||(e.Surface.prototype.requestAnimationFrame=e.Surface.prototype.Oe),e.Surface.prototype.Le=function(n,u){this.ce||(this.ce=this.getCanvas()),requestAnimationFrame(function(){e.Fd(this.Ed),n(this.ce),this.flush(u),this.dispose()}.bind(this))},e.Surface.prototype.drawOnce||(e.Surface.prototype.drawOnce=e.Surface.prototype.Le),e.PathEffect.MakeDash=function(n,u){if(u||(u=0),!n.length||n.length%2===1)throw\"Intervals array must have even length\";var c=l(n,\"HEAPF32\");return u=e.PathEffect._MakeDash(c,n.length,u),s(c,n),u},e.PathEffect.MakeLine2D=function(n,u){return u=h(u),e.PathEffect._MakeLine2D(n,u)},e.PathEffect.MakePath2D=function(n,u){return n=h(n),e.PathEffect._MakePath2D(n,u)},e.Shader.MakeColor=function(n,u){return u=u||null,n=P(n),e.Shader._MakeColor(n,u)},e.Shader.Blend=e.Shader.MakeBlend,e.Shader.Color=e.Shader.MakeColor,e.Shader.MakeLinearGradient=function(n,u,c,y,_,C,F,D){D=D||null;var I=f(c),$=l(y,\"HEAPF32\");F=F||0,C=h(C);var V=de.toTypedArray();return V.set(n),V.set(u,2),n=e.Shader._MakeLinearGradient(N,I.Nd,I.colorType,$,I.count,_,F,C,D),s(I.Nd,c),y&&s($,y),n},e.Shader.MakeRadialGradient=function(n,u,c,y,_,C,F,D){D=D||null;var I=f(c),$=l(y,\"HEAPF32\");return F=F||0,C=h(C),n=e.Shader._MakeRadialGradient(n[0],n[1],u,I.Nd,I.colorType,$,I.count,_,F,C,D),s(I.Nd,c),y&&s($,y),n},e.Shader.MakeSweepGradient=function(n,u,c,y,_,C,F,D,I,$){$=$||null;var V=f(c),d=l(y,\"HEAPF32\");return F=F||0,D=D||0,I=I||360,C=h(C),n=e.Shader._MakeSweepGradient(n,u,V.Nd,V.colorType,d,V.count,_,D,I,F,C,$),s(V.Nd,c),y&&s(d,y),n},e.Shader.MakeTwoPointConicalGradient=function(n,u,c,y,_,C,F,D,I,$){$=$||null;var V=f(_),d=l(C,\"HEAPF32\");I=I||0,D=h(D);var A=de.toTypedArray();return A.set(n),A.set(c,2),n=e.Shader._MakeTwoPointConicalGradient(N,u,y,V.Nd,V.colorType,d,V.count,F,I,D,$),s(V.Nd,_),C&&s(d,C),n},e.Vertices.prototype.bounds=function(n){this._bounds(N);var u=de.toTypedArray();return n?(n.set(u),n):u.slice()},e.Id&&e.Id.forEach(function(n){n()})},e.computeTonalColors=function(a){var n=l(a.ambient,\"HEAPF32\"),u=l(a.spot,\"HEAPF32\");this._computeTonalColors(n,u);var c={ambient:T(n),spot:T(u)};return s(n,a.ambient),s(u,a.spot),c},e.LTRBRect=function(a,n,u,c){return Float32Array.of(a,n,u,c)},e.XYWHRect=function(a,n,u,c){return Float32Array.of(a,n,a+u,n+c)},e.LTRBiRect=function(a,n,u,c){return Int32Array.of(a,n,u,c)},e.XYWHiRect=function(a,n,u,c){return Int32Array.of(a,n,a+u,n+c)},e.RRectXY=function(a,n,u){return Float32Array.of(a[0],a[1],a[2],a[3],n,u,n,u,n,u,n,u)},e.MakeAnimatedImageFromEncoded=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._decodeAnimatedImage(n,a.byteLength))?a:null},e.MakeImageFromEncoded=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._decodeImage(n,a.byteLength))?a:null};var qe=null;e.MakeImageFromCanvasImageSource=function(a){var n=a.width,u=a.height;qe||(qe=document.createElement(\"canvas\")),qe.width=n,qe.height=u;var c=qe.getContext(\"2d\",{willReadFrequently:!0});return c.drawImage(a,0,0),a=c.getImageData(0,0,n,u),e.MakeImage({width:n,height:u,alphaType:e.AlphaType.Unpremul,colorType:e.ColorType.RGBA_8888,colorSpace:e.ColorSpace.SRGB},a.data,4*n)},e.MakeImage=function(a,n,u){var c=e._malloc(n.length);return e.HEAPU8.set(n,c),e._MakeImage(a,c,n.length,u)},e.MakeVertices=function(a,n,u,c,y,_){var C=y&&y.length||0,F=0;return u&&u.length&&(F|=1),c&&c.length&&(F|=2),_===void 0||_||(F|=4),a=new e._VerticesBuilder(a,n.length/2,C,F),l(n,\"HEAPF32\",a.positions()),a.texCoords()&&l(u,\"HEAPF32\",a.texCoords()),a.colors()&&l(r(c),\"HEAPU32\",a.colors()),a.indices()&&l(y,\"HEAPU16\",a.indices()),a.detach()},function(a){a.Id=a.Id||[],a.Id.push(function(){function n(d){return d&&(d.dir=d.dir===0?a.TextDirection.RTL:a.TextDirection.LTR),d}function u(d){if(!d||!d.length)return[];for(var A=[],U=0;U<d.length;U+=5){var X=a.LTRBRect(d[U],d[U+1],d[U+2],d[U+3]),Ce=a.TextDirection.LTR;d[U+4]===0&&(Ce=a.TextDirection.RTL),A.push({rect:X,dir:Ce})}return a._free(d.byteOffset),A}function c(d){return d=d||{},d.weight===void 0&&(d.weight=a.FontWeight.Normal),d.width=d.width||a.FontWidth.Normal,d.slant=d.slant||a.FontSlant.Upright,d}function y(d){if(!d||!d.length)return L;for(var A=[],U=0;U<d.length;U++){var X=_(d[U]);A.push(X)}return l(A,\"HEAPU32\")}function _(d){if(D[d])return D[d];var A=le(d)+1,U=a._malloc(A);return se(d,G,U,A),D[d]=U}function C(d){if(d._colorPtr=P(d.color),d._foregroundColorPtr=L,d._backgroundColorPtr=L,d._decorationColorPtr=L,d.foregroundColor&&(d._foregroundColorPtr=P(d.foregroundColor,I)),d.backgroundColor&&(d._backgroundColorPtr=P(d.backgroundColor,$)),d.decorationColor&&(d._decorationColorPtr=P(d.decorationColor,V)),Array.isArray(d.fontFamilies)&&d.fontFamilies.length?(d._fontFamiliesPtr=y(d.fontFamilies),d._fontFamiliesLen=d.fontFamilies.length):(d._fontFamiliesPtr=L,d._fontFamiliesLen=0),d.locale){var A=d.locale;d._localePtr=_(A),d._localeLen=le(A)+1}else d._localePtr=L,d._localeLen=0;if(Array.isArray(d.shadows)&&d.shadows.length){A=d.shadows;var U=A.map(function(he){return he.color||a.BLACK}),X=A.map(function(he){return he.blurRadius||0});d._shadowLen=A.length;for(var Ce=a._malloc(8*A.length),Yt=Ce/4,Xt=0;Xt<A.length;Xt++){var Jr=A[Xt].offset||[0,0];a.HEAPF32[Yt]=Jr[0],a.HEAPF32[Yt+1]=Jr[1],Yt+=2}d._shadowColorsPtr=f(U).Nd,d._shadowOffsetsPtr=Ce,d._shadowBlurRadiiPtr=l(X,\"HEAPF32\")}else d._shadowLen=0,d._shadowColorsPtr=L,d._shadowOffsetsPtr=L,d._shadowBlurRadiiPtr=L;Array.isArray(d.fontFeatures)&&d.fontFeatures.length?(A=d.fontFeatures,U=A.map(function(he){return he.name}),X=A.map(function(he){return he.value}),d._fontFeatureLen=A.length,d._fontFeatureNamesPtr=y(U),d._fontFeatureValuesPtr=l(X,\"HEAPU32\")):(d._fontFeatureLen=0,d._fontFeatureNamesPtr=L,d._fontFeatureValuesPtr=L),Array.isArray(d.fontVariations)&&d.fontVariations.length?(A=d.fontVariations,U=A.map(function(he){return he.axis}),X=A.map(function(he){return he.value}),d._fontVariationLen=A.length,d._fontVariationAxesPtr=y(U),d._fontVariationValuesPtr=l(X,\"HEAPF32\")):(d._fontVariationLen=0,d._fontVariationAxesPtr=L,d._fontVariationValuesPtr=L)}function F(d){a._free(d._fontFamiliesPtr),a._free(d._shadowColorsPtr),a._free(d._shadowOffsetsPtr),a._free(d._shadowBlurRadiiPtr),a._free(d._fontFeatureNamesPtr),a._free(d._fontFeatureValuesPtr),a._free(d._fontVariationAxesPtr),a._free(d._fontVariationValuesPtr)}a.Paragraph.prototype.getRectsForRange=function(d,A,U,X){return d=this._getRectsForRange(d,A,U,X),u(d)},a.Paragraph.prototype.getRectsForPlaceholders=function(){var d=this._getRectsForPlaceholders();return u(d)},a.Paragraph.prototype.getGlyphInfoAt=function(d){return n(this._getGlyphInfoAt(d))},a.Paragraph.prototype.getClosestGlyphInfoAtCoordinate=function(d,A){return n(this._getClosestGlyphInfoAtCoordinate(d,A))},a.TypefaceFontProvider.prototype.registerFont=function(d,A){if(d=a.Typeface.MakeFreeTypeFaceFromData(d),!d)return null;A=_(A),this._registerFont(d,A)},a.ParagraphStyle=function(d){if(d.disableHinting=d.disableHinting||!1,d.ellipsis){var A=d.ellipsis;d._ellipsisPtr=_(A),d._ellipsisLen=le(A)+1}else d._ellipsisPtr=L,d._ellipsisLen=0;return d.heightMultiplier==null&&(d.heightMultiplier=-1),d.maxLines=d.maxLines||0,d.replaceTabCharacters=d.replaceTabCharacters||!1,A=(A=d.strutStyle)||{},A.strutEnabled=A.strutEnabled||!1,A.strutEnabled&&Array.isArray(A.fontFamilies)&&A.fontFamilies.length?(A._fontFamiliesPtr=y(A.fontFamilies),A._fontFamiliesLen=A.fontFamilies.length):(A._fontFamiliesPtr=L,A._fontFamiliesLen=0),A.fontStyle=c(A.fontStyle),A.fontSize==null&&(A.fontSize=-1),A.heightMultiplier==null&&(A.heightMultiplier=-1),A.halfLeading=A.halfLeading||!1,A.leading=A.leading||0,A.forceStrutHeight=A.forceStrutHeight||!1,d.strutStyle=A,d.textAlign=d.textAlign||a.TextAlign.Start,d.textDirection=d.textDirection||a.TextDirection.LTR,d.textHeightBehavior=d.textHeightBehavior||a.TextHeightBehavior.All,d.textStyle=a.TextStyle(d.textStyle),d.applyRoundingHack=d.applyRoundingHack!==!1,d},a.TextStyle=function(d){return d.color||(d.color=a.BLACK),d.decoration=d.decoration||0,d.decorationThickness=d.decorationThickness||0,d.decorationStyle=d.decorationStyle||a.DecorationStyle.Solid,d.textBaseline=d.textBaseline||a.TextBaseline.Alphabetic,d.fontSize==null&&(d.fontSize=-1),d.letterSpacing=d.letterSpacing||0,d.wordSpacing=d.wordSpacing||0,d.heightMultiplier==null&&(d.heightMultiplier=-1),d.halfLeading=d.halfLeading||!1,d.fontStyle=c(d.fontStyle),d};var D={},I=a._malloc(16),$=a._malloc(16),V=a._malloc(16);a.ParagraphBuilder.Make=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._Make(d,A),F(d.textStyle),A},a.ParagraphBuilder.MakeFromFontProvider=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._MakeFromFontProvider(d,A),F(d.textStyle),A},a.ParagraphBuilder.MakeFromFontCollection=function(d,A){return C(d.textStyle),A=a.ParagraphBuilder._MakeFromFontCollection(d,A),F(d.textStyle),A},a.ParagraphBuilder.ShapeText=function(d,A,U){let X=0;for(let Ce of A)X+=Ce.length;if(X!==d.length)throw\"Accumulated block lengths must equal text.length\";return a.ParagraphBuilder._ShapeText(d,A,U)},a.ParagraphBuilder.prototype.pushStyle=function(d){C(d),this._pushStyle(d),F(d)},a.ParagraphBuilder.prototype.pushPaintStyle=function(d,A,U){C(d),this._pushPaintStyle(d,A,U),F(d)},a.ParagraphBuilder.prototype.addPlaceholder=function(d,A,U,X,Ce){U=U||a.PlaceholderAlignment.Baseline,X=X||a.TextBaseline.Alphabetic,this._addPlaceholder(d||0,A||0,U,X,Ce||0)},a.ParagraphBuilder.prototype.setWordsUtf8=function(d){var A=l(d,\"HEAPU32\");this._setWordsUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setWordsUtf16=function(d){var A=l(d,\"HEAPU32\");this._setWordsUtf16(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setGraphemeBreaksUtf8=function(d){var A=l(d,\"HEAPU32\");this._setGraphemeBreaksUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setGraphemeBreaksUtf16=function(d){var A=l(d,\"HEAPU32\");this._setGraphemeBreaksUtf16(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setLineBreaksUtf8=function(d){var A=l(d,\"HEAPU32\");this._setLineBreaksUtf8(A,d&&d.length||0),s(A,d)},a.ParagraphBuilder.prototype.setLineBreaksUtf16=function(d){var A=l(d,\"HEAPU32\");this._setLineBreaksUtf16(A,d&&d.length||0),s(A,d)}})}(m),e.Id=e.Id||[],e.Id.push(function(){e.Path.prototype.op=function(a,n){return this._op(a,n)?this:null},e.Path.prototype.simplify=function(){return this._simplify()?this:null}}),e.Id=e.Id||[],e.Id.push(function(){e.Canvas.prototype.drawText=function(a,n,u,c,y){var _=le(a),C=e._malloc(_+1);se(a,G,C,_+1),this._drawSimpleText(C,_,n,u,y,c),e._free(C)},e.Canvas.prototype.drawGlyphs=function(a,n,u,c,y,_){if(!(2*a.length<=n.length))throw\"Not enough positions for the array of gyphs\";e.Fd(this.Ed);let C=l(a,\"HEAPU16\"),F=l(n,\"HEAPF32\");this._drawGlyphs(a.length,C,F,u,c,y,_),s(F,n),s(C,a)},e.Font.prototype.getGlyphBounds=function(a,n,u){var c=l(a,\"HEAPU16\"),y=e._malloc(16*a.length);return this._getGlyphWidthBounds(c,a.length,L,y,n||null),n=new Float32Array(e.HEAPU8.buffer,y,4*a.length),s(c,a),u?(u.set(n),e._free(y),u):(a=Float32Array.from(n),e._free(y),a)},e.Font.prototype.getGlyphIDs=function(a,n,u){n||(n=a.length);var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=e._malloc(2*n),n=this._getGlyphIDs(y,c-1,n,a),e._free(y),0>n?(e._free(a),null):(y=new Uint16Array(e.HEAPU8.buffer,a,n),u?(u.set(y),e._free(a),u):(u=Uint16Array.from(y),e._free(a),u))},e.Font.prototype.getGlyphIntercepts=function(a,n,u,c){var y=l(a,\"HEAPU16\"),_=l(n,\"HEAPF32\");return this._getGlyphIntercepts(y,a.length,!(a&&a._ck),_,n.length,!(n&&n._ck),u,c)},e.Font.prototype.getGlyphWidths=function(a,n,u){var c=l(a,\"HEAPU16\"),y=e._malloc(4*a.length);return this._getGlyphWidthBounds(c,a.length,y,L,n||null),n=new Float32Array(e.HEAPU8.buffer,y,a.length),s(c,a),u?(u.set(n),e._free(y),u):(a=Float32Array.from(n),e._free(y),a)},e.FontMgr.FromData=function(){if(!arguments.length)return null;var a=arguments;if(a.length===1&&Array.isArray(a[0])&&(a=arguments[0]),!a.length)return null;for(var n=[],u=[],c=0;c<a.length;c++){var y=new Uint8Array(a[c]),_=l(y,\"HEAPU8\");n.push(_),u.push(y.byteLength)}return n=l(n,\"HEAPU32\"),u=l(u,\"HEAPU32\"),a=e.FontMgr._fromData(n,u,a.length),e._free(n),e._free(u),a},e.Typeface.MakeFreeTypeFaceFromData=function(a){a=new Uint8Array(a);var n=l(a,\"HEAPU8\");return(a=e.Typeface._MakeFreeTypeFaceFromData(n,a.byteLength))?a:null},e.Typeface.prototype.getGlyphIDs=function(a,n,u){n||(n=a.length);var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=e._malloc(2*n),n=this._getGlyphIDs(y,c-1,n,a),e._free(y),0>n?(e._free(a),null):(y=new Uint16Array(e.HEAPU8.buffer,a,n),u?(u.set(y),e._free(a),u):(u=Uint16Array.from(y),e._free(a),u))},e.TextBlob.MakeOnPath=function(a,n,u,c){if(a&&a.length&&n&&n.countPoints()){if(n.countPoints()===1)return this.MakeFromText(a,u);c||(c=0);var y=u.getGlyphIDs(a);y=u.getGlyphWidths(y);var _=[];n=new e.ContourMeasureIter(n,!1,1);for(var C=n.next(),F=new Float32Array(4),D=0;D<a.length&&C;D++){var I=y[D];if(c+=I/2,c>C.length()){if(C.delete(),C=n.next(),!C){a=a.substring(0,D);break}c=I/2}C.getPosTan(c,F);var $=F[2],V=F[3];_.push($,V,F[0]-I/2*$,F[1]-I/2*V),c+=I/2}return a=this.MakeFromRSXform(a,_,u),C&&C.delete(),n.delete(),a}},e.TextBlob.MakeFromRSXform=function(a,n,u){var c=le(a)+1,y=e._malloc(c);return se(a,G,y,c),a=l(n,\"HEAPF32\"),u=e.TextBlob._MakeFromRSXform(y,c-1,a,u),e._free(y),u||null},e.TextBlob.MakeFromRSXformGlyphs=function(a,n,u){var c=l(a,\"HEAPU16\");return n=l(n,\"HEAPF32\"),u=e.TextBlob._MakeFromRSXformGlyphs(c,2*a.length,n,u),s(c,a),u||null},e.TextBlob.MakeFromGlyphs=function(a,n){var u=l(a,\"HEAPU16\");return n=e.TextBlob._MakeFromGlyphs(u,2*a.length,n),s(u,a),n||null},e.TextBlob.MakeFromText=function(a,n){var u=le(a)+1,c=e._malloc(u);return se(a,G,c,u),a=e.TextBlob._MakeFromText(c,u-1,n),e._free(c),a||null},e.MallocGlyphIDs=function(a){return e.Malloc(Uint16Array,a)}}),e.Id=e.Id||[],e.Id.push(function(){e.MakePicture=function(a){a=new Uint8Array(a);var n=e._malloc(a.byteLength);return e.HEAPU8.set(a,n),(a=e._MakePicture(n,a.byteLength))?a:null}}),e.Id=e.Id||[],e.Id.push(function(){e.RuntimeEffect.Make=function(a,n){return e.RuntimeEffect._Make(a,{onError:n||function(u){console.log(\"RuntimeEffect error\",u)}})},e.RuntimeEffect.MakeForBlender=function(a,n){return e.RuntimeEffect._MakeForBlender(a,{onError:n||function(u){console.log(\"RuntimeEffect error\",u)}})},e.RuntimeEffect.prototype.makeShader=function(a,n){var u=!a._ck,c=l(a,\"HEAPF32\");return n=h(n),this._makeShader(c,4*a.length,u,n)},e.RuntimeEffect.prototype.makeShaderWithChildren=function(a,n,u){var c=!a._ck,y=l(a,\"HEAPF32\");u=h(u);for(var _=[],C=0;C<n.length;C++)_.push(n[C].Dd.Hd);return n=l(_,\"HEAPU32\"),this._makeShaderWithChildren(y,4*a.length,c,n,_.length,u)},e.RuntimeEffect.prototype.makeBlender=function(a){var n=!a._ck,u=l(a,\"HEAPF32\");return this._makeBlender(u,4*a.length,n)}})}(m);var Qt=Object.assign({},m),wt=\"./this.program\",Zt=typeof window==\"object\",Oe=typeof importScripts==\"function\",zt=typeof process==\"object\"&&typeof process.versions==\"object\"&&typeof process.versions.node==\"string\",z=\"\",Tt,tt,rt;if(zt){var qt=Qr(\"fs\"),Ft=Qr(\"path\");z=Oe?Ft.dirname(z)+\"/\":__dirname+\"/\",Tt=(e,t)=>(e=e.startsWith(\"file://\")?new URL(e):Ft.normalize(e),qt.readFileSync(e,t?void 0:\"utf8\")),rt=e=>(e=Tt(e,!0),e.buffer||(e=new Uint8Array(e)),e),tt=(e,t,r,i=!0)=>{e=e.startsWith(\"file://\")?new URL(e):Ft.normalize(e),qt.readFile(e,i?void 0:\"utf8\",(o,s)=>{o?r(o):t(i?s.buffer:s)})},!m.thisProgram&&1<process.argv.length&&(wt=process.argv[1].replace(/\\\\/g,\"/\")),process.argv.slice(2),m.inspect=()=>\"[Emscripten Module object]\"}else(Zt||Oe)&&(Oe?z=self.location.href:typeof document<\"u\"&&document.currentScript&&(z=document.currentScript.src),ae&&(z=ae),z.indexOf(\"blob:\")!==0?z=z.substr(0,z.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1):z=\"\",Tt=e=>{var t=new XMLHttpRequest;return t.open(\"GET\",e,!1),t.send(null),t.responseText},Oe&&(rt=e=>{var t=new XMLHttpRequest;return t.open(\"GET\",e,!1),t.responseType=\"arraybuffer\",t.send(null),new Uint8Array(t.response)}),tt=(e,t,r)=>{var i=new XMLHttpRequest;i.open(\"GET\",e,!0),i.responseType=\"arraybuffer\",i.onload=()=>{i.status==200||i.status==0&&i.response?t(i.response):r()},i.onerror=r,i.send(null)});var qr=m.print||console.log.bind(console),Ae=m.printErr||console.error.bind(console);Object.assign(m,Qt),Qt=null,m.thisProgram&&(wt=m.thisProgram);var je;m.wasmBinary&&(je=m.wasmBinary);var Qn=m.noExitRuntime||!0;typeof WebAssembly!=\"object\"&&Rt(\"no native wasm support detected\");var nt,b,er=!1,pe,G,Ee,Be,w,O,R,tr;function rr(){var e=nt.buffer;m.HEAP8=pe=new Int8Array(e),m.HEAP16=Ee=new Int16Array(e),m.HEAP32=w=new Int32Array(e),m.HEAPU8=G=new Uint8Array(e),m.HEAPU16=Be=new Uint16Array(e),m.HEAPU32=O=new Uint32Array(e),m.HEAPF32=R=new Float32Array(e),m.HEAPF64=tr=new Float64Array(e)}var Q,nr=[],ir=[],or=[];function en(){var e=m.preRun.shift();nr.unshift(e)}var Me=0,Mt=null,We=null;function Rt(e){throw m.onAbort&&m.onAbort(e),e=\"Aborted(\"+e+\")\",Ae(e),er=!0,e=new WebAssembly.RuntimeError(e+\". Build with -sASSERTIONS for more info.\"),et(e),e}function ar(e){return e.startsWith(\"data:application/octet-stream;base64,\")}var Ge;if(Ge=\"canvaskit.wasm\",!ar(Ge)){var ur=Ge;Ge=m.locateFile?m.locateFile(ur,z):z+ur}function sr(e){if(e==Ge&&je)return new Uint8Array(je);if(rt)return rt(e);throw\"both async and sync fetching of the wasm failed\"}function tn(e){if(!je&&(Zt||Oe)){if(typeof fetch==\"function\"&&!e.startsWith(\"file://\"))return fetch(e,{credentials:\"same-origin\"}).then(t=>{if(!t.ok)throw\"failed to load wasm binary file at '\"+e+\"'\";return t.arrayBuffer()}).catch(()=>sr(e));if(tt)return new Promise((t,r)=>{tt(e,i=>t(new Uint8Array(i)),r)})}return Promise.resolve().then(()=>sr(e))}function lr(e,t,r){return tn(e).then(i=>WebAssembly.instantiate(i,t)).then(i=>i).then(r,i=>{Ae(\"failed to asynchronously prepare wasm: \"+i),Rt(i)})}function rn(e,t){var r=Ge;return je||typeof WebAssembly.instantiateStreaming!=\"function\"||ar(r)||r.startsWith(\"file://\")||zt||typeof fetch!=\"function\"?lr(r,e,t):fetch(r,{credentials:\"same-origin\"}).then(i=>WebAssembly.instantiateStreaming(i,e).then(t,function(o){return Ae(\"wasm streaming compile failed: \"+o),Ae(\"falling back to ArrayBuffer instantiation\"),lr(r,e,t)}))}var xt=e=>{for(;0<e.length;)e.shift()(m)},fr=typeof TextDecoder<\"u\"?new TextDecoder(\"utf8\"):void 0,Re=(e,t,r)=>{var i=t+r;for(r=t;e[r]&&!(r>=i);)++r;if(16<r-t&&e.buffer&&fr)return fr.decode(e.subarray(t,r));for(i=\"\";t<r;){var o=e[t++];if(o&128){var s=e[t++]&63;if((o&224)==192)i+=String.fromCharCode((o&31)<<6|s);else{var l=e[t++]&63;o=(o&240)==224?(o&15)<<12|s<<6|l:(o&7)<<18|s<<12|l<<6|e[t++]&63,65536>o?i+=String.fromCharCode(o):(o-=65536,i+=String.fromCharCode(55296|o>>10,56320|o&1023))}}else i+=String.fromCharCode(o)}return i},it={};function It(e){for(;e.length;){var t=e.pop();e.pop()(t)}}function $e(e){return this.fromWireType(w[e>>2])}var Le={},xe={},ot={},cr=void 0;function at(e){throw new cr(e)}function ue(e,t,r){function i(f){f=r(f),f.length!==e.length&&at(\"Mismatched type converter count\");for(var h=0;h<e.length;++h)ye(e[h],f[h])}e.forEach(function(f){ot[f]=t});var o=Array(t.length),s=[],l=0;t.forEach((f,h)=>{xe.hasOwnProperty(f)?o[h]=xe[f]:(s.push(f),Le.hasOwnProperty(f)||(Le[f]=[]),Le[f].push(()=>{o[h]=xe[f],++l,l===s.length&&i(o)}))}),s.length===0&&i(o)}function ut(e){switch(e){case 1:return 0;case 2:return 1;case 4:return 2;case 8:return 3;default:throw new TypeError(`Unknown type size: ${e}`)}}var dr=void 0;function S(e){for(var t=\"\";G[e];)t+=dr[G[e++]];return t}var be=void 0;function k(e){throw new be(e)}function nn(e,t,r={}){var i=t.name;if(e||k(`type \"${i}\" must have a positive integer typeid pointer`),xe.hasOwnProperty(e)){if(r.af)return;k(`Cannot register type '${i}' twice`)}xe[e]=t,delete ot[e],Le.hasOwnProperty(e)&&(t=Le[e],delete Le[e],t.forEach(o=>o()))}function ye(e,t,r={}){if(!(\"argPackAdvance\"in t))throw new TypeError(\"registerType registeredInstance requires argPackAdvance\");nn(e,t,r)}function Dt(e){k(e.Dd.Jd.Gd.name+\" instance already deleted\")}var Bt=!1;function hr(){}function pr(e){--e.count.value,e.count.value===0&&(e.Ld?e.Pd.Td(e.Ld):e.Jd.Gd.Td(e.Hd))}function yr(e,t,r){return t===r?e:r.Md===void 0?null:(e=yr(e,t,r.Md),e===null?null:r.Te(e))}var vr={},Se=[];function Gt(){for(;Se.length;){var e=Se.pop();e.Dd.$d=!1,e.delete()}}var Ve=void 0,Ne={};function on(e,t){for(t===void 0&&k(\"ptr should not be undefined\");e.Md;)t=e.ge(t),e=e.Md;return Ne[t]}function st(e,t){return t.Jd&&t.Hd||at(\"makeClassHandle requires ptr and ptrType\"),!!t.Pd!=!!t.Ld&&at(\"Both smartPtrType and smartPtr must be specified\"),t.count={value:1},Ye(Object.create(e,{Dd:{value:t}}))}function Ye(e){return typeof FinalizationRegistry>\"u\"?(Ye=t=>t,e):(Bt=new FinalizationRegistry(t=>{pr(t.Dd)}),Ye=t=>{var r=t.Dd;return r.Ld&&Bt.register(t,{Dd:r},t),t},hr=t=>{Bt.unregister(t)},Ye(e))}function we(){}function mr(e){if(e===void 0)return\"_unknown\";e=e.replace(/[^a-zA-Z0-9_]/g,\"$\");var t=e.charCodeAt(0);return 48<=t&&57>=t?`_${e}`:e}function Lt(e,t){return e=mr(e),{[e]:function(){return t.apply(this,arguments)}}[e]}function bt(e,t,r){if(e[t].Kd===void 0){var i=e[t];e[t]=function(){return e[t].Kd.hasOwnProperty(arguments.length)||k(`Function '${r}' called with an invalid number of arguments (${arguments.length}) - expects one of (${e[t].Kd})!`),e[t].Kd[arguments.length].apply(this,arguments)},e[t].Kd=[],e[t].Kd[i.Yd]=i}}function kt(e,t,r){m.hasOwnProperty(e)?((r===void 0||m[e].Kd!==void 0&&m[e].Kd[r]!==void 0)&&k(`Cannot register public name '${e}' twice`),bt(m,e,e),m.hasOwnProperty(r)&&k(`Cannot register multiple overloads of a function with the same number of arguments (${r})!`),m[e].Kd[r]=t):(m[e]=t,r!==void 0&&(m[e].sf=r))}function an(e,t,r,i,o,s,l,f){this.name=e,this.constructor=t,this.ae=r,this.Td=i,this.Md=o,this.We=s,this.ge=l,this.Te=f,this.ef=[]}function Ht(e,t,r){for(;t!==r;)t.ge||k(`Expected null or instance of ${r.name}, got an instance of ${t.name}`),e=t.ge(e),t=t.Md;return e}function un(e,t){return t===null?(this.ue&&k(`null is not a valid ${this.name}`),0):(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function sn(e,t){if(t===null){if(this.ue&&k(`null is not a valid ${this.name}`),this.le){var r=this.ve();return e!==null&&e.push(this.Td,r),r}return 0}if(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),!this.ke&&t.Dd.Jd.ke&&k(`Cannot convert argument of type ${t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name} to parameter type ${this.name}`),r=Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd),this.le)switch(t.Dd.Ld===void 0&&k(\"Passing raw pointer to smart pointer is illegal\"),this.kf){case 0:t.Dd.Pd===this?r=t.Dd.Ld:k(`Cannot convert argument of type ${t.Dd.Pd?t.Dd.Pd.name:t.Dd.Jd.name} to parameter type ${this.name}`);break;case 1:r=t.Dd.Ld;break;case 2:if(t.Dd.Pd===this)r=t.Dd.Ld;else{var i=t.clone();r=this.ff(r,te(function(){i.delete()})),e!==null&&e.push(this.Td,r)}break;default:k(\"Unsupporting sharing policy\")}return r}function ln(e,t){return t===null?(this.ue&&k(`null is not a valid ${this.name}`),0):(t.Dd||k(`Cannot pass \"${Ot(t)}\" as a ${this.name}`),t.Dd.Hd||k(`Cannot pass deleted object as a pointer of type ${this.name}`),t.Dd.Jd.ke&&k(`Cannot convert argument of type ${t.Dd.Jd.name} to parameter type ${this.name}`),Ht(t.Dd.Hd,t.Dd.Jd.Gd,this.Gd))}function ve(e,t,r,i,o,s,l,f,h,g,P){this.name=e,this.Gd=t,this.ue=r,this.ke=i,this.le=o,this.df=s,this.kf=l,this.Ee=f,this.ve=h,this.ff=g,this.Td=P,o||t.Md!==void 0?this.toWireType=sn:(this.toWireType=i?un:ln,this.Od=null)}function _r(e,t,r){m.hasOwnProperty(e)||at(\"Replacing nonexistant public symbol\"),m[e].Kd!==void 0&&r!==void 0?m[e].Kd[r]=t:(m[e]=t,m[e].Yd=r)}var fn=(e,t)=>{var r=[];return function(){if(r.length=0,Object.assign(r,arguments),e.includes(\"j\")){var i=m[\"dynCall_\"+e];i=r&&r.length?i.apply(null,[t].concat(r)):i.call(null,t)}else i=Q.get(t).apply(null,r);return i}};function K(e,t){e=S(e);var r=e.includes(\"j\")?fn(e,t):Q.get(t);return typeof r!=\"function\"&&k(`unknown function pointer with signature ${e}: ${t}`),r}var gr=void 0;function Pr(e){e=Wr(e);var t=S(e);return Pe(e),t}function Xe(e,t){function r(s){o[s]||xe[s]||(ot[s]?ot[s].forEach(r):(i.push(s),o[s]=!0))}var i=[],o={};throw t.forEach(r),new gr(`${e}: `+i.map(Pr).join([\", \"]))}function lt(e,t,r,i,o){var s=t.length;2>s&&k(\"argTypes array size mismatch! Must at least get return value and 'this' types!\");var l=t[1]!==null&&r!==null,f=!1;for(r=1;r<t.length;++r)if(t[r]!==null&&t[r].Od===void 0){f=!0;break}var h=t[0].name!==\"void\",g=s-2,P=Array(g),E=[],T=[];return function(){if(arguments.length!==g&&k(`function ${e} called with ${arguments.length} arguments, expected ${g} args!`),T.length=0,E.length=l?2:1,E[0]=o,l){var v=t[1].toWireType(T,this);E[1]=v}for(var M=0;M<g;++M)P[M]=t[M+2].toWireType(T,arguments[M]),E.push(P[M]);if(M=i.apply(null,E),f)It(T);else for(var x=l?1:2;x<t.length;x++){var W=x===1?v:P[x-2];t[x].Od!==null&&t[x].Od(W)}return v=h?t[0].fromWireType(M):void 0,v}}function ft(e,t){for(var r=[],i=0;i<e;i++)r.push(O[t+4*i>>2]);return r}function Cr(){this.Sd=[void 0],this.Ce=[]}var q=new Cr;function Ut(e){e>=q.be&&--q.get(e).Fe===0&&q.Je(e)}var ee=e=>(e||k(\"Cannot use deleted val. handle = \"+e),q.get(e).value),te=e=>{switch(e){case void 0:return 1;case null:return 2;case!0:return 3;case!1:return 4;default:return q.Ie({Fe:1,value:e})}};function cn(e,t,r){switch(t){case 0:return function(i){return this.fromWireType((r?pe:G)[i])};case 1:return function(i){return this.fromWireType((r?Ee:Be)[i>>1])};case 2:return function(i){return this.fromWireType((r?w:O)[i>>2])};default:throw new TypeError(\"Unknown integer type: \"+e)}}function Ke(e,t){var r=xe[e];return r===void 0&&k(t+\" has unknown type \"+Pr(e)),r}function Ot(e){if(e===null)return\"null\";var t=typeof e;return t===\"object\"||t===\"array\"||t===\"function\"?e.toString():\"\"+e}function dn(e,t){switch(t){case 2:return function(r){return this.fromWireType(R[r>>2])};case 3:return function(r){return this.fromWireType(tr[r>>3])};default:throw new TypeError(\"Unknown float type: \"+e)}}function hn(e,t,r){switch(t){case 0:return r?function(i){return pe[i]}:function(i){return G[i]};case 1:return r?function(i){return Ee[i>>1]}:function(i){return Be[i>>1]};case 2:return r?function(i){return w[i>>2]}:function(i){return O[i>>2]};default:throw new TypeError(\"Unknown integer type: \"+e)}}var se=(e,t,r,i)=>{if(!(0<i))return 0;var o=r;i=r+i-1;for(var s=0;s<e.length;++s){var l=e.charCodeAt(s);if(55296<=l&&57343>=l){var f=e.charCodeAt(++s);l=65536+((l&1023)<<10)|f&1023}if(127>=l){if(r>=i)break;t[r++]=l}else{if(2047>=l){if(r+1>=i)break;t[r++]=192|l>>6}else{if(65535>=l){if(r+2>=i)break;t[r++]=224|l>>12}else{if(r+3>=i)break;t[r++]=240|l>>18,t[r++]=128|l>>12&63}t[r++]=128|l>>6&63}t[r++]=128|l&63}}return t[r]=0,r-o},le=e=>{for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);127>=i?t++:2047>=i?t+=2:55296<=i&&57343>=i?(t+=4,++r):t+=3}return t},Ar=typeof TextDecoder<\"u\"?new TextDecoder(\"utf-16le\"):void 0,pn=(e,t)=>{for(var r=e>>1,i=r+t/2;!(r>=i)&&Be[r];)++r;if(r<<=1,32<r-e&&Ar)return Ar.decode(G.subarray(e,r));for(r=\"\",i=0;!(i>=t/2);++i){var o=Ee[e+2*i>>1];if(o==0)break;r+=String.fromCharCode(o)}return r},yn=(e,t,r)=>{if(r===void 0&&(r=2147483647),2>r)return 0;r-=2;var i=t;r=r<2*e.length?r/2:e.length;for(var o=0;o<r;++o)Ee[t>>1]=e.charCodeAt(o),t+=2;return Ee[t>>1]=0,t-i},vn=e=>2*e.length,mn=(e,t)=>{for(var r=0,i=\"\";!(r>=t/4);){var o=w[e+4*r>>2];if(o==0)break;++r,65536<=o?(o-=65536,i+=String.fromCharCode(55296|o>>10,56320|o&1023)):i+=String.fromCharCode(o)}return i},_n=(e,t,r)=>{if(r===void 0&&(r=2147483647),4>r)return 0;var i=t;r=i+r-4;for(var o=0;o<e.length;++o){var s=e.charCodeAt(o);if(55296<=s&&57343>=s){var l=e.charCodeAt(++o);s=65536+((s&1023)<<10)|l&1023}if(w[t>>2]=s,t+=4,t+4>r)break}return w[t>>2]=0,t-i},gn=e=>{for(var t=0,r=0;r<e.length;++r){var i=e.charCodeAt(r);55296<=i&&57343>=i&&++r,t+=4}return t},Pn={};function ct(e){var t=Pn[e];return t===void 0?S(e):t}var dt=[];function Er(){function e(t){t.$$$embind_global$$$=t;var r=typeof $$$embind_global$$$==\"object\"&&t.$$$embind_global$$$==t;return r||delete t.$$$embind_global$$$,r}if(typeof globalThis==\"object\")return globalThis;if(typeof $$$embind_global$$$==\"object\"||(typeof global==\"object\"&&e(global)?$$$embind_global$$$=global:typeof self==\"object\"&&e(self)&&($$$embind_global$$$=self),typeof $$$embind_global$$$==\"object\"))return $$$embind_global$$$;throw Error(\"unable to get global object.\")}function Cn(e){var t=dt.length;return dt.push(e),t}function An(e,t){for(var r=Array(e),i=0;i<e;++i)r[i]=Ke(O[t+4*i>>2],\"parameter \"+i);return r}var wr=[];function En(e){var t=Array(e+1);return function(r,i,o){t[0]=r;for(var s=0;s<e;++s){var l=Ke(O[i+4*s>>2],\"parameter \"+s);t[s+1]=l.readValueFromPointer(o),o+=l.argPackAdvance}return r=new(r.bind.apply(r,t)),te(r)}}var Tr={};function wn(e){var t=e.getExtension(\"ANGLE_instanced_arrays\");t&&(e.vertexAttribDivisor=function(r,i){t.vertexAttribDivisorANGLE(r,i)},e.drawArraysInstanced=function(r,i,o,s){t.drawArraysInstancedANGLE(r,i,o,s)},e.drawElementsInstanced=function(r,i,o,s,l){t.drawElementsInstancedANGLE(r,i,o,s,l)})}function Tn(e){var t=e.getExtension(\"OES_vertex_array_object\");t&&(e.createVertexArray=function(){return t.createVertexArrayOES()},e.deleteVertexArray=function(r){t.deleteVertexArrayOES(r)},e.bindVertexArray=function(r){t.bindVertexArrayOES(r)},e.isVertexArray=function(r){return t.isVertexArrayOES(r)})}function Fn(e){var t=e.getExtension(\"WEBGL_draw_buffers\");t&&(e.drawBuffers=function(r,i){t.drawBuffersWEBGL(r,i)})}var Fr=1,ht=[],fe=[],pt=[],Je=[],re=[],ce=[],yt=[],me=[],Ie=[],De=[],Mr={},Rr={},xr=4;function j(e){vt||(vt=e)}function ke(e){for(var t=Fr++,r=e.length;r<t;r++)e[r]=null;return t}function Mn(e,t){e.be||(e.be=e.getContext,e.getContext=function(i,o){return o=e.be(i,o),i==\"webgl\"==o instanceof WebGLRenderingContext?o:null});var r=1<t.majorVersion?e.getContext(\"webgl2\",t):e.getContext(\"webgl\",t);return r?Rn(r,t):0}function Rn(e,t){var r=ke(me),i={handle:r,attributes:t,version:t.majorVersion,Qd:e};return e.canvas&&(e.canvas.Ke=i),me[r]=i,(typeof t.Ue>\"u\"||t.Ue)&&xn(i),r}function Ir(e){return B=me[e],m.qf=p=B&&B.Qd,!(e&&!p)}function xn(e){if(e||(e=B),!e.bf){e.bf=!0;var t=e.Qd;wn(t),Tn(t),Fn(t),t.ze=t.getExtension(\"WEBGL_draw_instanced_base_vertex_base_instance\"),t.De=t.getExtension(\"WEBGL_multi_draw_instanced_base_vertex_base_instance\"),2<=e.version&&(t.Ae=t.getExtension(\"EXT_disjoint_timer_query_webgl2\")),(2>e.version||!t.Ae)&&(t.Ae=t.getExtension(\"EXT_disjoint_timer_query\")),t.rf=t.getExtension(\"WEBGL_multi_draw\"),(t.getSupportedExtensions()||[]).forEach(function(r){r.includes(\"lose_context\")||r.includes(\"debug\")||t.getExtension(r)})}}var B,vt,jt={},Dr=()=>{if(!Wt){var e={USER:\"web_user\",LOGNAME:\"web_user\",PATH:\"/\",PWD:\"/\",HOME:\"/home/<USER>",LANG:(typeof navigator==\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\",_:wt||\"./this.program\"},t;for(t in jt)jt[t]===void 0?delete e[t]:e[t]=jt[t];var r=[];for(t in e)r.push(`${t}=${e[t]}`);Wt=r}return Wt},Wt,In=[null,[],[]];function Br(e){p.bindVertexArray(yt[e])}function Gr(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2];p.deleteVertexArray(yt[i]),yt[i]=null}}var mt=[];function Lr(e,t,r,i){p.drawElements(e,t,r,i)}function He(e,t,r,i){for(var o=0;o<e;o++){var s=p[r](),l=s&&ke(i);s?(s.name=l,i[l]=s):j(1282),w[t+4*o>>2]=l}}function br(e,t){He(e,t,\"createVertexArray\",yt)}function kr(e,t,r){if(t){var i=void 0;switch(e){case 36346:i=1;break;case 36344:r!=0&&r!=1&&j(1280);return;case 34814:case 36345:i=0;break;case 34466:var o=p.getParameter(34467);i=o?o.length:0;break;case 33309:if(2>B.version){j(1282);return}i=2*(p.getSupportedExtensions()||[]).length;break;case 33307:case 33308:if(2>B.version){j(1280);return}i=e==33307?3:0}if(i===void 0)switch(o=p.getParameter(e),typeof o){case\"number\":i=o;break;case\"boolean\":i=o?1:0;break;case\"string\":j(1280);return;case\"object\":if(o===null)switch(e){case 34964:case 35725:case 34965:case 36006:case 36007:case 32873:case 34229:case 36662:case 36663:case 35053:case 35055:case 36010:case 35097:case 35869:case 32874:case 36389:case 35983:case 35368:case 34068:i=0;break;default:j(1280);return}else{if(o instanceof Float32Array||o instanceof Uint32Array||o instanceof Int32Array||o instanceof Array){for(e=0;e<o.length;++e)switch(r){case 0:w[t+4*e>>2]=o[e];break;case 2:R[t+4*e>>2]=o[e];break;case 4:pe[t+e>>0]=o[e]?1:0}return}try{i=o.name|0}catch(s){j(1280),Ae(\"GL_INVALID_ENUM in glGet\"+r+\"v: Unknown object returned from WebGL getParameter(\"+e+\")! (error: \"+s+\")\");return}}break;default:j(1280),Ae(\"GL_INVALID_ENUM in glGet\"+r+\"v: Native code calling glGet\"+r+\"v(\"+e+\") and it returns \"+o+\" of type \"+typeof o+\"!\");return}switch(r){case 1:r=i,O[t>>2]=r,O[t+4>>2]=(r-O[t>>2])/4294967296;break;case 0:w[t>>2]=i;break;case 2:R[t>>2]=i;break;case 4:pe[t>>0]=i?1:0}}else j(1281)}var Qe=e=>{var t=le(e)+1,r=Ct(t);return r&&se(e,G,r,t),r};function Hr(e){return e.slice(-1)==\"]\"&&e.lastIndexOf(\"[\")}function _t(e){return e-=5120,e==0?pe:e==1?G:e==2?Ee:e==4?w:e==6?R:e==5||e==28922||e==28520||e==30779||e==30782?O:Be}function $t(e,t,r,i,o){e=_t(e);var s=31-Math.clz32(e.BYTES_PER_ELEMENT),l=xr;return e.subarray(o>>s,o+i*(r*({5:3,6:4,8:2,29502:3,29504:4,26917:2,26918:2,29846:3,29847:4}[t-6402]||1)*(1<<s)+l-1&-l)>>s)}function H(e){var t=p.Re;if(t){var r=t.fe[e];return typeof r==\"number\"&&(t.fe[e]=r=p.getUniformLocation(t,t.Ge[e]+(0<r?\"[\"+r+\"]\":\"\"))),r}j(1282)}var Te=[],Ze=[],gt=e=>e%4===0&&(e%100!==0||e%400===0),Ur=[31,29,31,30,31,30,31,31,30,31,30,31],Or=[31,28,31,30,31,30,31,31,30,31,30,31];function Dn(e){var t=Array(le(e)+1);return se(e,t,0,t.length),t}var Bn=(e,t,r,i)=>{function o(v,M,x){for(v=typeof v==\"number\"?v.toString():v||\"\";v.length<M;)v=x[0]+v;return v}function s(v,M){return o(v,M,\"0\")}function l(v,M){function x(J){return 0>J?-1:0<J?1:0}var W;return(W=x(v.getFullYear()-M.getFullYear()))===0&&(W=x(v.getMonth()-M.getMonth()))===0&&(W=x(v.getDate()-M.getDate())),W}function f(v){switch(v.getDay()){case 0:return new Date(v.getFullYear()-1,11,29);case 1:return v;case 2:return new Date(v.getFullYear(),0,3);case 3:return new Date(v.getFullYear(),0,2);case 4:return new Date(v.getFullYear(),0,1);case 5:return new Date(v.getFullYear()-1,11,31);case 6:return new Date(v.getFullYear()-1,11,30)}}function h(v){var M=v.Vd;for(v=new Date(new Date(v.Wd+1900,0,1).getTime());0<M;){var x=v.getMonth(),W=(gt(v.getFullYear())?Ur:Or)[x];if(M>W-v.getDate())M-=W-v.getDate()+1,v.setDate(1),11>x?v.setMonth(x+1):(v.setMonth(0),v.setFullYear(v.getFullYear()+1));else{v.setDate(v.getDate()+M);break}}return x=new Date(v.getFullYear()+1,0,4),M=f(new Date(v.getFullYear(),0,4)),x=f(x),0>=l(M,v)?0>=l(x,v)?v.getFullYear()+1:v.getFullYear():v.getFullYear()-1}var g=w[i+40>>2];i={nf:w[i>>2],mf:w[i+4>>2],pe:w[i+8>>2],we:w[i+12>>2],qe:w[i+16>>2],Wd:w[i+20>>2],Rd:w[i+24>>2],Vd:w[i+28>>2],uf:w[i+32>>2],lf:w[i+36>>2],pf:g&&g?Re(G,g):\"\"},r=r?Re(G,r):\"\",g={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var P in g)r=r.replace(new RegExp(P,\"g\"),g[P]);var E=\"Sunday Monday Tuesday Wednesday Thursday Friday Saturday\".split(\" \"),T=\"January February March April May June July August September October November December\".split(\" \");g={\"%a\":v=>E[v.Rd].substring(0,3),\"%A\":v=>E[v.Rd],\"%b\":v=>T[v.qe].substring(0,3),\"%B\":v=>T[v.qe],\"%C\":v=>s((v.Wd+1900)/100|0,2),\"%d\":v=>s(v.we,2),\"%e\":v=>o(v.we,2,\" \"),\"%g\":v=>h(v).toString().substring(2),\"%G\":v=>h(v),\"%H\":v=>s(v.pe,2),\"%I\":v=>(v=v.pe,v==0?v=12:12<v&&(v-=12),s(v,2)),\"%j\":v=>{for(var M=0,x=0;x<=v.qe-1;M+=(gt(v.Wd+1900)?Ur:Or)[x++]);return s(v.we+M,3)},\"%m\":v=>s(v.qe+1,2),\"%M\":v=>s(v.mf,2),\"%n\":()=>`\n`,\"%p\":v=>0<=v.pe&&12>v.pe?\"AM\":\"PM\",\"%S\":v=>s(v.nf,2),\"%t\":()=>\"\t\",\"%u\":v=>v.Rd||7,\"%U\":v=>s(Math.floor((v.Vd+7-v.Rd)/7),2),\"%V\":v=>{var M=Math.floor((v.Vd+7-(v.Rd+6)%7)/7);if(2>=(v.Rd+371-v.Vd-2)%7&&M++,M)M==53&&(x=(v.Rd+371-v.Vd)%7,x==4||x==3&&gt(v.Wd)||(M=1));else{M=52;var x=(v.Rd+7-v.Vd-1)%7;(x==4||x==5&&gt(v.Wd%400-1))&&M++}return s(M,2)},\"%w\":v=>v.Rd,\"%W\":v=>s(Math.floor((v.Vd+7-(v.Rd+6)%7)/7),2),\"%y\":v=>(v.Wd+1900).toString().substring(2),\"%Y\":v=>v.Wd+1900,\"%z\":v=>{v=v.lf;var M=0<=v;return v=Math.abs(v)/60,(M?\"+\":\"-\")+String(\"0000\"+(v/60*100+v%60)).slice(-4)},\"%Z\":v=>v.pf,\"%%\":()=>\"%\"},r=r.replace(/%%/g,\"\\0\\0\");for(P in g)r.includes(P)&&(r=r.replace(new RegExp(P,\"g\"),g[P](i)));return r=r.replace(/\\0\\0/g,\"%\"),P=Dn(r),P.length>t?0:(pe.set(P,e),P.length-1)};cr=m.InternalError=class extends Error{constructor(e){super(e),this.name=\"InternalError\"}};for(var jr=Array(256),Pt=0;256>Pt;++Pt)jr[Pt]=String.fromCharCode(Pt);dr=jr,be=m.BindingError=class extends Error{constructor(e){super(e),this.name=\"BindingError\"}},we.prototype.isAliasOf=function(e){if(!(this instanceof we&&e instanceof we))return!1;var t=this.Dd.Jd.Gd,r=this.Dd.Hd,i=e.Dd.Jd.Gd;for(e=e.Dd.Hd;t.Md;)r=t.ge(r),t=t.Md;for(;i.Md;)e=i.ge(e),i=i.Md;return t===i&&r===e},we.prototype.clone=function(){if(this.Dd.Hd||Dt(this),this.Dd.ee)return this.Dd.count.value+=1,this;var e=Ye,t=Object,r=t.create,i=Object.getPrototypeOf(this),o=this.Dd;return e=e(r.call(t,i,{Dd:{value:{count:o.count,$d:o.$d,ee:o.ee,Hd:o.Hd,Jd:o.Jd,Ld:o.Ld,Pd:o.Pd}}})),e.Dd.count.value+=1,e.Dd.$d=!1,e},we.prototype.delete=function(){this.Dd.Hd||Dt(this),this.Dd.$d&&!this.Dd.ee&&k(\"Object already scheduled for deletion\"),hr(this),pr(this.Dd),this.Dd.ee||(this.Dd.Ld=void 0,this.Dd.Hd=void 0)},we.prototype.isDeleted=function(){return!this.Dd.Hd},we.prototype.deleteLater=function(){return this.Dd.Hd||Dt(this),this.Dd.$d&&!this.Dd.ee&&k(\"Object already scheduled for deletion\"),Se.push(this),Se.length===1&&Ve&&Ve(Gt),this.Dd.$d=!0,this},m.getInheritedInstanceCount=function(){return Object.keys(Ne).length},m.getLiveInheritedInstances=function(){var e=[],t;for(t in Ne)Ne.hasOwnProperty(t)&&e.push(Ne[t]);return e},m.flushPendingDeletes=Gt,m.setDelayFunction=function(e){Ve=e,Se.length&&Ve&&Ve(Gt)},ve.prototype.Xe=function(e){return this.Ee&&(e=this.Ee(e)),e},ve.prototype.ye=function(e){this.Td&&this.Td(e)},ve.prototype.argPackAdvance=8,ve.prototype.readValueFromPointer=$e,ve.prototype.deleteObject=function(e){e!==null&&e.delete()},ve.prototype.fromWireType=function(e){function t(){return this.le?st(this.Gd.ae,{Jd:this.df,Hd:r,Pd:this,Ld:e}):st(this.Gd.ae,{Jd:this,Hd:e})}var r=this.Xe(e);if(!r)return this.ye(e),null;var i=on(this.Gd,r);if(i!==void 0)return i.Dd.count.value===0?(i.Dd.Hd=r,i.Dd.Ld=e,i.clone()):(i=i.clone(),this.ye(e),i);if(i=this.Gd.We(r),i=vr[i],!i)return t.call(this);i=this.ke?i.Qe:i.pointerType;var o=yr(r,this.Gd,i.Gd);return o===null?t.call(this):this.le?st(i.Gd.ae,{Jd:i,Hd:o,Pd:this,Ld:e}):st(i.Gd.ae,{Jd:i,Hd:o})},gr=m.UnboundTypeError=function(e,t){var r=Lt(t,function(i){this.name=t,this.message=i,i=Error(i).stack,i!==void 0&&(this.stack=this.toString()+`\n`+i.replace(/^Error(:[^\\n]*)?\\n/,\"\"))});return r.prototype=Object.create(e.prototype),r.prototype.constructor=r,r.prototype.toString=function(){return this.message===void 0?this.name:`${this.name}: ${this.message}`},r}(Error,\"UnboundTypeError\"),Object.assign(Cr.prototype,{get(e){return this.Sd[e]},has(e){return this.Sd[e]!==void 0},Ie(e){var t=this.Ce.pop()||this.Sd.length;return this.Sd[t]=e,t},Je(e){this.Sd[e]=void 0,this.Ce.push(e)}}),q.Sd.push({value:void 0},{value:null},{value:!0},{value:!1}),q.be=q.Sd.length,m.count_emval_handles=function(){for(var e=0,t=q.be;t<q.Sd.length;++t)q.Sd[t]!==void 0&&++e;return e};for(var p,Z=0;32>Z;++Z)mt.push(Array(Z));var Gn=new Float32Array(288);for(Z=0;288>Z;++Z)Te[Z]=Gn.subarray(0,Z+1);var Ln=new Int32Array(288);for(Z=0;288>Z;++Z)Ze[Z]=Ln.subarray(0,Z+1);var bn={Q:function(){return 0},Ab:()=>{},Cb:function(){return 0},xb:()=>{},yb:()=>{},R:function(){},zb:()=>{},v:function(e){var t=it[e];delete it[e];var r=t.ve,i=t.Td,o=t.Be,s=o.map(l=>l.$e).concat(o.map(l=>l.hf));ue([e],s,l=>{var f={};return o.forEach((h,g)=>{var P=l[g],E=h.Ye,T=h.Ze,v=l[g+o.length],M=h.gf,x=h.jf;f[h.Ve]={read:W=>P.fromWireType(E(T,W)),write:(W,J)=>{var Y=[];M(x,W,v.toWireType(Y,J)),It(Y)}}}),[{name:t.name,fromWireType:function(h){var g={},P;for(P in f)g[P]=f[P].read(h);return i(h),g},toWireType:function(h,g){for(var P in f)if(!(P in g))throw new TypeError(`Missing field: \"${P}\"`);var E=r();for(P in f)f[P].write(E,g[P]);return h!==null&&h.push(i,E),E},argPackAdvance:8,readValueFromPointer:$e,Od:i}]})},pb:function(){},Gb:function(e,t,r,i,o){var s=ut(r);t=S(t),ye(e,{name:t,fromWireType:function(l){return!!l},toWireType:function(l,f){return f?i:o},argPackAdvance:8,readValueFromPointer:function(l){if(r===1)var f=pe;else if(r===2)f=Ee;else if(r===4)f=w;else throw new TypeError(\"Unknown boolean type size: \"+t);return this.fromWireType(f[l>>s])},Od:null})},k:function(e,t,r,i,o,s,l,f,h,g,P,E,T){P=S(P),s=K(o,s),f&&(f=K(l,f)),g&&(g=K(h,g)),T=K(E,T);var v=mr(P);kt(v,function(){Xe(`Cannot construct ${P} due to unbound types`,[i])}),ue([e,t,r],i?[i]:[],function(M){if(M=M[0],i)var x=M.Gd,W=x.ae;else W=we.prototype;M=Lt(v,function(){if(Object.getPrototypeOf(this)!==J)throw new be(\"Use 'new' to construct \"+P);if(Y.Ud===void 0)throw new be(P+\" has no accessible constructor\");var Ue=Y.Ud[arguments.length];if(Ue===void 0)throw new be(`Tried to invoke ctor of ${P} with invalid number of parameters (${arguments.length}) - expected (${Object.keys(Y.Ud).toString()}) parameters instead!`);return Ue.apply(this,arguments)});var J=Object.create(W,{constructor:{value:M}});M.prototype=J;var Y=new an(P,M,J,T,x,s,f,g);Y.Md&&(Y.Md.he===void 0&&(Y.Md.he=[]),Y.Md.he.push(Y)),x=new ve(P,Y,!0,!1,!1),W=new ve(P+\"*\",Y,!1,!1,!1);var _e=new ve(P+\" const*\",Y,!1,!0,!1);return vr[e]={pointerType:W,Qe:_e},_r(v,M),[x,W,_e]})},f:function(e,t,r,i,o,s,l){var f=ft(r,i);t=S(t),s=K(o,s),ue([],[e],function(h){function g(){Xe(`Cannot call ${P} due to unbound types`,f)}h=h[0];var P=`${h.name}.${t}`;t.startsWith(\"@@\")&&(t=Symbol[t.substring(2)]);var E=h.Gd.constructor;return E[t]===void 0?(g.Yd=r-1,E[t]=g):(bt(E,t,P),E[t].Kd[r-1]=g),ue([],f,function(T){if(T=[T[0],null].concat(T.slice(1)),T=lt(P,T,null,s,l),E[t].Kd===void 0?(T.Yd=r-1,E[t]=T):E[t].Kd[r-1]=T,h.Gd.he)for(let v of h.Gd.he)v.constructor.hasOwnProperty(t)||(v.constructor[t]=T);return[]}),[]})},t:function(e,t,r,i,o,s){var l=ft(t,r);o=K(i,o),ue([],[e],function(f){f=f[0];var h=`constructor ${f.name}`;if(f.Gd.Ud===void 0&&(f.Gd.Ud=[]),f.Gd.Ud[t-1]!==void 0)throw new be(`Cannot register multiple constructors with identical number of parameters (${t-1}) for class '${f.name}'! Overload resolution is currently only performed using the parameter count, not actual type info!`);return f.Gd.Ud[t-1]=()=>{Xe(`Cannot construct ${f.name} due to unbound types`,l)},ue([],l,function(g){return g.splice(1,0,null),f.Gd.Ud[t-1]=lt(h,g,null,o,s),[]}),[]})},b:function(e,t,r,i,o,s,l,f){var h=ft(r,i);t=S(t),s=K(o,s),ue([],[e],function(g){function P(){Xe(`Cannot call ${E} due to unbound types`,h)}g=g[0];var E=`${g.name}.${t}`;t.startsWith(\"@@\")&&(t=Symbol[t.substring(2)]),f&&g.Gd.ef.push(t);var T=g.Gd.ae,v=T[t];return v===void 0||v.Kd===void 0&&v.className!==g.name&&v.Yd===r-2?(P.Yd=r-2,P.className=g.name,T[t]=P):(bt(T,t,E),T[t].Kd[r-2]=P),ue([],h,function(M){return M=lt(E,M,g,s,l),T[t].Kd===void 0?(M.Yd=r-2,T[t]=M):T[t].Kd[r-2]=M,[]}),[]})},o:function(e,t,r){e=S(e),ue([],[t],function(i){return i=i[0],m[e]=i.fromWireType(r),[]})},Fb:function(e,t){t=S(t),ye(e,{name:t,fromWireType:function(r){var i=ee(r);return Ut(r),i},toWireType:function(r,i){return te(i)},argPackAdvance:8,readValueFromPointer:$e,Od:null})},j:function(e,t,r,i){function o(){}r=ut(r),t=S(t),o.values={},ye(e,{name:t,constructor:o,fromWireType:function(s){return this.constructor.values[s]},toWireType:function(s,l){return l.value},argPackAdvance:8,readValueFromPointer:cn(t,r,i),Od:null}),kt(t,o)},c:function(e,t,r){var i=Ke(e,\"enum\");t=S(t),e=i.constructor,i=Object.create(i.constructor.prototype,{value:{value:r},constructor:{value:Lt(`${i.name}_${t}`,function(){})}}),e.values[r]=i,e[t]=i},T:function(e,t,r){r=ut(r),t=S(t),ye(e,{name:t,fromWireType:function(i){return i},toWireType:function(i,o){return o},argPackAdvance:8,readValueFromPointer:dn(t,r),Od:null})},r:function(e,t,r,i,o,s){var l=ft(t,r);e=S(e),o=K(i,o),kt(e,function(){Xe(`Cannot call ${e} due to unbound types`,l)},t-1),ue([],l,function(f){return f=[f[0],null].concat(f.slice(1)),_r(e,lt(e,f,null,o,s),t-1),[]})},x:function(e,t,r,i,o){t=S(t),o===-1&&(o=4294967295),o=ut(r);var s=f=>f;if(i===0){var l=32-8*r;s=f=>f<<l>>>l}r=t.includes(\"unsigned\")?function(f,h){return h>>>0}:function(f,h){return h},ye(e,{name:t,fromWireType:s,toWireType:r,argPackAdvance:8,readValueFromPointer:hn(t,o,i!==0),Od:null})},n:function(e,t,r){function i(s){s>>=2;var l=O;return new o(l.buffer,l[s+1],l[s])}var o=[Int8Array,Uint8Array,Int16Array,Uint16Array,Int32Array,Uint32Array,Float32Array,Float64Array][t];r=S(r),ye(e,{name:r,fromWireType:i,argPackAdvance:8,readValueFromPointer:i},{af:!0})},m:function(e,t,r,i,o,s,l,f,h,g,P,E){r=S(r),s=K(o,s),f=K(l,f),g=K(h,g),E=K(P,E),ue([e],[t],function(T){return T=T[0],[new ve(r,T.Gd,!1,!1,!0,T,i,s,f,g,E)]})},S:function(e,t){t=S(t);var r=t===\"std::string\";ye(e,{name:t,fromWireType:function(i){var o=O[i>>2],s=i+4;if(r)for(var l=s,f=0;f<=o;++f){var h=s+f;if(f==o||G[h]==0){if(l=l?Re(G,l,h-l):\"\",g===void 0)var g=l;else g+=String.fromCharCode(0),g+=l;l=h+1}}else{for(g=Array(o),f=0;f<o;++f)g[f]=String.fromCharCode(G[s+f]);g=g.join(\"\")}return Pe(i),g},toWireType:function(i,o){o instanceof ArrayBuffer&&(o=new Uint8Array(o));var s=typeof o==\"string\";s||o instanceof Uint8Array||o instanceof Uint8ClampedArray||o instanceof Int8Array||k(\"Cannot pass non-string to std::string\");var l=r&&s?le(o):o.length,f=Ct(4+l+1),h=f+4;if(O[f>>2]=l,r&&s)se(o,G,h,l+1);else if(s)for(s=0;s<l;++s){var g=o.charCodeAt(s);255<g&&(Pe(h),k(\"String has UTF-16 code units that do not fit in 8 bits\")),G[h+s]=g}else for(s=0;s<l;++s)G[h+s]=o[s];return i!==null&&i.push(Pe,f),f},argPackAdvance:8,readValueFromPointer:$e,Od:function(i){Pe(i)}})},K:function(e,t,r){if(r=S(r),t===2)var i=pn,o=yn,s=vn,l=()=>Be,f=1;else t===4&&(i=mn,o=_n,s=gn,l=()=>O,f=2);ye(e,{name:r,fromWireType:function(h){for(var g=O[h>>2],P=l(),E,T=h+4,v=0;v<=g;++v){var M=h+4+v*t;(v==g||P[M>>f]==0)&&(T=i(T,M-T),E===void 0?E=T:(E+=String.fromCharCode(0),E+=T),T=M+t)}return Pe(h),E},toWireType:function(h,g){typeof g!=\"string\"&&k(`Cannot pass non-string to C++ string type ${r}`);var P=s(g),E=Ct(4+P+t);return O[E>>2]=P>>f,o(g,E+4,P+t),h!==null&&h.push(Pe,E),E},argPackAdvance:8,readValueFromPointer:$e,Od:function(h){Pe(h)}})},w:function(e,t,r,i,o,s){it[e]={name:S(t),ve:K(r,i),Td:K(o,s),Be:[]}},e:function(e,t,r,i,o,s,l,f,h,g){it[e].Be.push({Ve:S(t),$e:r,Ye:K(i,o),Ze:s,hf:l,gf:K(f,h),jf:g})},Hb:function(e,t){t=S(t),ye(e,{cf:!0,name:t,argPackAdvance:0,fromWireType:function(){},toWireType:function(){}})},Eb:()=>!0,tb:()=>{throw 1/0},y:function(e,t,r){e=ee(e),t=Ke(t,\"emval::as\");var i=[],o=te(i);return O[r>>2]=o,t.toWireType(i,e)},Y:function(e,t,r,i,o){e=dt[e],t=ee(t),r=ct(r);var s=[];return O[i>>2]=te(s),e(t,r,s,o)},q:function(e,t,r,i){e=dt[e],t=ee(t),r=ct(r),e(t,r,null,i)},d:Ut,H:function(e){return e===0?te(Er()):(e=ct(e),te(Er()[e]))},p:function(e,t){var r=An(e,t),i=r[0];t=i.name+\"_$\"+r.slice(1).map(function(l){return l.name}).join(\"_\")+\"$\";var o=wr[t];if(o!==void 0)return o;var s=Array(e-1);return o=Cn((l,f,h,g)=>{for(var P=0,E=0;E<e-1;++E)s[E]=r[E+1].readValueFromPointer(g+P),P+=r[E+1].argPackAdvance;for(l=l[f].apply(l,s),E=0;E<e-1;++E)r[E+1].Se&&r[E+1].Se(s[E]);if(!i.cf)return i.toWireType(h,l)}),wr[t]=o},s:function(e,t){return e=ee(e),t=ee(t),te(e[t])},l:function(e){4<e&&(q.get(e).Fe+=1)},G:function(e,t,r,i){e=ee(e);var o=Tr[t];return o||(o=En(t),Tr[t]=o),o(e,r,i)},C:function(){return te([])},g:function(e){return te(ct(e))},z:function(){return te({})},jb:function(e){return e=ee(e),!e},u:function(e){var t=ee(e);It(t),Ut(e)},i:function(e,t,r){e=ee(e),t=ee(t),r=ee(r),e[t]=r},h:function(e,t){return e=Ke(e,\"_emval_take_value\"),e=e.readValueFromPointer(t),te(e)},mb:function(){return-52},nb:function(){},a:()=>{Rt(\"\")},Db:()=>performance.now(),ub:e=>{var t=G.length;if(e>>>=0,2147483648<e)return!1;for(var r=1;4>=r;r*=2){var i=t*(1+.2/r);i=Math.min(i,e+100663296);var o=Math;i=Math.max(e,i);e:{o=o.min.call(o,2147483648,i+(65536-i%65536)%65536)-nt.buffer.byteLength+65535>>>16;try{nt.grow(o),rr();var s=1;break e}catch{}s=void 0}if(s)return!0}return!1},kb:function(){return B?B.handle:0},vb:(e,t)=>{var r=0;return Dr().forEach(function(i,o){var s=t+r;for(o=O[e+4*o>>2]=s,s=0;s<i.length;++s)pe[o++>>0]=i.charCodeAt(s);pe[o>>0]=0,r+=i.length+1}),0},wb:(e,t)=>{var r=Dr();O[e>>2]=r.length;var i=0;return r.forEach(function(o){i+=o.length+1}),O[t>>2]=i,0},J:()=>52,lb:function(){return 52},Bb:()=>52,ob:function(){return 70},P:(e,t,r,i)=>{for(var o=0,s=0;s<r;s++){var l=O[t>>2],f=O[t+4>>2];t+=8;for(var h=0;h<f;h++){var g=G[l+h],P=In[e];g===0||g===10?((e===1?qr:Ae)(Re(P,0)),P.length=0):P.push(g)}o+=f}return O[i>>2]=o,0},$:function(e){p.activeTexture(e)},aa:function(e,t){p.attachShader(fe[e],ce[t])},ba:function(e,t,r){p.bindAttribLocation(fe[e],t,r?Re(G,r):\"\")},ca:function(e,t){e==35051?p.se=t:e==35052&&(p.Zd=t),p.bindBuffer(e,ht[t])},_:function(e,t){p.bindFramebuffer(e,pt[t])},ac:function(e,t){p.bindRenderbuffer(e,Je[t])},Mb:function(e,t){p.bindSampler(e,Ie[t])},da:function(e,t){p.bindTexture(e,re[t])},uc:Br,xc:Br,ea:function(e,t,r,i){p.blendColor(e,t,r,i)},fa:function(e){p.blendEquation(e)},ga:function(e,t){p.blendFunc(e,t)},Wb:function(e,t,r,i,o,s,l,f,h,g){p.blitFramebuffer(e,t,r,i,o,s,l,f,h,g)},ha:function(e,t,r,i){2<=B.version?r&&t?p.bufferData(e,G,i,r,t):p.bufferData(e,t,i):p.bufferData(e,r?G.subarray(r,r+t):t,i)},ia:function(e,t,r,i){2<=B.version?r&&p.bufferSubData(e,t,G,i,r):p.bufferSubData(e,t,G.subarray(i,i+r))},bc:function(e){return p.checkFramebufferStatus(e)},N:function(e){p.clear(e)},Z:function(e,t,r,i){p.clearColor(e,t,r,i)},O:function(e){p.clearStencil(e)},rb:function(e,t,r,i){return p.clientWaitSync(De[e],t,(r>>>0)+4294967296*i)},ja:function(e,t,r,i){p.colorMask(!!e,!!t,!!r,!!i)},ka:function(e){p.compileShader(ce[e])},la:function(e,t,r,i,o,s,l,f){2<=B.version?p.Zd||!l?p.compressedTexImage2D(e,t,r,i,o,s,l,f):p.compressedTexImage2D(e,t,r,i,o,s,G,f,l):p.compressedTexImage2D(e,t,r,i,o,s,f?G.subarray(f,f+l):null)},ma:function(e,t,r,i,o,s,l,f,h){2<=B.version?p.Zd||!f?p.compressedTexSubImage2D(e,t,r,i,o,s,l,f,h):p.compressedTexSubImage2D(e,t,r,i,o,s,l,G,h,f):p.compressedTexSubImage2D(e,t,r,i,o,s,l,h?G.subarray(h,h+f):null)},Ub:function(e,t,r,i,o){p.copyBufferSubData(e,t,r,i,o)},na:function(e,t,r,i,o,s,l,f){p.copyTexSubImage2D(e,t,r,i,o,s,l,f)},oa:function(){var e=ke(fe),t=p.createProgram();return t.name=e,t.oe=t.me=t.ne=0,t.xe=1,fe[e]=t,e},pa:function(e){var t=ke(ce);return ce[t]=p.createShader(e),t},qa:function(e){p.cullFace(e)},ra:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=ht[i];o&&(p.deleteBuffer(o),o.name=0,ht[i]=null,i==p.se&&(p.se=0),i==p.Zd&&(p.Zd=0))}},cc:function(e,t){for(var r=0;r<e;++r){var i=w[t+4*r>>2],o=pt[i];o&&(p.deleteFramebuffer(o),o.name=0,pt[i]=null)}},sa:function(e){if(e){var t=fe[e];t?(p.deleteProgram(t),t.name=0,fe[e]=null):j(1281)}},dc:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=Je[i];o&&(p.deleteRenderbuffer(o),o.name=0,Je[i]=null)}},Nb:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=Ie[i];o&&(p.deleteSampler(o),o.name=0,Ie[i]=null)}},ta:function(e){if(e){var t=ce[e];t?(p.deleteShader(t),ce[e]=null):j(1281)}},Vb:function(e){if(e){var t=De[e];t?(p.deleteSync(t),t.name=0,De[e]=null):j(1281)}},ua:function(e,t){for(var r=0;r<e;r++){var i=w[t+4*r>>2],o=re[i];o&&(p.deleteTexture(o),o.name=0,re[i]=null)}},vc:Gr,yc:Gr,va:function(e){p.depthMask(!!e)},wa:function(e){p.disable(e)},xa:function(e){p.disableVertexAttribArray(e)},ya:function(e,t,r){p.drawArrays(e,t,r)},sc:function(e,t,r,i){p.drawArraysInstanced(e,t,r,i)},qc:function(e,t,r,i,o){p.ze.drawArraysInstancedBaseInstanceWEBGL(e,t,r,i,o)},oc:function(e,t){for(var r=mt[e],i=0;i<e;i++)r[i]=w[t+4*i>>2];p.drawBuffers(r)},za:Lr,tc:function(e,t,r,i,o){p.drawElementsInstanced(e,t,r,i,o)},rc:function(e,t,r,i,o,s,l){p.ze.drawElementsInstancedBaseVertexBaseInstanceWEBGL(e,t,r,i,o,s,l)},ic:function(e,t,r,i,o,s){Lr(e,i,o,s)},Aa:function(e){p.enable(e)},Ba:function(e){p.enableVertexAttribArray(e)},Sb:function(e,t){return(e=p.fenceSync(e,t))?(t=ke(De),e.name=t,De[t]=e,t):0},Ca:function(){p.finish()},Da:function(){p.flush()},ec:function(e,t,r,i){p.framebufferRenderbuffer(e,t,r,Je[i])},fc:function(e,t,r,i,o){p.framebufferTexture2D(e,t,r,re[i],o)},Ea:function(e){p.frontFace(e)},Fa:function(e,t){He(e,t,\"createBuffer\",ht)},gc:function(e,t){He(e,t,\"createFramebuffer\",pt)},hc:function(e,t){He(e,t,\"createRenderbuffer\",Je)},Ob:function(e,t){He(e,t,\"createSampler\",Ie)},Ga:function(e,t){He(e,t,\"createTexture\",re)},wc:br,zc:br,Yb:function(e){p.generateMipmap(e)},Ha:function(e,t,r){r?w[r>>2]=p.getBufferParameter(e,t):j(1281)},Ia:function(){var e=p.getError()||vt;return vt=0,e},Ja:function(e,t){kr(e,t,2)},Zb:function(e,t,r,i){e=p.getFramebufferAttachmentParameter(e,t,r),(e instanceof WebGLRenderbuffer||e instanceof WebGLTexture)&&(e=e.name|0),w[i>>2]=e},I:function(e,t){kr(e,t,0)},Ka:function(e,t,r,i){e=p.getProgramInfoLog(fe[e]),e===null&&(e=\"(unknown error)\"),t=0<t&&i?se(e,G,i,t):0,r&&(w[r>>2]=t)},La:function(e,t,r){if(r)if(e>=Fr)j(1281);else if(e=fe[e],t==35716)e=p.getProgramInfoLog(e),e===null&&(e=\"(unknown error)\"),w[r>>2]=e.length+1;else if(t==35719){if(!e.oe)for(t=0;t<p.getProgramParameter(e,35718);++t)e.oe=Math.max(e.oe,p.getActiveUniform(e,t).name.length+1);w[r>>2]=e.oe}else if(t==35722){if(!e.me)for(t=0;t<p.getProgramParameter(e,35721);++t)e.me=Math.max(e.me,p.getActiveAttrib(e,t).name.length+1);w[r>>2]=e.me}else if(t==35381){if(!e.ne)for(t=0;t<p.getProgramParameter(e,35382);++t)e.ne=Math.max(e.ne,p.getActiveUniformBlockName(e,t).length+1);w[r>>2]=e.ne}else w[r>>2]=p.getProgramParameter(e,t);else j(1281)},_b:function(e,t,r){r?w[r>>2]=p.getRenderbufferParameter(e,t):j(1281)},Ma:function(e,t,r,i){e=p.getShaderInfoLog(ce[e]),e===null&&(e=\"(unknown error)\"),t=0<t&&i?se(e,G,i,t):0,r&&(w[r>>2]=t)},Jb:function(e,t,r,i){e=p.getShaderPrecisionFormat(e,t),w[r>>2]=e.rangeMin,w[r+4>>2]=e.rangeMax,w[i>>2]=e.precision},Na:function(e,t,r){r?t==35716?(e=p.getShaderInfoLog(ce[e]),e===null&&(e=\"(unknown error)\"),w[r>>2]=e?e.length+1:0):t==35720?(e=p.getShaderSource(ce[e]),w[r>>2]=e?e.length+1:0):w[r>>2]=p.getShaderParameter(ce[e],t):j(1281)},M:function(e){var t=Mr[e];if(!t){switch(e){case 7939:t=p.getSupportedExtensions()||[],t=t.concat(t.map(function(i){return\"GL_\"+i})),t=Qe(t.join(\" \"));break;case 7936:case 7937:case 37445:case 37446:(t=p.getParameter(e))||j(1280),t=t&&Qe(t);break;case 7938:t=p.getParameter(7938),t=2<=B.version?\"OpenGL ES 3.0 (\"+t+\")\":\"OpenGL ES 2.0 (\"+t+\")\",t=Qe(t);break;case 35724:t=p.getParameter(35724);var r=t.match(/^WebGL GLSL ES ([0-9]\\.[0-9][0-9]?)(?:$| .*)/);r!==null&&(r[1].length==3&&(r[1]+=\"0\"),t=\"OpenGL ES GLSL ES \"+r[1]+\" (\"+t+\")\"),t=Qe(t);break;default:j(1280)}Mr[e]=t}return t},ib:function(e,t){if(2>B.version)return j(1282),0;var r=Rr[e];if(r)return 0>t||t>=r.length?(j(1281),0):r[t];switch(e){case 7939:return r=p.getSupportedExtensions()||[],r=r.concat(r.map(function(i){return\"GL_\"+i})),r=r.map(function(i){return Qe(i)}),r=Rr[e]=r,0>t||t>=r.length?(j(1281),0):r[t];default:return j(1280),0}},Oa:function(e,t){if(t=t?Re(G,t):\"\",e=fe[e]){var r=e,i=r.fe,o=r.He,s;if(!i)for(r.fe=i={},r.Ge={},s=0;s<p.getProgramParameter(r,35718);++s){var l=p.getActiveUniform(r,s),f=l.name;l=l.size;var h=Hr(f);h=0<h?f.slice(0,h):f;var g=r.xe;for(r.xe+=l,o[h]=[l,g],f=0;f<l;++f)i[g]=f,r.Ge[g++]=h}if(r=e.fe,i=0,o=t,s=Hr(t),0<s&&(i=parseInt(t.slice(s+1))>>>0,o=t.slice(0,s)),(o=e.He[o])&&i<o[0]&&(i+=o[1],r[i]=r[i]||p.getUniformLocation(e,t)))return i}else j(1281);return-1},Kb:function(e,t,r){for(var i=mt[t],o=0;o<t;o++)i[o]=w[r+4*o>>2];p.invalidateFramebuffer(e,i)},Lb:function(e,t,r,i,o,s,l){for(var f=mt[t],h=0;h<t;h++)f[h]=w[r+4*h>>2];p.invalidateSubFramebuffer(e,f,i,o,s,l)},Tb:function(e){return p.isSync(De[e])},Pa:function(e){return(e=re[e])?p.isTexture(e):0},Qa:function(e){p.lineWidth(e)},Ra:function(e){e=fe[e],p.linkProgram(e),e.fe=0,e.He={}},mc:function(e,t,r,i,o,s){p.De.multiDrawArraysInstancedBaseInstanceWEBGL(e,w,t>>2,w,r>>2,w,i>>2,O,o>>2,s)},nc:function(e,t,r,i,o,s,l,f){p.De.multiDrawElementsInstancedBaseVertexBaseInstanceWEBGL(e,w,t>>2,r,w,i>>2,w,o>>2,w,s>>2,O,l>>2,f)},Sa:function(e,t){e==3317&&(xr=t),p.pixelStorei(e,t)},pc:function(e){p.readBuffer(e)},Ta:function(e,t,r,i,o,s,l){if(2<=B.version)if(p.se)p.readPixels(e,t,r,i,o,s,l);else{var f=_t(s);p.readPixels(e,t,r,i,o,s,f,l>>31-Math.clz32(f.BYTES_PER_ELEMENT))}else(l=$t(s,o,r,i,l))?p.readPixels(e,t,r,i,o,s,l):j(1280)},$b:function(e,t,r,i){p.renderbufferStorage(e,t,r,i)},Xb:function(e,t,r,i,o){p.renderbufferStorageMultisample(e,t,r,i,o)},Pb:function(e,t,r){p.samplerParameterf(Ie[e],t,r)},Qb:function(e,t,r){p.samplerParameteri(Ie[e],t,r)},Rb:function(e,t,r){p.samplerParameteri(Ie[e],t,w[r>>2])},Ua:function(e,t,r,i){p.scissor(e,t,r,i)},Va:function(e,t,r,i){for(var o=\"\",s=0;s<t;++s){var l=i?w[i+4*s>>2]:-1,f=w[r+4*s>>2];l=f?Re(G,f,0>l?void 0:l):\"\",o+=l}p.shaderSource(ce[e],o)},Wa:function(e,t,r){p.stencilFunc(e,t,r)},Xa:function(e,t,r,i){p.stencilFuncSeparate(e,t,r,i)},Ya:function(e){p.stencilMask(e)},Za:function(e,t){p.stencilMaskSeparate(e,t)},_a:function(e,t,r){p.stencilOp(e,t,r)},$a:function(e,t,r,i){p.stencilOpSeparate(e,t,r,i)},ab:function(e,t,r,i,o,s,l,f,h){if(2<=B.version)if(p.Zd)p.texImage2D(e,t,r,i,o,s,l,f,h);else if(h){var g=_t(f);p.texImage2D(e,t,r,i,o,s,l,f,g,h>>31-Math.clz32(g.BYTES_PER_ELEMENT))}else p.texImage2D(e,t,r,i,o,s,l,f,null);else p.texImage2D(e,t,r,i,o,s,l,f,h?$t(f,l,i,o,h):null)},bb:function(e,t,r){p.texParameterf(e,t,r)},cb:function(e,t,r){p.texParameterf(e,t,R[r>>2])},db:function(e,t,r){p.texParameteri(e,t,r)},eb:function(e,t,r){p.texParameteri(e,t,w[r>>2])},jc:function(e,t,r,i,o){p.texStorage2D(e,t,r,i,o)},fb:function(e,t,r,i,o,s,l,f,h){if(2<=B.version)if(p.Zd)p.texSubImage2D(e,t,r,i,o,s,l,f,h);else if(h){var g=_t(f);p.texSubImage2D(e,t,r,i,o,s,l,f,g,h>>31-Math.clz32(g.BYTES_PER_ELEMENT))}else p.texSubImage2D(e,t,r,i,o,s,l,f,null);else g=null,h&&(g=$t(f,l,o,s,h)),p.texSubImage2D(e,t,r,i,o,s,l,f,g)},gb:function(e,t){p.uniform1f(H(e),t)},hb:function(e,t,r){if(2<=B.version)t&&p.uniform1fv(H(e),R,r>>2,t);else{if(288>=t)for(var i=Te[t-1],o=0;o<t;++o)i[o]=R[r+4*o>>2];else i=R.subarray(r>>2,r+4*t>>2);p.uniform1fv(H(e),i)}},Uc:function(e,t){p.uniform1i(H(e),t)},Vc:function(e,t,r){if(2<=B.version)t&&p.uniform1iv(H(e),w,r>>2,t);else{if(288>=t)for(var i=Ze[t-1],o=0;o<t;++o)i[o]=w[r+4*o>>2];else i=w.subarray(r>>2,r+4*t>>2);p.uniform1iv(H(e),i)}},Wc:function(e,t,r){p.uniform2f(H(e),t,r)},Xc:function(e,t,r){if(2<=B.version)t&&p.uniform2fv(H(e),R,r>>2,2*t);else{if(144>=t)for(var i=Te[2*t-1],o=0;o<2*t;o+=2)i[o]=R[r+4*o>>2],i[o+1]=R[r+(4*o+4)>>2];else i=R.subarray(r>>2,r+8*t>>2);p.uniform2fv(H(e),i)}},Tc:function(e,t,r){p.uniform2i(H(e),t,r)},Sc:function(e,t,r){if(2<=B.version)t&&p.uniform2iv(H(e),w,r>>2,2*t);else{if(144>=t)for(var i=Ze[2*t-1],o=0;o<2*t;o+=2)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2];else i=w.subarray(r>>2,r+8*t>>2);p.uniform2iv(H(e),i)}},Rc:function(e,t,r,i){p.uniform3f(H(e),t,r,i)},Qc:function(e,t,r){if(2<=B.version)t&&p.uniform3fv(H(e),R,r>>2,3*t);else{if(96>=t)for(var i=Te[3*t-1],o=0;o<3*t;o+=3)i[o]=R[r+4*o>>2],i[o+1]=R[r+(4*o+4)>>2],i[o+2]=R[r+(4*o+8)>>2];else i=R.subarray(r>>2,r+12*t>>2);p.uniform3fv(H(e),i)}},Pc:function(e,t,r,i){p.uniform3i(H(e),t,r,i)},Oc:function(e,t,r){if(2<=B.version)t&&p.uniform3iv(H(e),w,r>>2,3*t);else{if(96>=t)for(var i=Ze[3*t-1],o=0;o<3*t;o+=3)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2],i[o+2]=w[r+(4*o+8)>>2];else i=w.subarray(r>>2,r+12*t>>2);p.uniform3iv(H(e),i)}},Nc:function(e,t,r,i,o){p.uniform4f(H(e),t,r,i,o)},Mc:function(e,t,r){if(2<=B.version)t&&p.uniform4fv(H(e),R,r>>2,4*t);else{if(72>=t){var i=Te[4*t-1],o=R;r>>=2;for(var s=0;s<4*t;s+=4){var l=r+s;i[s]=o[l],i[s+1]=o[l+1],i[s+2]=o[l+2],i[s+3]=o[l+3]}}else i=R.subarray(r>>2,r+16*t>>2);p.uniform4fv(H(e),i)}},Ac:function(e,t,r,i,o){p.uniform4i(H(e),t,r,i,o)},Bc:function(e,t,r){if(2<=B.version)t&&p.uniform4iv(H(e),w,r>>2,4*t);else{if(72>=t)for(var i=Ze[4*t-1],o=0;o<4*t;o+=4)i[o]=w[r+4*o>>2],i[o+1]=w[r+(4*o+4)>>2],i[o+2]=w[r+(4*o+8)>>2],i[o+3]=w[r+(4*o+12)>>2];else i=w.subarray(r>>2,r+16*t>>2);p.uniform4iv(H(e),i)}},Cc:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix2fv(H(e),!!r,R,i>>2,4*t);else{if(72>=t)for(var o=Te[4*t-1],s=0;s<4*t;s+=4)o[s]=R[i+4*s>>2],o[s+1]=R[i+(4*s+4)>>2],o[s+2]=R[i+(4*s+8)>>2],o[s+3]=R[i+(4*s+12)>>2];else o=R.subarray(i>>2,i+16*t>>2);p.uniformMatrix2fv(H(e),!!r,o)}},Dc:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix3fv(H(e),!!r,R,i>>2,9*t);else{if(32>=t)for(var o=Te[9*t-1],s=0;s<9*t;s+=9)o[s]=R[i+4*s>>2],o[s+1]=R[i+(4*s+4)>>2],o[s+2]=R[i+(4*s+8)>>2],o[s+3]=R[i+(4*s+12)>>2],o[s+4]=R[i+(4*s+16)>>2],o[s+5]=R[i+(4*s+20)>>2],o[s+6]=R[i+(4*s+24)>>2],o[s+7]=R[i+(4*s+28)>>2],o[s+8]=R[i+(4*s+32)>>2];else o=R.subarray(i>>2,i+36*t>>2);p.uniformMatrix3fv(H(e),!!r,o)}},Ec:function(e,t,r,i){if(2<=B.version)t&&p.uniformMatrix4fv(H(e),!!r,R,i>>2,16*t);else{if(18>=t){var o=Te[16*t-1],s=R;i>>=2;for(var l=0;l<16*t;l+=16){var f=i+l;o[l]=s[f],o[l+1]=s[f+1],o[l+2]=s[f+2],o[l+3]=s[f+3],o[l+4]=s[f+4],o[l+5]=s[f+5],o[l+6]=s[f+6],o[l+7]=s[f+7],o[l+8]=s[f+8],o[l+9]=s[f+9],o[l+10]=s[f+10],o[l+11]=s[f+11],o[l+12]=s[f+12],o[l+13]=s[f+13],o[l+14]=s[f+14],o[l+15]=s[f+15]}}else o=R.subarray(i>>2,i+64*t>>2);p.uniformMatrix4fv(H(e),!!r,o)}},Fc:function(e){e=fe[e],p.useProgram(e),p.Re=e},Gc:function(e,t){p.vertexAttrib1f(e,t)},Hc:function(e,t){p.vertexAttrib2f(e,R[t>>2],R[t+4>>2])},Ic:function(e,t){p.vertexAttrib3f(e,R[t>>2],R[t+4>>2],R[t+8>>2])},Jc:function(e,t){p.vertexAttrib4f(e,R[t>>2],R[t+4>>2],R[t+8>>2],R[t+12>>2])},kc:function(e,t){p.vertexAttribDivisor(e,t)},lc:function(e,t,r,i,o){p.vertexAttribIPointer(e,t,r,i,o)},Kc:function(e,t,r,i,o,s){p.vertexAttribPointer(e,t,r,!!i,o,s)},Lc:function(e,t,r,i){p.viewport(e,t,r,i)},qb:function(e,t,r,i){p.waitSync(De[e],t,(r>>>0)+4294967296*i)},W:jn,F:Hn,E:Wn,X:Un,Ib:Nn,V:Sn,U:Yn,A:Vn,B:$n,D:On,L:kn,sb:(e,t,r,i)=>Bn(e,t,r,i)};(function(){function e(r){if(b=r=r.exports,nt=b.Yc,rr(),Q=b._c,ir.unshift(b.Zc),Me--,m.monitorRunDependencies&&m.monitorRunDependencies(Me),Me==0&&(Mt!==null&&(clearInterval(Mt),Mt=null),We)){var i=We;We=null,i()}return r}var t={a:bn};if(Me++,m.monitorRunDependencies&&m.monitorRunDependencies(Me),m.instantiateWasm)try{return m.instantiateWasm(t,e)}catch(r){Ae(\"Module.instantiateWasm callback failed with error: \"+r),et(r)}return rn(t,function(r){e(r.instance)}).catch(et),{}})();var Pe=m._free=e=>(Pe=m._free=b.$c)(e),Ct=m._malloc=e=>(Ct=m._malloc=b.ad)(e),Wr=e=>(Wr=b.bd)(e);m.__embind_initialize_bindings=()=>(m.__embind_initialize_bindings=b.cd)();var ne=(e,t)=>(ne=b.dd)(e,t),ie=()=>(ie=b.ed)(),oe=e=>(oe=b.fd)(e);m.dynCall_viji=(e,t,r,i,o)=>(m.dynCall_viji=b.gd)(e,t,r,i,o),m.dynCall_vijiii=(e,t,r,i,o,s,l)=>(m.dynCall_vijiii=b.hd)(e,t,r,i,o,s,l),m.dynCall_viiiiij=(e,t,r,i,o,s,l,f)=>(m.dynCall_viiiiij=b.id)(e,t,r,i,o,s,l,f),m.dynCall_jii=(e,t,r)=>(m.dynCall_jii=b.jd)(e,t,r),m.dynCall_vij=(e,t,r,i)=>(m.dynCall_vij=b.kd)(e,t,r,i),m.dynCall_iiij=(e,t,r,i,o)=>(m.dynCall_iiij=b.ld)(e,t,r,i,o),m.dynCall_iiiij=(e,t,r,i,o,s)=>(m.dynCall_iiiij=b.md)(e,t,r,i,o,s),m.dynCall_viij=(e,t,r,i,o)=>(m.dynCall_viij=b.nd)(e,t,r,i,o),m.dynCall_viiij=(e,t,r,i,o,s)=>(m.dynCall_viiij=b.od)(e,t,r,i,o,s),m.dynCall_ji=(e,t)=>(m.dynCall_ji=b.pd)(e,t),m.dynCall_iij=(e,t,r,i)=>(m.dynCall_iij=b.qd)(e,t,r,i),m.dynCall_jiiiiii=(e,t,r,i,o,s,l)=>(m.dynCall_jiiiiii=b.rd)(e,t,r,i,o,s,l),m.dynCall_jiiiiji=(e,t,r,i,o,s,l,f)=>(m.dynCall_jiiiiji=b.sd)(e,t,r,i,o,s,l,f),m.dynCall_iijj=(e,t,r,i,o,s)=>(m.dynCall_iijj=b.td)(e,t,r,i,o,s),m.dynCall_iiiji=(e,t,r,i,o,s)=>(m.dynCall_iiiji=b.ud)(e,t,r,i,o,s),m.dynCall_iiji=(e,t,r,i,o)=>(m.dynCall_iiji=b.vd)(e,t,r,i,o),m.dynCall_iijjiii=(e,t,r,i,o,s,l,f,h)=>(m.dynCall_iijjiii=b.wd)(e,t,r,i,o,s,l,f,h),m.dynCall_vijjjii=(e,t,r,i,o,s,l,f,h,g)=>(m.dynCall_vijjjii=b.xd)(e,t,r,i,o,s,l,f,h,g),m.dynCall_jiji=(e,t,r,i,o)=>(m.dynCall_jiji=b.yd)(e,t,r,i,o),m.dynCall_viijii=(e,t,r,i,o,s,l)=>(m.dynCall_viijii=b.zd)(e,t,r,i,o,s,l),m.dynCall_iiiiij=(e,t,r,i,o,s,l)=>(m.dynCall_iiiiij=b.Ad)(e,t,r,i,o,s,l),m.dynCall_iiiiijj=(e,t,r,i,o,s,l,f,h)=>(m.dynCall_iiiiijj=b.Bd)(e,t,r,i,o,s,l,f,h),m.dynCall_iiiiiijj=(e,t,r,i,o,s,l,f,h,g)=>(m.dynCall_iiiiiijj=b.Cd)(e,t,r,i,o,s,l,f,h,g);function kn(e,t,r,i,o){var s=ie();try{Q.get(e)(t,r,i,o)}catch(l){if(oe(s),l!==l+0)throw l;ne(1,0)}}function Hn(e,t,r){var i=ie();try{return Q.get(e)(t,r)}catch(o){if(oe(i),o!==o+0)throw o;ne(1,0)}}function Un(e,t,r,i,o){var s=ie();try{return Q.get(e)(t,r,i,o)}catch(l){if(oe(s),l!==l+0)throw l;ne(1,0)}}function On(e,t,r,i){var o=ie();try{Q.get(e)(t,r,i)}catch(s){if(oe(o),s!==s+0)throw s;ne(1,0)}}function jn(e,t){var r=ie();try{return Q.get(e)(t)}catch(i){if(oe(r),i!==i+0)throw i;ne(1,0)}}function Wn(e,t,r,i){var o=ie();try{return Q.get(e)(t,r,i)}catch(s){if(oe(o),s!==s+0)throw s;ne(1,0)}}function $n(e,t,r){var i=ie();try{Q.get(e)(t,r)}catch(o){if(oe(i),o!==o+0)throw o;ne(1,0)}}function Sn(e,t,r,i,o,s,l,f,h,g){var P=ie();try{return Q.get(e)(t,r,i,o,s,l,f,h,g)}catch(E){if(oe(P),E!==E+0)throw E;ne(1,0)}}function Vn(e,t){var r=ie();try{Q.get(e)(t)}catch(i){if(oe(r),i!==i+0)throw i;ne(1,0)}}function Nn(e,t,r,i,o,s,l){var f=ie();try{return Q.get(e)(t,r,i,o,s,l)}catch(h){if(oe(f),h!==h+0)throw h;ne(1,0)}}function Yn(e){var t=ie();try{Q.get(e)()}catch(r){if(oe(t),r!==r+0)throw r;ne(1,0)}}var At;We=function e(){At||$r(),At||(We=e)};function $r(){function e(){if(!At&&(At=!0,m.calledRun=!0,!er)){if(xt(ir),Jt(m),m.onRuntimeInitialized&&m.onRuntimeInitialized(),m.postRun)for(typeof m.postRun==\"function\"&&(m.postRun=[m.postRun]);m.postRun.length;){var t=m.postRun.shift();or.unshift(t)}xt(or)}}if(!(0<Me)){if(m.preRun)for(typeof m.preRun==\"function\"&&(m.preRun=[m.preRun]);m.preRun.length;)en();xt(nr),0<Me||(m.setStatus?(m.setStatus(\"Running...\"),setTimeout(function(){setTimeout(function(){m.setStatus(\"\")},1),e()},1)):e())}}if(m.preInit)for(typeof m.preInit==\"function\"&&(m.preInit=[m.preInit]);0<m.preInit.length;)m.preInit.pop()();return $r(),ge.ready}})();typeof zr==\"object\"&&typeof Kt==\"object\"?Kt.exports=Zr:typeof define==\"function\"&&define.amd&&define([],()=>Zr)});/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Jn());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/ui.js\n"));

/***/ })

}]);