# PowerShell script to fix SPRINT - AI logo file names

Write-Host "🔧 Fixing SPRINT - AI Logo File Names" -ForegroundColor Cyan
Write-Host "=====================================" -ForegroundColor Cyan

# Define directories to fix
$directories = @(
    "public",
    "frontend\public"
)

# Define file renames
$renames = @(
    @{ from = "sprint-ai-logo-main.png.png"; to = "sprint-ai-logo-main.png" },
    @{ from = "sprint-ai-logo-compact.png.png"; to = "sprint-ai-logo-compact.png" },
    @{ from = "sprint-ai-logo-icon.png.png"; to = "sprint-ai-logo-icon.png" },
    @{ from = "sprint-ai-logo-icon.png.jpg"; to = "sprint-ai-logo-icon.png" }
)

foreach ($dir in $directories) {
    if (Test-Path $dir) {
        Write-Host "`n📁 Checking $dir:" -ForegroundColor Yellow
        
        foreach ($rename in $renames) {
            $fromPath = Join-Path $dir $rename.from
            $toPath = Join-Path $dir $rename.to
            
            if (Test-Path $fromPath) {
                try {
                    # Remove target if it exists
                    if (Test-Path $toPath) {
                        Remove-Item $toPath -Force
                    }
                    
                    # Rename the file
                    Rename-Item $fromPath $toPath
                    Write-Host "✅ Renamed: $($rename.from) → $($rename.to)" -ForegroundColor Green
                } catch {
                    Write-Host "❌ Error renaming $($rename.from): $($_.Exception.Message)" -ForegroundColor Red
                }
            } else {
                Write-Host "ℹ️  File not found: $($rename.from)" -ForegroundColor Gray
            }
        }
    } else {
        Write-Host "❌ Directory not found: $dir" -ForegroundColor Red
    }
}

# Copy logos from root to frontend if needed
Write-Host "`n📋 Copying logos from root to frontend:" -ForegroundColor Yellow

$logoFiles = @(
    "sprint-ai-logo-main.png",
    "sprint-ai-logo-main.svg",
    "sprint-ai-logo-compact.png",
    "sprint-ai-logo-icon.png",
    "sprint-ai-logo-white.png"
)

foreach ($logoFile in $logoFiles) {
    $sourcePath = Join-Path "public" $logoFile
    $targetPath = Join-Path "frontend\public" $logoFile
    
    if (Test-Path $sourcePath) {
        try {
            Copy-Item $sourcePath $targetPath -Force
            Write-Host "✅ Copied: $logoFile" -ForegroundColor Green
        } catch {
            Write-Host "❌ Error copying $logoFile`: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "⚠️  Source not found: $logoFile" -ForegroundColor Yellow
    }
}

Write-Host "`n🎉 Logo file names fixed!" -ForegroundColor Green
Write-Host "`n🚀 Next steps:" -ForegroundColor Cyan
Write-Host "1. cd frontend" -ForegroundColor White
Write-Host "2. npm run dev" -ForegroundColor White
Write-Host "3. Test: http://localhost:3000" -ForegroundColor White
