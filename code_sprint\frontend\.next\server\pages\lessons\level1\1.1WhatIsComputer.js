/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/lessons/level1/1.1WhatIsComputer";
exports.ids = ["pages/lessons/level1/1.1WhatIsComputer"];
exports.modules = {

/***/ "(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.1WhatIsComputer&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.1WhatIsComputer.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.1WhatIsComputer&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.1WhatIsComputer.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/../node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/../node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/../node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./src/pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./src/pages/_app.js\");\n/* harmony import */ var _src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./src/pages/lessons/level1/1.1WhatIsComputer.js */ \"(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js\");\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/lessons/level1/1.1WhatIsComputer\",\n        pathname: \"/lessons/level1/1.1WhatIsComputer\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _src_pages_lessons_level1_1_1WhatIsComputer_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.1WhatIsComputer&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.1WhatIsComputer.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./src/components/MarkAsReadButton.jsx":
/*!*********************************************!*\
  !*** ./src/components/MarkAsReadButton.jsx ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../utils/progressTracker */ \"(pages-dir-node)/./src/utils/progressTracker.js\");\n\n\n\nconst MarkAsReadButton = ({ lessonId, level, onComplete })=>{\n    const [isCompleted, setIsCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [completedLessons, setCompletedLessons] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"MarkAsReadButton.useEffect\": ()=>{\n            // Check if lesson is already completed\n            const checkCompletion = {\n                \"MarkAsReadButton.useEffect.checkCompletion\": async ()=>{\n                    try {\n                        const progress = await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.getUserProgress)();\n                        setCompletedLessons(progress.completedLessons || []);\n                        setIsCompleted((0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.isLessonCompleted)(lessonId, progress.completedLessons || []));\n                    } catch (error) {\n                        console.error('Error checking lesson completion:', error);\n                    }\n                }\n            }[\"MarkAsReadButton.useEffect.checkCompletion\"];\n            checkCompletion();\n        }\n    }[\"MarkAsReadButton.useEffect\"], [\n        lessonId\n    ]);\n    const handleMarkAsRead = async ()=>{\n        if (isCompleted) return; // Already completed\n        setIsLoading(true);\n        try {\n            const updatedProgress = await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_2__.markLessonComplete)(lessonId, level);\n            setIsCompleted(true);\n            setCompletedLessons(updatedProgress.completedLessons || []);\n            // Call onComplete callback if provided\n            if (onComplete) {\n                onComplete(updatedProgress);\n            }\n            // Show success message\n            alert('Lesson marked as complete! 🎉');\n        } catch (error) {\n            console.error('Error marking lesson as complete:', error);\n            alert('Failed to mark lesson as complete. Please try again.');\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center mt-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n            onClick: handleMarkAsRead,\n            disabled: isCompleted || isLoading,\n            className: `\n          px-6 py-3 rounded-lg font-semibold text-white transition-all duration-300 transform hover:scale-105\n          ${isCompleted ? 'bg-green-500 cursor-not-allowed' : 'bg-blue-500 hover:bg-blue-600 active:scale-95'}\n          ${isLoading ? 'opacity-50 cursor-not-allowed' : ''}\n        `,\n            children: isLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 64,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Updating...\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 63,\n                columnNumber: 11\n            }, undefined) : isCompleted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"currentColor\",\n                        viewBox: \"0 0 20 20\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            fillRule: \"evenodd\",\n                            d: \"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\",\n                            clipRule: \"evenodd\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                            lineNumber: 70,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 69,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Completed\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 72,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 68,\n                columnNumber: 11\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center space-x-2\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                        className: \"w-5 h-5\",\n                        fill: \"none\",\n                        stroke: \"currentColor\",\n                        viewBox: \"0 0 24 24\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                            strokeLinecap: \"round\",\n                            strokeLinejoin: \"round\",\n                            strokeWidth: 2,\n                            d: \"M5 13l4 4L19 7\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                            lineNumber: 77,\n                            columnNumber: 15\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 76,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                        children: \"Mark as Read\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                        lineNumber: 79,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n                lineNumber: 75,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n            lineNumber: 50,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/components/MarkAsReadButton.jsx\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (MarkAsReadButton);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/components/MarkAsReadButton.jsx\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_app.js":
/*!***************************!*\
  !*** ./src/pages/_app.js ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./src/styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fYXBwLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7OztBQUFBLGdCQUFnQjs7QUFDYyxDQUFDLGdCQUFnQjtBQUNXLENBQUMsaUJBQWlCO0FBQ3pDO0FBRXBCLFNBQVNDLElBQUksRUFBRUMsU0FBUyxFQUFFQyxTQUFTLEVBQUU7SUFDbEQscUJBQU8sOERBQUNEO1FBQVcsR0FBR0MsU0FBUzs7Ozs7O0FBQ2pDO0FBRUEsd0JBQXdCO0FBQ3hCRixJQUFJRyxTQUFTLEdBQUc7SUFDZEYsV0FBV0YsK0RBQXFCLENBQUNNLFVBQVU7SUFDM0NILFdBQVdILDBEQUFnQixDQUFDTSxVQUFVO0FBQ3hDIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9mcm9udGVuZC9zcmMvcGFnZXMvX2FwcC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBwYWdlcy9fYXBwLmpzXG5pbXBvcnQgXCJAL3N0eWxlcy9nbG9iYWxzLmNzc1wiOyAvLyBHbG9iYWwgc3R5bGVzXG5pbXBvcnQgXCIuL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3NcIjsgLy8gQW5pbWF0aW9ucyBDU1NcbmltcG9ydCBQcm9wVHlwZXMgZnJvbSBcInByb3AtdHlwZXNcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gQXBwKHsgQ29tcG9uZW50LCBwYWdlUHJvcHMgfSkge1xuICByZXR1cm4gPENvbXBvbmVudCB7Li4ucGFnZVByb3BzfSAvPjtcbn1cblxuLy8gQWRkIFByb3BUeXBlcyBmb3IgQXBwXG5BcHAucHJvcFR5cGVzID0ge1xuICBDb21wb25lbnQ6IFByb3BUeXBlcy5lbGVtZW50VHlwZS5pc1JlcXVpcmVkLCAvLyBDb21wb25lbnQgbXVzdCBiZSBhIHZhbGlkIFJlYWN0IGNvbXBvbmVudFxuICBwYWdlUHJvcHM6IFByb3BUeXBlcy5vYmplY3QuaXNSZXF1aXJlZCwgLy8gcGFnZVByb3BzIGlzIGFuIG9iamVjdFxufTtcbiJdLCJuYW1lcyI6WyJQcm9wVHlwZXMiLCJBcHAiLCJDb21wb25lbnQiLCJwYWdlUHJvcHMiLCJwcm9wVHlwZXMiLCJlbGVtZW50VHlwZSIsImlzUmVxdWlyZWQiLCJvYmplY3QiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/_document.js":
/*!********************************!*\
  !*** ./src/pages/_document.js ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/../node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9fZG9jdW1lbnQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQTZEO0FBRTlDLFNBQVNJO0lBQ3RCLHFCQUNFLDhEQUFDSiwrQ0FBSUE7UUFBQ0ssTUFBSzs7MEJBQ1QsOERBQUNKLCtDQUFJQTs7Ozs7MEJBQ0wsOERBQUNLOztrQ0FDQyw4REFBQ0osK0NBQUlBOzs7OztrQ0FDTCw4REFBQ0MscURBQVVBOzs7Ozs7Ozs7Ozs7Ozs7OztBQUluQiIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL19kb2N1bWVudC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgeyBIdG1sLCBIZWFkLCBNYWluLCBOZXh0U2NyaXB0IH0gZnJvbSBcIm5leHQvZG9jdW1lbnRcIjtcblxuZXhwb3J0IGRlZmF1bHQgZnVuY3Rpb24gRG9jdW1lbnQoKSB7XG4gIHJldHVybiAoXG4gICAgPEh0bWwgbGFuZz1cImVuXCI+XG4gICAgICA8SGVhZCAvPlxuICAgICAgPGJvZHk+XG4gICAgICAgIDxNYWluIC8+XG4gICAgICAgIDxOZXh0U2NyaXB0IC8+XG4gICAgICA8L2JvZHk+XG4gICAgPC9IdG1sPlxuICApO1xufVxuIl0sIm5hbWVzIjpbIkh0bWwiLCJIZWFkIiwiTWFpbiIsIk5leHRTY3JpcHQiLCJEb2N1bWVudCIsImxhbmciLCJib2R5Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css":
/*!******************************************************************!*\
  !*** ./src/pages/lessons/level-4/Section1/animations.module.css ***!
  \******************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__VOjD6\",\n\t\"fadeInUp\": \"animations_fadeInUp__wib6A\",\n\t\"hidden\": \"animations_hidden__KLYnx\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3NyYy9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiL21udC9kL0FhbWlyL2NvZGVzcHJpbnQvY29kZV9zcHJpbnQvZnJvbnRlbmQvc3JjL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gRXhwb3J0c1xubW9kdWxlLmV4cG9ydHMgPSB7XG5cdFwiY29uZmV0dGlcIjogXCJhbmltYXRpb25zX2NvbmZldHRpX19WT2pENlwiLFxuXHRcImZhZGVJblVwXCI6IFwiYW5pbWF0aW9uc19mYWRlSW5VcF9fd2liNkFcIixcblx0XCJoaWRkZW5cIjogXCJhbmltYXRpb25zX2hpZGRlbl9fS0xZbnhcIlxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js":
/*!*******************************************************!*\
  !*** ./src/pages/lessons/level1/1.1WhatIsComputer.js ***!
  \*******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/../node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _components_MarkAsReadButton__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../../../components/MarkAsReadButton */ \"(pages-dir-node)/./src/components/MarkAsReadButton.jsx\");\n/* harmony import */ var _utils_progressTracker__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../../../utils/progressTracker */ \"(pages-dir-node)/./src/utils/progressTracker.js\");\n\n // Import Image from next/image\n\n\n\nconst WhatIsComputer = ()=>{\n    // Track lesson access when component mounts\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"WhatIsComputer.useEffect\": ()=>{\n            const trackAccess = {\n                \"WhatIsComputer.useEffect.trackAccess\": async ()=>{\n                    try {\n                        await (0,_utils_progressTracker__WEBPACK_IMPORTED_MODULE_4__.trackLessonAccess)('1.1WhatIsComputer', 'level1');\n                    } catch (error) {\n                        console.error('Error tracking lesson access:', error);\n                    }\n                }\n            }[\"WhatIsComputer.useEffect.trackAccess\"];\n            trackAccess();\n        }\n    }[\"WhatIsComputer.useEffect\"], []);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" bg-white text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white-100 border-l-4 border-blue-500 p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4 text-purple-700\",\n                    children: \"What is a Computer?\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 23,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-4\",\n                    children: [\n                        \"A computer is a \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold text-blue-500\",\n                            children: \"smart machine\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 27,\n                            columnNumber: 25\n                        }, undefined),\n                        \" that helps us do many things like play games, draw pictures, learn new things, and talk to our friends. It can follow instructions to do all these tasks very quickly.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 26,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Microchip on a Fingertip:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 33,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Did you know? Computers that used to take up an entire room now fit comfortably on your finger! A microchip is a tiny part inside a computer that helps it do all the amazing things it can do.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 32,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-center items-center\",\n                    children: [\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            src: \"/lvl1_img/Untitled.png\" // Corrected path\n                            ,\n                            alt: \"A computer illustration\",\n                            width: 500,\n                            height: 300,\n                            className: \"rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 40,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_MarkAsReadButton__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                    lessonId: \"1.1WhatIsComputer\",\n                    level: \"level1\",\n                    onComplete: (progress)=>{\n                        console.log('Lesson completed!', progress);\n                    }\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 50,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n            lineNumber: 22,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/frontend/src/pages/lessons/level1/1.1WhatIsComputer.js\",\n        lineNumber: 21,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatIsComputer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/pages/lessons/level1/1.1WhatIsComputer.js\n");

/***/ }),

/***/ "(pages-dir-node)/./src/styles/globals.css":
/*!********************************!*\
  !*** ./src/styles/globals.css ***!
  \********************************/
/***/ (() => {



/***/ }),

/***/ "(pages-dir-node)/./src/utils/progressTracker.js":
/*!**************************************!*\
  !*** ./src/utils/progressTracker.js ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateOverallProgress: () => (/* binding */ calculateOverallProgress),\n/* harmony export */   getLessonDisplayName: () => (/* binding */ getLessonDisplayName),\n/* harmony export */   getNextLesson: () => (/* binding */ getNextLesson),\n/* harmony export */   getUserProgress: () => (/* binding */ getUserProgress),\n/* harmony export */   isLessonCompleted: () => (/* binding */ isLessonCompleted),\n/* harmony export */   markLessonComplete: () => (/* binding */ markLessonComplete),\n/* harmony export */   trackLessonAccess: () => (/* binding */ trackLessonAccess),\n/* harmony export */   updateProgress: () => (/* binding */ updateProgress)\n/* harmony export */ });\n// Progress tracking utility for LMS\nconst API_BASE_URL =  false ? 0 : 'http://localhost:5000/api';\n// Get user's progress from backend\nconst getUserProgress = async ()=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage\n        if (!token) {\n            console.log('No token found, using local storage for progress');\n            return getProgressLocally();\n        }\n        const response = await fetch(`${API_BASE_URL}/progress`, {\n            method: 'GET',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            }\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return getProgressLocally();\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error fetching progress, using local storage fallback:', error.message);\n        return getProgressLocally();\n    }\n};\n// Get progress from local storage\nconst getProgressLocally = ()=>{\n    try {\n        const progress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        return {\n            currentLevel: progress.currentLevel || 'level1',\n            currentLesson: progress.currentLesson || '1.1WhatIsComputer',\n            completedLessons: progress.completedLessons || [],\n            accessedLessons: progress.accessedLessons || [],\n            lastAccessedAt: progress.lastAccessedAt || new Date().toISOString(),\n            totalLessonsCompleted: progress.totalLessonsCompleted || 0,\n            progressPercentage: progress.progressPercentage || 0\n        };\n    } catch (error) {\n        console.error('Error reading local progress:', error);\n        return {\n            currentLevel: 'level1',\n            currentLesson: '1.1WhatIsComputer',\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Local storage fallback for progress tracking\nconst updateProgressLocally = (lessonId, level, action)=>{\n    try {\n        // Get existing progress from localStorage\n        const existingProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');\n        // Initialize if empty\n        if (!existingProgress.completedLessons) {\n            existingProgress.completedLessons = [];\n        }\n        if (!existingProgress.accessedLessons) {\n            existingProgress.accessedLessons = [];\n        }\n        // Update progress based on action\n        if (action === 'access' && !existingProgress.accessedLessons.includes(lessonId)) {\n            existingProgress.accessedLessons.push(lessonId);\n        }\n        if (action === 'complete' && !existingProgress.completedLessons.includes(lessonId)) {\n            existingProgress.completedLessons.push(lessonId);\n        }\n        // Update other fields\n        existingProgress.currentLevel = level;\n        existingProgress.currentLesson = lessonId;\n        existingProgress.lastAccessedAt = new Date().toISOString();\n        existingProgress.totalLessonsCompleted = existingProgress.completedLessons.length;\n        existingProgress.progressPercentage = calculateOverallProgress(existingProgress.completedLessons);\n        // Save back to localStorage\n        localStorage.setItem('userProgress', JSON.stringify(existingProgress));\n        console.log(`Progress updated locally: ${action} for ${lessonId} in ${level}`);\n        return existingProgress;\n    } catch (error) {\n        console.error('Error updating progress locally:', error);\n        return {\n            currentLevel: level,\n            currentLesson: lessonId,\n            completedLessons: [],\n            accessedLessons: [],\n            lastAccessedAt: new Date().toISOString(),\n            totalLessonsCompleted: 0,\n            progressPercentage: 0\n        };\n    }\n};\n// Update user's progress\nconst updateProgress = async (lessonId, level, action = 'access')=>{\n    try {\n        const token = localStorage.getItem('token');\n        // If no token, use local storage fallback\n        if (!token) {\n            console.log('No token found, using local storage for progress tracking');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const response = await fetch(`${API_BASE_URL}/progress/update`, {\n            method: 'POST',\n            headers: {\n                'Authorization': `Bearer ${token}`,\n                'Content-Type': 'application/json'\n            },\n            body: JSON.stringify({\n                lessonId,\n                level,\n                action\n            })\n        });\n        if (!response.ok) {\n            console.log('Backend not available, using local storage fallback');\n            return updateProgressLocally(lessonId, level, action);\n        }\n        const data = await response.json();\n        return data.progress;\n    } catch (error) {\n        console.log('Error updating progress, using local storage fallback:', error.message);\n        return updateProgressLocally(lessonId, level, action);\n    }\n};\n// Mark lesson as completed\nconst markLessonComplete = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'complete');\n};\n// Track lesson access\nconst trackLessonAccess = async (lessonId, level)=>{\n    return await updateProgress(lessonId, level, 'access');\n};\n// Check if lesson is completed\nconst isLessonCompleted = (lessonId, completedLessons)=>{\n    return completedLessons.includes(lessonId);\n};\n// Get next lesson based on current progress\nconst getNextLesson = (currentLevel, currentLesson)=>{\n    // Define lesson sequences for each level\n    const lessonSequences = {\n        level1: [\n            '1.1WhatIsComputer',\n            '1.2HowComputersWork',\n            '1.3WhatIsAProgram',\n            '1.4TypesOfComputers',\n            '1.5HardwareAndSoftware',\n            '1.6OperatingSystem',\n            '2.1WhatIsScratch',\n            '3.1WhatIsIoT',\n            '3.2HowIoTWorks',\n            '3.3IoTExamples',\n            '3.4IoTBenefits',\n            '4.1WhatIsComputerVision',\n            '4.2HowComputerVisionWorks',\n            '4.3ComputerVisionApplications',\n            '5.1Summary',\n            '5.3CompletionMessage'\n        ],\n        level2: [\n            '0.0CourseContent',\n            '0.1DisplayLevel2',\n            '01.0MetaverseAndAugmentedReality',\n            '01.1CoolTechnologyBehindMetaverse',\n            '02.01WhatIsAugmentedReality',\n            '02.0AugmentedReality',\n            '02.1MarkerBasedAR',\n            '02.2MarkerlessAR',\n            '02.3UsesOfAugmentedReality',\n            '03.0VirtualReality',\n            '03.2ApplicationsOfVirtualReality',\n            '04.1UnderstandingDNA',\n            '04.2CoolAIToolsForGenomics',\n            '04.3ApplicationsInMedicine',\n            '04.4FunFactsGenomicAI',\n            '2.0CyberSecurity',\n            '2.1WhatIsCybersecurity',\n            '2.2ThreatsAndAttacks',\n            '2.3ProtectingYourselfOnline',\n            '2.4FutureOfCybersecurity',\n            '3.0QuantumComputing',\n            '3.1WhatIsQuantumComputing',\n            '3.2QuantumBitsAndEntanglement',\n            '4.0GenomicAI'\n        ],\n        level3: [\n            '0.0CourseContent',\n            '1.0IntroductionToDataScience',\n            '1.1GlitchSaysHi',\n            '1.2WhatIsDataScience',\n            '1.3UnderstandingDataScience',\n            '2.1HowDataIsCollected',\n            '2.2UnderstandingMeanMedianMode',\n            '2.3ExploringDataTypes',\n            '3.0DataVisualization',\n            '3.1WhyVisualizeData',\n            '3.2CreateYourOwnBarGraph',\n            '3.3IntroductionToCharts',\n            '3.4DataStorytelling',\n            '3.5AdvancedVisualization',\n            '4.0Quiz',\n            '4.1Conclusion'\n        ],\n        level4: [\n            // Add level 4 lessons here\n            '1.1HelloWorld',\n            '1.2Pyramid',\n            '1.3Ascii',\n            '1.4Letter',\n            '2.1DataTypes',\n            '2.2DataTypes',\n            '2.3whew',\n            '3.1ControlFlow',\n            '3.2AreYouACatPerson',\n            '3.3TheGoodTheBadAndTheElif',\n            '3.4BookOfAnswers',\n            '3.5GodOfThunder',\n            '4.1DontMakeMeGuess',\n            '4.2OhYouAreActuallyMakingMeGuess',\n            '4.3PlierEtendreReleverElancer',\n            '4.4MyTrueLoveSentToMe',\n            '4.5GoodOlFizzBuzz'\n        ],\n        level5: [\n            // Add level 5 lessons here\n            'Ai',\n            'MachineLearning',\n            'NeuralNetworks',\n            'DeepLearning',\n            'Future',\n            'Final',\n            'Whatisnlp',\n            'Human',\n            'Breakingitdownwithtokenization',\n            'Cleaningupthemess',\n            'Letsgetsentimental',\n            'Buildingachatbot'\n        ]\n    };\n    const currentSequence = lessonSequences[currentLevel];\n    if (!currentSequence) return null;\n    const currentIndex = currentSequence.indexOf(currentLesson);\n    if (currentIndex === -1 || currentIndex === currentSequence.length - 1) {\n        // Move to next level if current level is complete\n        const levelNumber = parseInt(currentLevel.replace('level', ''));\n        const nextLevel = `level${levelNumber + 1}`;\n        if (lessonSequences[nextLevel]) {\n            return {\n                level: nextLevel,\n                lesson: lessonSequences[nextLevel][0]\n            };\n        }\n        return null; // All levels completed\n    }\n    return {\n        level: currentLevel,\n        lesson: currentSequence[currentIndex + 1]\n    };\n};\n// Calculate overall progress percentage\nconst calculateOverallProgress = (completedLessons)=>{\n    const totalLessons = 100; // Adjust based on actual total\n    return Math.round(completedLessons.length / totalLessons * 100);\n};\n// Get lesson display name\nconst getLessonDisplayName = (lessonId)=>{\n    const lessonNames = {\n        '1.1WhatIsComputer': 'What is a Computer?',\n        '1.2HowComputersWork': 'How Computers Work',\n        '1.3WhatIsAProgram': 'What is a Program?',\n        '1.4TypesOfComputers': 'Types of Computers',\n        '1.5HardwareAndSoftware': 'Hardware and Software',\n        '1.6OperatingSystem': 'Operating System',\n        '2.1WhatIsScratch': 'What is Scratch?',\n        '3.1WhatIsIoT': 'What is IoT?',\n        '3.2HowIoTWorks': 'How IoT Works',\n        '3.3IoTExamples': 'IoT Examples',\n        '3.4IoTBenefits': 'IoT Benefits',\n        '4.1WhatIsComputerVision': 'What is Computer Vision?',\n        '4.2HowComputerVisionWorks': 'How Computer Vision Works',\n        '4.3ComputerVisionApplications': 'Computer Vision Applications',\n        '5.1Summary': 'Summary',\n        '5.3CompletionMessage': 'Completion Message'\n    };\n    return lessonNames[lessonId] || lessonId;\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./src/utils/progressTracker.js\n");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/../node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel1%2F1.1WhatIsComputer&preferredRegion=&absolutePagePath=.%2Fsrc%2Fpages%2Flessons%2Flevel1%2F1.1WhatIsComputer.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();