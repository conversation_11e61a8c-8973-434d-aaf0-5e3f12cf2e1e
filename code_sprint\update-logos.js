const fs = require('fs');
const path = require('path');

console.log('🎨 SPRINT - AI Logo Update Script');
console.log('==================================\n');

// Define the files that need logo updates
const filesToUpdate = [
  // Frontend files
  'frontend/src/pages/index.jsx',
  'frontend/src/pages/dashboard.jsx', 
  'frontend/src/pages/login.jsx',
  'frontend/src/pages/register.jsx',
  
  // Root files
  'pages/index.jsx',
  'pages/dashboard.jsx',
  'pages/login.jsx',
  
  // Configuration files
  'check-images.js'
];

// Logo replacement mappings
const logoReplacements = [
  {
    old: '/codesprint-logo.png',
    new: '/sprint-ai-logo-main.png',
    description: 'Main logo reference'
  },
  {
    old: '/codesprint-logo.svg',
    new: '/sprint-ai-logo-main.png',
    description: 'SVG logo reference'
  },
  {
    old: 'CodeSprint Logo',
    new: 'SPRINT - AI Logo',
    description: 'Alt text'
  },
  {
    old: 'codesprint-logo.png',
    new: 'sprint-ai-logo-main.png',
    description: 'Image checker reference'
  },
  {
    old: 'codesprint-logo.svg',
    new: 'sprint-ai-logo-main.png',
    description: 'Image checker SVG reference'
  }
];

// Check if required logo files exist
function checkLogoFiles() {
  const requiredLogos = [
    'sprint-ai-logo-main.png',
    'sprint-ai-logo-compact.png', 
    'sprint-ai-logo-icon.png',
    'sprint-ai-logo-white.png'
  ];
  
  const publicDirs = ['public', 'frontend/public'];
  let allFilesExist = true;
  
  console.log('📁 Checking for required logo files...\n');
  
  publicDirs.forEach(dir => {
    console.log(`Checking ${dir}/:`);
    requiredLogos.forEach(logo => {
      const filePath = path.join(__dirname, dir, logo);
      if (fs.existsSync(filePath)) {
        console.log(`✅ ${logo}`);
      } else {
        console.log(`❌ ${logo} - MISSING`);
        allFilesExist = false;
      }
    });
    console.log('');
  });
  
  return allFilesExist;
}

// Update a single file
function updateFile(filePath) {
  const fullPath = path.join(__dirname, filePath);
  
  if (!fs.existsSync(fullPath)) {
    console.log(`⚠️  File not found: ${filePath}`);
    return false;
  }
  
  try {
    let content = fs.readFileSync(fullPath, 'utf8');
    let updated = false;
    
    logoReplacements.forEach(replacement => {
      if (content.includes(replacement.old)) {
        content = content.replace(new RegExp(replacement.old, 'g'), replacement.new);
        updated = true;
        console.log(`   ✅ Updated ${replacement.description}`);
      }
    });
    
    if (updated) {
      fs.writeFileSync(fullPath, content, 'utf8');
      console.log(`✅ Updated: ${filePath}`);
      return true;
    } else {
      console.log(`ℹ️  No changes needed: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.log(`❌ Error updating ${filePath}: ${error.message}`);
    return false;
  }
}

// Main execution
function main() {
  // Step 1: Check if logo files exist
  if (!checkLogoFiles()) {
    console.log('❌ Missing logo files! Please save the logo files first.');
    console.log('📖 See LOGO_UPDATE_GUIDE.md for instructions.\n');
    return;
  }
  
  console.log('🚀 All logo files found! Starting updates...\n');
  
  // Step 2: Update all files
  let updatedCount = 0;
  let totalFiles = 0;
  
  filesToUpdate.forEach(filePath => {
    totalFiles++;
    if (updateFile(filePath)) {
      updatedCount++;
    }
    console.log('');
  });
  
  // Step 3: Summary
  console.log('📊 Update Summary:');
  console.log('==================');
  console.log(`Files processed: ${totalFiles}`);
  console.log(`Files updated: ${updatedCount}`);
  console.log(`Files unchanged: ${totalFiles - updatedCount}`);
  
  if (updatedCount > 0) {
    console.log('\n🎉 Logo update completed successfully!');
    console.log('\n🚀 Next steps:');
    console.log('1. Start your application: npm run dev');
    console.log('2. Check all pages for updated logos');
    console.log('3. Verify no 404 errors in browser console');
  } else {
    console.log('\nℹ️  No files needed updating. Logos may already be current.');
  }
}

// Run the script
main();
