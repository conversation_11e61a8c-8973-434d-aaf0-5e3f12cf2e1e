{"name": "@splinetool/runtime", "version": "1.9.98", "description": "Spline is a collaborative design platform for creating production-ready interactive experiences in multiple dimensions. © 2024 Spline, Inc.", "keywords": ["spline", "3D", "webgl", "shader", "interactive", "collaborative"], "type": "module", "main": "./build/runtime.js", "module": "./build/runtime.js", "types": "./runtime.d.ts", "react-native": "./build/runtime.js", "exports": {".": {"types": "./runtime.d.ts", "import": "./build/runtime.js", "require": "./build/runtime.cjs"}, "./build/runtime.js": {"types": "./runtime.d.ts", "import": "./build/runtime.js", "require": "./build/runtime.cjs"}}, "files": ["build", "runtime.d.ts"], "scripts": {"start": "cross-env NODE_ENV=development node --experimental-json-modules ./esbuild.mjs", "build": "cross-env NODE_ENV=production node --experimental-json-modules ./esbuild.mjs", "test-server-side": "cross-env NODE_ENV=test node --experimental-json-modules ./esbuild.mjs && node ./build/runtime.cjs && echo '✅ Pass'", "lint": "eslint src/ --max-warnings=0 --cache", "format": "prettier src/ --write", "typecheck": "tsc", "analyze": "cross-env GENERATE_SOURCEMAP=true yarn build && cross-env NODE_OPTIONS=--no-experimental-fetch source-map-explorer 'build/runtime.js' --no-border-checks", "example": "cross-env VITE_IS_PROD=true yarn build && vite"}, "devDependencies": {"@types/semver-compare": "^1.0.1", "browserslist": "^4.16.7", "browserslist-to-esbuild": "^1.1.1", "cross-env": "^7.0.3", "esbuild": "^0.15.16", "esbuild-plugin-glslify": "^1.0.1", "eslint": "^8.46.0", "glslify-import": "^3.1.0", "jest": "26.6.0", "lodash-es": "^4.17.21", "prettier": "^2.6.2", "raw-loader": "^4.0.2", "source-map-explorer": "^2.5.2", "spe": "0.0.0", "spe2d": "0.0.0", "three": "patch:three@npm:0.149.0#./packages/editor/patches/three.patch", "typescript": "^4.1.3", "vite": "^5.4.10"}, "browserslist": {"development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"], "production": [">0.2%", "not dead", "not op_mini all", "not IE <= 11", "Safari >= 13.0", "not Android <= 4.4"]}, "dependencies": {"on-change": "^4.0.0", "semver-compare": "^1.0.0"}}