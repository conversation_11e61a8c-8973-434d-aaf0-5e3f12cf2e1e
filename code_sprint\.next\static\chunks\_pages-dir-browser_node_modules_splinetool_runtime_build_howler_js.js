"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_howler_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/howler.js":
/*!**********************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/howler.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/*! howler.js v2.2.3 | (c) 2013-2020, James Simpson of GoldFire Studios | MIT License | howlerjs.com */\n!function(){\"use strict\";var e=function(){this.init()};e.prototype={init:function(){var e=this||n;return e._counter=1e3,e._html5AudioPool=[],e.html5PoolSize=10,e._codecs={},e._howls=[],e._muted=!1,e._volume=1,e._canPlayEvent=\"canplaythrough\",e._navigator=\"undefined\"!=typeof window&&window.navigator?window.navigator:null,e.masterGain=null,e.noAudio=!1,e.usingWebAudio=!0,e.autoSuspend=!0,e.ctx=null,e.autoUnlock=!0,e._setup(),e},volume:function(e){var o=this||n;if(e=parseFloat(e),o.ctx||_(),void 0!==e&&e>=0&&e<=1){if(o._volume=e,o._muted)return o;o.usingWebAudio&&o.masterGain.gain.setValueAtTime(e,n.ctx.currentTime);for(var t=0;t<o._howls.length;t++)if(!o._howls[t]._webAudio)for(var r=o._howls[t]._getSoundIds(),a=0;a<r.length;a++){var u=o._howls[t]._soundById(r[a]);u&&u._node&&(u._node.volume=u._volume*e)}return o}return o._volume},mute:function(e){var o=this||n;o.ctx||_(),o._muted=e,o.usingWebAudio&&o.masterGain.gain.setValueAtTime(e?0:o._volume,n.ctx.currentTime);for(var t=0;t<o._howls.length;t++)if(!o._howls[t]._webAudio)for(var r=o._howls[t]._getSoundIds(),a=0;a<r.length;a++){var u=o._howls[t]._soundById(r[a]);u&&u._node&&(u._node.muted=!!e||u._muted)}return o},stop:function(){for(var e=this||n,o=0;o<e._howls.length;o++)e._howls[o].stop();return e},unload:function(){for(var e=this||n,o=e._howls.length-1;o>=0;o--)e._howls[o].unload();return e.usingWebAudio&&e.ctx&&void 0!==e.ctx.close&&(e.ctx.close(),e.ctx=null,_()),e},codecs:function(e){return(this||n)._codecs[e.replace(/^x-/,\"\")]},_setup:function(){var e=this||n;if(e.state=e.ctx?e.ctx.state||\"suspended\":\"suspended\",e._autoSuspend(),!e.usingWebAudio)if(\"undefined\"!=typeof Audio)try{var o=new Audio;void 0===o.oncanplaythrough&&(e._canPlayEvent=\"canplay\")}catch(n){e.noAudio=!0}else e.noAudio=!0;try{var o=new Audio;o.muted&&(e.noAudio=!0)}catch(e){}return e.noAudio||e._setupCodecs(),e},_setupCodecs:function(){var e=this||n,o=null;try{o=\"undefined\"!=typeof Audio?new Audio:null}catch(n){return e}if(!o||\"function\"!=typeof o.canPlayType)return e;var t=o.canPlayType(\"audio/mpeg;\").replace(/^no$/,\"\"),r=e._navigator?e._navigator.userAgent:\"\",a=r.match(/OPR\\/([0-6].)/g),u=a&&parseInt(a[0].split(\"/\")[1],10)<33,d=-1!==r.indexOf(\"Safari\")&&-1===r.indexOf(\"Chrome\"),i=r.match(/Version\\/(.*?) /),_=d&&i&&parseInt(i[1],10)<15;return e._codecs={mp3:!(u||!t&&!o.canPlayType(\"audio/mp3;\").replace(/^no$/,\"\")),mpeg:!!t,opus:!!o.canPlayType('audio/ogg; codecs=\"opus\"').replace(/^no$/,\"\"),ogg:!!o.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/,\"\"),oga:!!o.canPlayType('audio/ogg; codecs=\"vorbis\"').replace(/^no$/,\"\"),wav:!!(o.canPlayType('audio/wav; codecs=\"1\"')||o.canPlayType(\"audio/wav\")).replace(/^no$/,\"\"),aac:!!o.canPlayType(\"audio/aac;\").replace(/^no$/,\"\"),caf:!!o.canPlayType(\"audio/x-caf;\").replace(/^no$/,\"\"),m4a:!!(o.canPlayType(\"audio/x-m4a;\")||o.canPlayType(\"audio/m4a;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),m4b:!!(o.canPlayType(\"audio/x-m4b;\")||o.canPlayType(\"audio/m4b;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),mp4:!!(o.canPlayType(\"audio/x-mp4;\")||o.canPlayType(\"audio/mp4;\")||o.canPlayType(\"audio/aac;\")).replace(/^no$/,\"\"),weba:!(_||!o.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/,\"\")),webm:!(_||!o.canPlayType('audio/webm; codecs=\"vorbis\"').replace(/^no$/,\"\")),dolby:!!o.canPlayType('audio/mp4; codecs=\"ec-3\"').replace(/^no$/,\"\"),flac:!!(o.canPlayType(\"audio/x-flac;\")||o.canPlayType(\"audio/flac;\")).replace(/^no$/,\"\")},e},_unlockAudio:function(){var e=this||n;if(!e._audioUnlocked&&e.ctx){e._audioUnlocked=!1,e.autoUnlock=!1,e._mobileUnloaded||44100===e.ctx.sampleRate||(e._mobileUnloaded=!0,e.unload()),e._scratchBuffer=e.ctx.createBuffer(1,1,22050);var o=function(n){for(;e._html5AudioPool.length<e.html5PoolSize;)try{var t=new Audio;t._unlocked=!0,e._releaseHtml5Audio(t)}catch(n){e.noAudio=!0;break}for(var r=0;r<e._howls.length;r++)if(!e._howls[r]._webAudio)for(var a=e._howls[r]._getSoundIds(),u=0;u<a.length;u++){var d=e._howls[r]._soundById(a[u]);d&&d._node&&!d._node._unlocked&&(d._node._unlocked=!0,d._node.load())}e._autoResume();var i=e.ctx.createBufferSource();i.buffer=e._scratchBuffer,i.connect(e.ctx.destination),void 0===i.start?i.noteOn(0):i.start(0),\"function\"==typeof e.ctx.resume&&e.ctx.resume(),i.onended=function(){i.disconnect(0),e._audioUnlocked=!0,document.removeEventListener(\"touchstart\",o,!0),document.removeEventListener(\"touchend\",o,!0),document.removeEventListener(\"click\",o,!0),document.removeEventListener(\"keydown\",o,!0);for(var n=0;n<e._howls.length;n++)e._howls[n]._emit(\"unlock\")}};return document.addEventListener(\"touchstart\",o,!0),document.addEventListener(\"touchend\",o,!0),document.addEventListener(\"click\",o,!0),document.addEventListener(\"keydown\",o,!0),e}},_obtainHtml5Audio:function(){var e=this||n;if(e._html5AudioPool.length)return e._html5AudioPool.pop();var o=(new Audio).play();return o&&\"undefined\"!=typeof Promise&&(o instanceof Promise||\"function\"==typeof o.then)&&o.catch(function(){console.warn(\"HTML5 Audio pool exhausted, returning potentially locked audio object.\")}),new Audio},_releaseHtml5Audio:function(e){var o=this||n;return e._unlocked&&o._html5AudioPool.push(e),o},_autoSuspend:function(){var e=this;if(e.autoSuspend&&e.ctx&&void 0!==e.ctx.suspend&&n.usingWebAudio){for(var o=0;o<e._howls.length;o++)if(e._howls[o]._webAudio)for(var t=0;t<e._howls[o]._sounds.length;t++)if(!e._howls[o]._sounds[t]._paused)return e;return e._suspendTimer&&clearTimeout(e._suspendTimer),e._suspendTimer=setTimeout(function(){if(e.autoSuspend){e._suspendTimer=null,e.state=\"suspending\";var n=function(){e.state=\"suspended\",e._resumeAfterSuspend&&(delete e._resumeAfterSuspend,e._autoResume())};e.ctx.suspend().then(n,n)}},3e4),e}},_autoResume:function(){var e=this;if(e.ctx&&void 0!==e.ctx.resume&&n.usingWebAudio)return\"running\"===e.state&&\"interrupted\"!==e.ctx.state&&e._suspendTimer?(clearTimeout(e._suspendTimer),e._suspendTimer=null):\"suspended\"===e.state||\"running\"===e.state&&\"interrupted\"===e.ctx.state?(e.ctx.resume().then(function(){e.state=\"running\";for(var n=0;n<e._howls.length;n++)e._howls[n]._emit(\"resume\")}),e._suspendTimer&&(clearTimeout(e._suspendTimer),e._suspendTimer=null)):\"suspending\"===e.state&&(e._resumeAfterSuspend=!0),e}};var n=new e,o=function(e){var n=this;if(!e.src||0===e.src.length)return void console.error(\"An array of source files must be passed with any new Howl.\");n.init(e)};o.prototype={init:function(e){var o=this;return n.ctx||_(),o._autoplay=e.autoplay||!1,o._format=\"string\"!=typeof e.format?e.format:[e.format],o._html5=e.html5||!1,o._muted=e.mute||!1,o._loop=e.loop||!1,o._pool=e.pool||5,o._preload=\"boolean\"!=typeof e.preload&&\"metadata\"!==e.preload||e.preload,o._rate=e.rate||1,o._sprite=e.sprite||{},o._src=\"string\"!=typeof e.src?e.src:[e.src],o._volume=void 0!==e.volume?e.volume:1,o._xhr={method:e.xhr&&e.xhr.method?e.xhr.method:\"GET\",headers:e.xhr&&e.xhr.headers?e.xhr.headers:null,withCredentials:!(!e.xhr||!e.xhr.withCredentials)&&e.xhr.withCredentials},o._duration=0,o._state=\"unloaded\",o._sounds=[],o._endTimers={},o._queue=[],o._playLock=!1,o._onend=e.onend?[{fn:e.onend}]:[],o._onfade=e.onfade?[{fn:e.onfade}]:[],o._onload=e.onload?[{fn:e.onload}]:[],o._onloaderror=e.onloaderror?[{fn:e.onloaderror}]:[],o._onplayerror=e.onplayerror?[{fn:e.onplayerror}]:[],o._onpause=e.onpause?[{fn:e.onpause}]:[],o._onplay=e.onplay?[{fn:e.onplay}]:[],o._onstop=e.onstop?[{fn:e.onstop}]:[],o._onmute=e.onmute?[{fn:e.onmute}]:[],o._onvolume=e.onvolume?[{fn:e.onvolume}]:[],o._onrate=e.onrate?[{fn:e.onrate}]:[],o._onseek=e.onseek?[{fn:e.onseek}]:[],o._onunlock=e.onunlock?[{fn:e.onunlock}]:[],o._onresume=[],o._webAudio=n.usingWebAudio&&!o._html5,void 0!==n.ctx&&n.ctx&&n.autoUnlock&&n._unlockAudio(),n._howls.push(o),o._autoplay&&o._queue.push({event:\"play\",action:function(){o.play()}}),o._preload&&\"none\"!==o._preload&&o.load(),o},load:function(){var e=this,o=null;if(n.noAudio)return void e._emit(\"loaderror\",null,\"No audio support.\");\"string\"==typeof e._src&&(e._src=[e._src]);for(var r=0;r<e._src.length;r++){var u,d;if(e._format&&e._format[r])u=e._format[r];else{if(\"string\"!=typeof(d=e._src[r])){e._emit(\"loaderror\",null,\"Non-string found in selected audio sources - ignoring.\");continue}u=/^data:audio\\/([^;,]+);/i.exec(d),u||(u=/\\.([^.]+)$/.exec(d.split(\"?\",1)[0])),u&&(u=u[1].toLowerCase())}if(u||console.warn('No file extension was found. Consider using the \"format\" property or specify an extension.'),u&&n.codecs(u)){o=e._src[r];break}}return o?(e._src=o,e._state=\"loading\",\"https:\"===window.location.protocol&&\"http:\"===o.slice(0,5)&&(e._html5=!0,e._webAudio=!1),new t(e),e._webAudio&&a(e),e):void e._emit(\"loaderror\",null,\"No codec support for selected audio sources.\")},play:function(e,o){var t=this,r=null;if(\"number\"==typeof e)r=e,e=null;else{if(\"string\"==typeof e&&\"loaded\"===t._state&&!t._sprite[e])return null;if(void 0===e&&(e=\"__default\",!t._playLock)){for(var a=0,u=0;u<t._sounds.length;u++)t._sounds[u]._paused&&!t._sounds[u]._ended&&(a++,r=t._sounds[u]._id);1===a?e=null:r=null}}var d=r?t._soundById(r):t._inactiveSound();if(!d)return null;if(r&&!e&&(e=d._sprite||\"__default\"),\"loaded\"!==t._state){d._sprite=e,d._ended=!1;var i=d._id;return t._queue.push({event:\"play\",action:function(){t.play(i)}}),i}if(r&&!d._paused)return o||t._loadQueue(\"play\"),d._id;t._webAudio&&n._autoResume();var _=Math.max(0,d._seek>0?d._seek:t._sprite[e][0]/1e3),s=Math.max(0,(t._sprite[e][0]+t._sprite[e][1])/1e3-_),l=1e3*s/Math.abs(d._rate),c=t._sprite[e][0]/1e3,f=(t._sprite[e][0]+t._sprite[e][1])/1e3;d._sprite=e,d._ended=!1;var p=function(){d._paused=!1,d._seek=_,d._start=c,d._stop=f,d._loop=!(!d._loop&&!t._sprite[e][2])};if(_>=f)return void t._ended(d);var m=d._node;if(t._webAudio){var v=function(){t._playLock=!1,p(),t._refreshBuffer(d);var e=d._muted||t._muted?0:d._volume;m.gain.setValueAtTime(e,n.ctx.currentTime),d._playStart=n.ctx.currentTime,void 0===m.bufferSource.start?d._loop?m.bufferSource.noteGrainOn(0,_,86400):m.bufferSource.noteGrainOn(0,_,s):d._loop?m.bufferSource.start(0,_,86400):m.bufferSource.start(0,_,s),l!==1/0&&(t._endTimers[d._id]=setTimeout(t._ended.bind(t,d),l)),o||setTimeout(function(){t._emit(\"play\",d._id),t._loadQueue()},0)};\"running\"===n.state&&\"interrupted\"!==n.ctx.state?v():(t._playLock=!0,t.once(\"resume\",v),t._clearTimer(d._id))}else{var h=function(){m.currentTime=_,m.muted=d._muted||t._muted||n._muted||m.muted,m.volume=d._volume*n.volume(),m.playbackRate=d._rate;try{var r=m.play();if(r&&\"undefined\"!=typeof Promise&&(r instanceof Promise||\"function\"==typeof r.then)?(t._playLock=!0,p(),r.then(function(){t._playLock=!1,m._unlocked=!0,o?t._loadQueue():t._emit(\"play\",d._id)}).catch(function(){t._playLock=!1,t._emit(\"playerror\",d._id,\"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.\"),d._ended=!0,d._paused=!0})):o||(t._playLock=!1,p(),t._emit(\"play\",d._id)),m.playbackRate=d._rate,m.paused)return void t._emit(\"playerror\",d._id,\"Playback was unable to start. This is most commonly an issue on mobile devices and Chrome where playback was not within a user interaction.\");\"__default\"!==e||d._loop?t._endTimers[d._id]=setTimeout(t._ended.bind(t,d),l):(t._endTimers[d._id]=function(){t._ended(d),m.removeEventListener(\"ended\",t._endTimers[d._id],!1)},m.addEventListener(\"ended\",t._endTimers[d._id],!1))}catch(e){t._emit(\"playerror\",d._id,e)}};\"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\"===m.src&&(m.src=t._src,m.load());var y=window&&window.ejecta||!m.readyState&&n._navigator.isCocoonJS;if(m.readyState>=3||y)h();else{t._playLock=!0,t._state=\"loading\";var g=function(){t._state=\"loaded\",h(),m.removeEventListener(n._canPlayEvent,g,!1)};m.addEventListener(n._canPlayEvent,g,!1),t._clearTimer(d._id)}}return d._id},pause:function(e){var n=this;if(\"loaded\"!==n._state||n._playLock)return n._queue.push({event:\"pause\",action:function(){n.pause(e)}}),n;for(var o=n._getSoundIds(e),t=0;t<o.length;t++){n._clearTimer(o[t]);var r=n._soundById(o[t]);if(r&&!r._paused&&(r._seek=n.seek(o[t]),r._rateSeek=0,r._paused=!0,n._stopFade(o[t]),r._node))if(n._webAudio){if(!r._node.bufferSource)continue;void 0===r._node.bufferSource.stop?r._node.bufferSource.noteOff(0):r._node.bufferSource.stop(0),n._cleanBuffer(r._node)}else isNaN(r._node.duration)&&r._node.duration!==1/0||r._node.pause();arguments[1]||n._emit(\"pause\",r?r._id:null)}return n},stop:function(e,n){var o=this;if(\"loaded\"!==o._state||o._playLock)return o._queue.push({event:\"stop\",action:function(){o.stop(e)}}),o;for(var t=o._getSoundIds(e),r=0;r<t.length;r++){o._clearTimer(t[r]);var a=o._soundById(t[r]);a&&(a._seek=a._start||0,a._rateSeek=0,a._paused=!0,a._ended=!0,o._stopFade(t[r]),a._node&&(o._webAudio?a._node.bufferSource&&(void 0===a._node.bufferSource.stop?a._node.bufferSource.noteOff(0):a._node.bufferSource.stop(0),o._cleanBuffer(a._node)):isNaN(a._node.duration)&&a._node.duration!==1/0||(a._node.currentTime=a._start||0,a._node.pause(),a._node.duration===1/0&&o._clearSound(a._node))),n||o._emit(\"stop\",a._id))}return o},mute:function(e,o){var t=this;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"mute\",action:function(){t.mute(e,o)}}),t;if(void 0===o){if(\"boolean\"!=typeof e)return t._muted;t._muted=e}for(var r=t._getSoundIds(o),a=0;a<r.length;a++){var u=t._soundById(r[a]);u&&(u._muted=e,u._interval&&t._stopFade(u._id),t._webAudio&&u._node?u._node.gain.setValueAtTime(e?0:u._volume,n.ctx.currentTime):u._node&&(u._node.muted=!!n._muted||e),t._emit(\"mute\",u._id))}return t},volume:function(){var e,o,t=this,r=arguments;if(0===r.length)return t._volume;if(1===r.length||2===r.length&&void 0===r[1]){t._getSoundIds().indexOf(r[0])>=0?o=parseInt(r[0],10):e=parseFloat(r[0])}else r.length>=2&&(e=parseFloat(r[0]),o=parseInt(r[1],10));var a;if(!(void 0!==e&&e>=0&&e<=1))return a=o?t._soundById(o):t._sounds[0],a?a._volume:0;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"volume\",action:function(){t.volume.apply(t,r)}}),t;void 0===o&&(t._volume=e),o=t._getSoundIds(o);for(var u=0;u<o.length;u++)(a=t._soundById(o[u]))&&(a._volume=e,r[2]||t._stopFade(o[u]),t._webAudio&&a._node&&!a._muted?a._node.gain.setValueAtTime(e,n.ctx.currentTime):a._node&&!a._muted&&(a._node.volume=e*n.volume()),t._emit(\"volume\",a._id));return t},fade:function(e,o,t,r){var a=this;if(\"loaded\"!==a._state||a._playLock)return a._queue.push({event:\"fade\",action:function(){a.fade(e,o,t,r)}}),a;e=Math.min(Math.max(0,parseFloat(e)),1),o=Math.min(Math.max(0,parseFloat(o)),1),t=parseFloat(t),a.volume(e,r);for(var u=a._getSoundIds(r),d=0;d<u.length;d++){var i=a._soundById(u[d]);if(i){if(r||a._stopFade(u[d]),a._webAudio&&!i._muted){var _=n.ctx.currentTime,s=_+t/1e3;i._volume=e,i._node.gain.setValueAtTime(e,_),i._node.gain.linearRampToValueAtTime(o,s)}a._startFadeInterval(i,e,o,t,u[d],void 0===r)}}return a},_startFadeInterval:function(e,n,o,t,r,a){var u=this,d=n,i=o-n,_=Math.abs(i/.01),s=Math.max(4,_>0?t/_:t),l=Date.now();e._fadeTo=o,e._interval=setInterval(function(){var r=(Date.now()-l)/t;l=Date.now(),d+=i*r,d=Math.round(100*d)/100,d=i<0?Math.max(o,d):Math.min(o,d),u._webAudio?e._volume=d:u.volume(d,e._id,!0),a&&(u._volume=d),(o<n&&d<=o||o>n&&d>=o)&&(clearInterval(e._interval),e._interval=null,e._fadeTo=null,u.volume(o,e._id),u._emit(\"fade\",e._id))},s)},_stopFade:function(e){var o=this,t=o._soundById(e);return t&&t._interval&&(o._webAudio&&t._node.gain.cancelScheduledValues(n.ctx.currentTime),clearInterval(t._interval),t._interval=null,o.volume(t._fadeTo,e),t._fadeTo=null,o._emit(\"fade\",e)),o},loop:function(){var e,n,o,t=this,r=arguments;if(0===r.length)return t._loop;if(1===r.length){if(\"boolean\"!=typeof r[0])return!!(o=t._soundById(parseInt(r[0],10)))&&o._loop;e=r[0],t._loop=e}else 2===r.length&&(e=r[0],n=parseInt(r[1],10));for(var a=t._getSoundIds(n),u=0;u<a.length;u++)(o=t._soundById(a[u]))&&(o._loop=e,t._webAudio&&o._node&&o._node.bufferSource&&(o._node.bufferSource.loop=e,e&&(o._node.bufferSource.loopStart=o._start||0,o._node.bufferSource.loopEnd=o._stop,t.playing(a[u])&&(t.pause(a[u],!0),t.play(a[u],!0)))));return t},rate:function(){var e,o,t=this,r=arguments;if(0===r.length)o=t._sounds[0]._id;else if(1===r.length){var a=t._getSoundIds(),u=a.indexOf(r[0]);u>=0?o=parseInt(r[0],10):e=parseFloat(r[0])}else 2===r.length&&(e=parseFloat(r[0]),o=parseInt(r[1],10));var d;if(\"number\"!=typeof e)return d=t._soundById(o),d?d._rate:t._rate;if(\"loaded\"!==t._state||t._playLock)return t._queue.push({event:\"rate\",action:function(){t.rate.apply(t,r)}}),t;void 0===o&&(t._rate=e),o=t._getSoundIds(o);for(var i=0;i<o.length;i++)if(d=t._soundById(o[i])){t.playing(o[i])&&(d._rateSeek=t.seek(o[i]),d._playStart=t._webAudio?n.ctx.currentTime:d._playStart),d._rate=e,t._webAudio&&d._node&&d._node.bufferSource?d._node.bufferSource.playbackRate.setValueAtTime(e,n.ctx.currentTime):d._node&&(d._node.playbackRate=e);var _=t.seek(o[i]),s=(t._sprite[d._sprite][0]+t._sprite[d._sprite][1])/1e3-_,l=1e3*s/Math.abs(d._rate);!t._endTimers[o[i]]&&d._paused||(t._clearTimer(o[i]),t._endTimers[o[i]]=setTimeout(t._ended.bind(t,d),l)),t._emit(\"rate\",d._id)}return t},seek:function(){var e,o,t=this,r=arguments;if(0===r.length)t._sounds.length&&(o=t._sounds[0]._id);else if(1===r.length){var a=t._getSoundIds(),u=a.indexOf(r[0]);u>=0?o=parseInt(r[0],10):t._sounds.length&&(o=t._sounds[0]._id,e=parseFloat(r[0]))}else 2===r.length&&(e=parseFloat(r[0]),o=parseInt(r[1],10));if(void 0===o)return 0;if(\"number\"==typeof e&&(\"loaded\"!==t._state||t._playLock))return t._queue.push({event:\"seek\",action:function(){t.seek.apply(t,r)}}),t;var d=t._soundById(o);if(d){if(!(\"number\"==typeof e&&e>=0)){if(t._webAudio){var i=t.playing(o)?n.ctx.currentTime-d._playStart:0,_=d._rateSeek?d._rateSeek-d._seek:0;return d._seek+(_+i*Math.abs(d._rate))}return d._node.currentTime}var s=t.playing(o);s&&t.pause(o,!0),d._seek=e,d._ended=!1,t._clearTimer(o),t._webAudio||!d._node||isNaN(d._node.duration)||(d._node.currentTime=e);var l=function(){s&&t.play(o,!0),t._emit(\"seek\",o)};if(s&&!t._webAudio){var c=function(){t._playLock?setTimeout(c,0):l()};setTimeout(c,0)}else l()}return t},playing:function(e){var n=this;if(\"number\"==typeof e){var o=n._soundById(e);return!!o&&!o._paused}for(var t=0;t<n._sounds.length;t++)if(!n._sounds[t]._paused)return!0;return!1},duration:function(e){var n=this,o=n._duration,t=n._soundById(e);return t&&(o=n._sprite[t._sprite][1]/1e3),o},state:function(){return this._state},unload:function(){for(var e=this,o=e._sounds,t=0;t<o.length;t++)o[t]._paused||e.stop(o[t]._id),e._webAudio||(e._clearSound(o[t]._node),o[t]._node.removeEventListener(\"error\",o[t]._errorFn,!1),o[t]._node.removeEventListener(n._canPlayEvent,o[t]._loadFn,!1),o[t]._node.removeEventListener(\"ended\",o[t]._endFn,!1),n._releaseHtml5Audio(o[t]._node)),delete o[t]._node,e._clearTimer(o[t]._id);var a=n._howls.indexOf(e);a>=0&&n._howls.splice(a,1);var u=!0;for(t=0;t<n._howls.length;t++)if(n._howls[t]._src===e._src||e._src.indexOf(n._howls[t]._src)>=0){u=!1;break}return r&&u&&delete r[e._src],n.noAudio=!1,e._state=\"unloaded\",e._sounds=[],e=null,null},on:function(e,n,o,t){var r=this,a=r[\"_on\"+e];return\"function\"==typeof n&&a.push(t?{id:o,fn:n,once:t}:{id:o,fn:n}),r},off:function(e,n,o){var t=this,r=t[\"_on\"+e],a=0;if(\"number\"==typeof n&&(o=n,n=null),n||o)for(a=0;a<r.length;a++){var u=o===r[a].id;if(n===r[a].fn&&u||!n&&u){r.splice(a,1);break}}else if(e)t[\"_on\"+e]=[];else{var d=Object.keys(t);for(a=0;a<d.length;a++)0===d[a].indexOf(\"_on\")&&Array.isArray(t[d[a]])&&(t[d[a]]=[])}return t},once:function(e,n,o){var t=this;return t.on(e,n,o,1),t},_emit:function(e,n,o){for(var t=this,r=t[\"_on\"+e],a=r.length-1;a>=0;a--)r[a].id&&r[a].id!==n&&\"load\"!==e||(setTimeout(function(e){e.call(this,n,o)}.bind(t,r[a].fn),0),r[a].once&&t.off(e,r[a].fn,r[a].id));return t._loadQueue(e),t},_loadQueue:function(e){var n=this;if(n._queue.length>0){var o=n._queue[0];o.event===e&&(n._queue.shift(),n._loadQueue()),e||o.action()}return n},_ended:function(e){var o=this,t=e._sprite;if(!o._webAudio&&e._node&&!e._node.paused&&!e._node.ended&&e._node.currentTime<e._stop)return setTimeout(o._ended.bind(o,e),100),o;var r=!(!e._loop&&!o._sprite[t][2]);if(o._emit(\"end\",e._id),!o._webAudio&&r&&o.stop(e._id,!0).play(e._id),o._webAudio&&r){o._emit(\"play\",e._id),e._seek=e._start||0,e._rateSeek=0,e._playStart=n.ctx.currentTime;var a=1e3*(e._stop-e._start)/Math.abs(e._rate);o._endTimers[e._id]=setTimeout(o._ended.bind(o,e),a)}return o._webAudio&&!r&&(e._paused=!0,e._ended=!0,e._seek=e._start||0,e._rateSeek=0,o._clearTimer(e._id),o._cleanBuffer(e._node),n._autoSuspend()),o._webAudio||r||o.stop(e._id,!0),o},_clearTimer:function(e){var n=this;if(n._endTimers[e]){if(\"function\"!=typeof n._endTimers[e])clearTimeout(n._endTimers[e]);else{var o=n._soundById(e);o&&o._node&&o._node.removeEventListener(\"ended\",n._endTimers[e],!1)}delete n._endTimers[e]}return n},_soundById:function(e){for(var n=this,o=0;o<n._sounds.length;o++)if(e===n._sounds[o]._id)return n._sounds[o];return null},_inactiveSound:function(){var e=this;e._drain();for(var n=0;n<e._sounds.length;n++)if(e._sounds[n]._ended)return e._sounds[n].reset();return new t(e)},_drain:function(){var e=this,n=e._pool,o=0,t=0;if(!(e._sounds.length<n)){for(t=0;t<e._sounds.length;t++)e._sounds[t]._ended&&o++;for(t=e._sounds.length-1;t>=0;t--){if(o<=n)return;e._sounds[t]._ended&&(e._webAudio&&e._sounds[t]._node&&e._sounds[t]._node.disconnect(0),e._sounds.splice(t,1),o--)}}},_getSoundIds:function(e){var n=this;if(void 0===e){for(var o=[],t=0;t<n._sounds.length;t++)o.push(n._sounds[t]._id);return o}return[e]},_refreshBuffer:function(e){var o=this;return e._node.bufferSource=n.ctx.createBufferSource(),e._node.bufferSource.buffer=r[o._src],e._panner?e._node.bufferSource.connect(e._panner):e._node.bufferSource.connect(e._node),e._node.bufferSource.loop=e._loop,e._loop&&(e._node.bufferSource.loopStart=e._start||0,e._node.bufferSource.loopEnd=e._stop||0),e._node.bufferSource.playbackRate.setValueAtTime(e._rate,n.ctx.currentTime),o},_cleanBuffer:function(e){var o=this,t=n._navigator&&n._navigator.vendor.indexOf(\"Apple\")>=0;if(n._scratchBuffer&&e.bufferSource&&(e.bufferSource.onended=null,e.bufferSource.disconnect(0),t))try{e.bufferSource.buffer=n._scratchBuffer}catch(e){}return e.bufferSource=null,o},_clearSound:function(e){/MSIE |Trident\\//.test(n._navigator&&n._navigator.userAgent)||(e.src=\"data:audio/wav;base64,UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA\")}};var t=function(e){this._parent=e,this.init()};t.prototype={init:function(){var e=this,o=e._parent;return e._muted=o._muted,e._loop=o._loop,e._volume=o._volume,e._rate=o._rate,e._seek=0,e._paused=!0,e._ended=!0,e._sprite=\"__default\",e._id=++n._counter,o._sounds.push(e),e.create(),e},create:function(){var e=this,o=e._parent,t=n._muted||e._muted||e._parent._muted?0:e._volume;return o._webAudio?(e._node=void 0===n.ctx.createGain?n.ctx.createGainNode():n.ctx.createGain(),e._node.gain.setValueAtTime(t,n.ctx.currentTime),e._node.paused=!0,e._node.connect(n.masterGain)):n.noAudio||(e._node=n._obtainHtml5Audio(),e._errorFn=e._errorListener.bind(e),e._node.addEventListener(\"error\",e._errorFn,!1),e._loadFn=e._loadListener.bind(e),e._node.addEventListener(n._canPlayEvent,e._loadFn,!1),e._endFn=e._endListener.bind(e),e._node.addEventListener(\"ended\",e._endFn,!1),e._node.src=o._src,e._node.preload=!0===o._preload?\"auto\":o._preload,e._node.volume=t*n.volume(),e._node.load()),e},reset:function(){var e=this,o=e._parent;return e._muted=o._muted,e._loop=o._loop,e._volume=o._volume,e._rate=o._rate,e._seek=0,e._rateSeek=0,e._paused=!0,e._ended=!0,e._sprite=\"__default\",e._id=++n._counter,e},_errorListener:function(){var e=this;e._parent._emit(\"loaderror\",e._id,e._node.error?e._node.error.code:0),e._node.removeEventListener(\"error\",e._errorFn,!1)},_loadListener:function(){var e=this,o=e._parent;o._duration=Math.ceil(10*e._node.duration)/10,0===Object.keys(o._sprite).length&&(o._sprite={__default:[0,1e3*o._duration]}),\"loaded\"!==o._state&&(o._state=\"loaded\",o._emit(\"load\"),o._loadQueue()),e._node.removeEventListener(n._canPlayEvent,e._loadFn,!1)},_endListener:function(){var e=this,n=e._parent;n._duration===1/0&&(n._duration=Math.ceil(10*e._node.duration)/10,n._sprite.__default[1]===1/0&&(n._sprite.__default[1]=1e3*n._duration),n._ended(e)),e._node.removeEventListener(\"ended\",e._endFn,!1)}};var r={},a=function(e){var n=e._src;if(r[n])return e._duration=r[n].duration,void i(e);if(/^data:[^;]+;base64,/.test(n)){for(var o=atob(n.split(\",\")[1]),t=new Uint8Array(o.length),a=0;a<o.length;++a)t[a]=o.charCodeAt(a);d(t.buffer,e)}else{var _=new XMLHttpRequest;_.open(e._xhr.method,n,!0),_.withCredentials=e._xhr.withCredentials,_.responseType=\"arraybuffer\",e._xhr.headers&&Object.keys(e._xhr.headers).forEach(function(n){_.setRequestHeader(n,e._xhr.headers[n])}),_.onload=function(){var n=(_.status+\"\")[0];if(\"0\"!==n&&\"2\"!==n&&\"3\"!==n)return void e._emit(\"loaderror\",null,\"Failed loading audio file with status: \"+_.status+\".\");d(_.response,e)},_.onerror=function(){e._webAudio&&(e._html5=!0,e._webAudio=!1,e._sounds=[],delete r[n],e.load())},u(_)}},u=function(e){try{e.send()}catch(n){e.onerror()}},d=function(e,o){var t=function(){o._emit(\"loaderror\",null,\"Decoding audio data failed.\")},a=function(e){e&&o._sounds.length>0?(r[o._src]=e,i(o,e)):t()};\"undefined\"!=typeof Promise&&1===n.ctx.decodeAudioData.length?n.ctx.decodeAudioData(e).then(a).catch(t):n.ctx.decodeAudioData(e,a,t)},i=function(e,n){n&&!e._duration&&(e._duration=n.duration),0===Object.keys(e._sprite).length&&(e._sprite={__default:[0,1e3*e._duration]}),\"loaded\"!==e._state&&(e._state=\"loaded\",e._emit(\"load\"),e._loadQueue())},_=function(){if(n.usingWebAudio){try{\"undefined\"!=typeof AudioContext?n.ctx=new AudioContext:\"undefined\"!=typeof webkitAudioContext?n.ctx=new webkitAudioContext:n.usingWebAudio=!1}catch(e){n.usingWebAudio=!1}n.ctx||(n.usingWebAudio=!1);var e=/iP(hone|od|ad)/.test(n._navigator&&n._navigator.platform),o=n._navigator&&n._navigator.appVersion.match(/OS (\\d+)_(\\d+)_?(\\d+)?/),t=o?parseInt(o[1],10):null;if(e&&t&&t<9){var r=/safari/.test(n._navigator&&n._navigator.userAgent.toLowerCase());n._navigator&&!r&&(n.usingWebAudio=!1)}n.usingWebAudio&&(n.masterGain=void 0===n.ctx.createGain?n.ctx.createGainNode():n.ctx.createGain(),n.masterGain.gain.setValueAtTime(n._muted?0:n._volume,n.ctx.currentTime),n.masterGain.connect(n.ctx.destination)),n._setup()}};\"function\"==typeof define&&define.amd&&define([],function(){return{Howler:n,Howl:o}}),\"undefined\"!=typeof exports&&(exports.Howler=n,exports.Howl=o),\"undefined\"!=typeof global?(global.HowlerGlobal=e,global.Howler=n,global.Howl=o,global.Sound=t):\"undefined\"!=typeof window&&(window.HowlerGlobal=e,window.Howler=n,window.Howl=o,window.Sound=t)}();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/howler.js\n"));

/***/ })

}]);