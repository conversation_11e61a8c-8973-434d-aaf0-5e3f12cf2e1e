const fs = require('fs');
const path = require('path');

console.log('🖼️  CodeSprint Image Checker');
console.log('============================\n');

// Define image paths that should exist
const requiredImages = [
  // Logo and mascot
  'sprint-ai-logo-main.svg',
  'sprint-ai-logo-main.png',
  'sprint-ai-logo-compact.png',
  'sprint-ai-logo-icon.png',
  'sprint-ai-logo-white.png',
  'glitch-mascot.png',
  
  // Level icons
  'level1-icon.svg',
  'level2-icon.svg',
  'level3-icon.svg',
  'level4-icon.svg',
  'level5-icon.svg',
  
  // Chart images
  'line_charts.png',
  'bar_charts.png',
  'pie_charts.png',
  
  // Level 1 images
  'lvl1_img/Untitled.png',
  'lvl1_img/a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg',
  
  // Game images (samples)
  'chess.png',
  'fortnite.png',
  'valorant.png'
];

const publicDir = path.join(__dirname, 'public');
const frontendPublicDir = path.join(__dirname, 'frontend', 'public');

function checkImages(directory, label) {
  console.log(`📁 Checking ${label}:`);
  
  if (!fs.existsSync(directory)) {
    console.log(`❌ Directory not found: ${directory}\n`);
    return;
  }
  
  let foundCount = 0;
  let missingCount = 0;
  
  requiredImages.forEach(imagePath => {
    const fullPath = path.join(directory, imagePath);
    if (fs.existsSync(fullPath)) {
      console.log(`✅ ${imagePath}`);
      foundCount++;
    } else {
      console.log(`❌ ${imagePath} - MISSING`);
      missingCount++;
    }
  });
  
  console.log(`\n📊 ${label} Summary: ${foundCount} found, ${missingCount} missing\n`);
  return { found: foundCount, missing: missingCount };
}

// Check both public directories
const rootResults = checkImages(publicDir, 'Root Public Directory');
const frontendResults = checkImages(frontendPublicDir, 'Frontend Public Directory');

console.log('🎯 Overall Summary:');
console.log('==================');
console.log(`Root directory: ${rootResults.found} found, ${rootResults.missing} missing`);
console.log(`Frontend directory: ${frontendResults.found} found, ${frontendResults.missing} missing`);

if (rootResults.missing === 0 && frontendResults.missing === 0) {
  console.log('\n🎉 All required images are present!');
} else {
  console.log('\n⚠️  Some images are missing. Check the paths above.');
}

console.log('\n💡 Tips:');
console.log('- Make sure you\'re running this from the code_sprint directory');
console.log('- Check that image file names match exactly (case-sensitive)');
console.log('- Verify images exist in both public directories if using both apps');
