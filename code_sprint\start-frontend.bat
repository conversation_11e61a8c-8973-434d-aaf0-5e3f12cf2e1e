@echo off
echo 🚀 Starting SPRINT - AI Frontend
echo ================================

echo 📁 Navigating to frontend directory...
cd frontend

echo 📦 Installing dependencies (if needed)...
npm install --legacy-peer-deps

echo 🔧 Starting development server...
echo.
echo 🌐 Frontend will be available at: http://localhost:3000
echo.
echo 📋 Test these URLs:
echo    - Home: http://localhost:3000
echo    - Login: http://localhost:3000/login
echo    - Courses: http://localhost:3000/courses
echo    - Level 1: http://localhost:3000/lessons/level-1
echo    - Dashboard: http://localhost:3000/dashboard
echo.
echo 🎨 SPRINT - AI logos should appear on all pages!
echo.

npm run dev
