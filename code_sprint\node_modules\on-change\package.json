{"name": "on-change", "version": "4.0.2", "description": "Watch an object or array for changes", "license": "MIT", "repository": "sindresorhus/on-change", "funding": "https://github.com/sindresorhus/on-change?sponsor=1", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": "./index.js", "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "scripts": {"test": "xo && ava && tsd", "bench": "karma start karma.bench.conf.cjs"}, "files": ["index.js", "index.d.ts", "lib"], "keywords": ["on", "change", "watch", "object", "array", "changes", "observe", "watcher", "observer", "proxy", "proxies", "es2015", "event", "listener"], "devDependencies": {"@typescript-eslint/parser": "^5.10.0", "ava": "^4.0.1", "display-value": "^2.0.0", "karma-webpack-bundle": "^1.3.1", "powerset": "0.0.1", "tsd": "^0.19.1", "typescript": "^4.5.5", "xo": "^0.47.0"}}