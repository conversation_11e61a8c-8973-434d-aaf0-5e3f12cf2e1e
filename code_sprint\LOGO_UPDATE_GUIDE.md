# 🎨 SPRINT - AI Logo Update Guide

## Step 1: Save the New Logo Files

You need to save the 4 logo variations you provided as image files in both public directories:

### Required Logo Files:
1. **sprint-ai-logo-main.png** - Main horizontal logo with full text
2. **sprint-ai-logo-compact.png** - Compact version for smaller spaces
3. **sprint-ai-logo-icon.png** - Icon only version
4. **sprint-ai-logo-white.png** - White version for dark backgrounds

### Save Locations:
```
code_sprint/public/
├── sprint-ai-logo-main.png
├── sprint-ai-logo-compact.png
├── sprint-ai-logo-icon.png
└── sprint-ai-logo-white.png

code_sprint/frontend/public/
├── sprint-ai-logo-main.png
├── sprint-ai-logo-compact.png
├── sprint-ai-logo-icon.png
└── sprint-ai-logo-white.png
```

## Step 2: Manual File Saving Instructions

1. **Right-click** on each logo image you provided
2. **Save As** with these exact names:
   - First image → `sprint-ai-logo-main.png`
   - Second image → `sprint-ai-logo-compact.png` 
   - Third image → `sprint-ai-logo-icon.png`
   - Fourth image → `sprint-ai-logo-white.png`

3. **Copy each file** to both directories:
   - `code_sprint/public/`
   - `code_sprint/frontend/public/`

## Step 3: Run the Update Script

After saving the files, run:
```bash
npm run update-logos
```

This will automatically update all logo references in the codebase.

## Files That Will Be Updated:

### Frontend Pages:
- ✅ `frontend/src/pages/index.jsx` - Home page header
- ✅ `frontend/src/pages/dashboard.jsx` - Dashboard header  
- ✅ `frontend/src/pages/login.jsx` - Login page header
- ✅ `frontend/src/pages/register.jsx` - Register page header

### Root Pages:
- ✅ `pages/index.jsx` - Home page header
- ✅ `pages/dashboard.jsx` - Dashboard header
- ✅ `pages/login.jsx` - Login page header

### Configuration:
- ✅ `check-images.js` - Image verification script
- ✅ Alt text updated to "SPRINT - AI Logo"

## Logo Usage Guidelines:

### Main Logo (`sprint-ai-logo-main.png`):
- **Use for:** Headers, main navigation, large displays
- **Size:** 150x50px (standard)
- **Background:** Light backgrounds

### Compact Logo (`sprint-ai-logo-compact.png`):
- **Use for:** Mobile headers, small spaces
- **Size:** 100x40px
- **Background:** Light backgrounds

### Icon Logo (`sprint-ai-logo-icon.png`):
- **Use for:** Favicons, app icons, very small spaces
- **Size:** 32x32px or 64x64px
- **Background:** Any

### White Logo (`sprint-ai-logo-white.png`):
- **Use for:** Dark backgrounds, overlays
- **Size:** 150x50px (standard)
- **Background:** Dark backgrounds

## Verification Steps:

1. **Save all logo files** in both public directories
2. **Run update script:** `npm run update-logos`
3. **Start application:** `npm run dev`
4. **Check these pages:**
   - Home: http://localhost:3000
   - Login: http://localhost:3000/login
   - Dashboard: http://localhost:3000/dashboard
   - Register: http://localhost:3000/register

5. **Verify logos appear correctly** on all pages
6. **Check browser console** for any 404 errors

## Troubleshooting:

### If logos don't appear:
1. Verify file names match exactly (case-sensitive)
2. Check files exist in both public directories
3. Clear browser cache (Ctrl+F5)
4. Restart development server

### If update script fails:
1. Manually update the files listed above
2. Replace `/codesprint-logo.png` with `/sprint-ai-logo-main.png`
3. Update alt text to "SPRINT - AI Logo"

---

**Ready to proceed? Save the logo files first, then I'll create the update script!**
