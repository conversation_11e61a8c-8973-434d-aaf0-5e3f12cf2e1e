"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_navmesh_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/navmesh.js":
/*!***********************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/navmesh.js ***!
  \***********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\nvar Module = (function() {\n  var _scriptDir = typeof document !== 'undefined' && document.currentScript ? document.currentScript.src : undefined;\n  \n  return (\nfunction(Module) {\n  Module = Module || {};\n\nvar Module=typeof Module!==\"undefined\"?Module:{};var readyPromiseResolve,readyPromiseReject;Module[\"ready\"]=new Promise(function(resolve,reject){readyPromiseResolve=resolve;readyPromiseReject=reject});var moduleOverrides={};var key;for(key in Module){if(Module.hasOwnProperty(key)){moduleOverrides[key]=Module[key]}}var arguments_=[];var thisProgram=\"./this.program\";var quit_=function(status,toThrow){throw toThrow};var ENVIRONMENT_IS_WEB=true;var ENVIRONMENT_IS_WORKER=false;var scriptDirectory=\"\";function locateFile(path){if(Module[\"locateFile\"]){return Module[\"locateFile\"](path,scriptDirectory)}return scriptDirectory+path}var read_,readAsync,readBinary,setWindowTitle;if(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER){if(ENVIRONMENT_IS_WORKER){scriptDirectory=self.location.href}else if(typeof document!==\"undefined\"&&document.currentScript){scriptDirectory=document.currentScript.src}if(_scriptDir){scriptDirectory=_scriptDir}if(scriptDirectory.indexOf(\"blob:\")!==0){scriptDirectory=scriptDirectory.substr(0,scriptDirectory.replace(/[?#].*/,\"\").lastIndexOf(\"/\")+1)}else{scriptDirectory=\"\"}{read_=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.send(null);return xhr.responseText};if(ENVIRONMENT_IS_WORKER){readBinary=function(url){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,false);xhr.responseType=\"arraybuffer\";xhr.send(null);return new Uint8Array(xhr.response)}}readAsync=function(url,onload,onerror){var xhr=new XMLHttpRequest;xhr.open(\"GET\",url,true);xhr.responseType=\"arraybuffer\";xhr.onload=function(){if(xhr.status==200||xhr.status==0&&xhr.response){onload(xhr.response);return}onerror()};xhr.onerror=onerror;xhr.send(null)}}setWindowTitle=function(title){document.title=title}}else{}var out=Module[\"print\"]||console.log.bind(console);var err=Module[\"printErr\"]||console.warn.bind(console);for(key in moduleOverrides){if(moduleOverrides.hasOwnProperty(key)){Module[key]=moduleOverrides[key]}}moduleOverrides=null;if(Module[\"arguments\"])arguments_=Module[\"arguments\"];if(Module[\"thisProgram\"])thisProgram=Module[\"thisProgram\"];if(Module[\"quit\"])quit_=Module[\"quit\"];function convertJsFunctionToWasm(func,sig){if(typeof WebAssembly.Function===\"function\"){var typeNames={\"i\":\"i32\",\"j\":\"i64\",\"f\":\"f32\",\"d\":\"f64\"};var type={parameters:[],results:sig[0]==\"v\"?[]:[typeNames[sig[0]]]};for(var i=1;i<sig.length;++i){type.parameters.push(typeNames[sig[i]])}return new WebAssembly.Function(type,func)}var typeSection=[1,0,1,96];var sigRet=sig.slice(0,1);var sigParam=sig.slice(1);var typeCodes={\"i\":127,\"j\":126,\"f\":125,\"d\":124};typeSection.push(sigParam.length);for(var i=0;i<sigParam.length;++i){typeSection.push(typeCodes[sigParam[i]])}if(sigRet==\"v\"){typeSection.push(0)}else{typeSection=typeSection.concat([1,typeCodes[sigRet]])}typeSection[1]=typeSection.length-2;var bytes=new Uint8Array([0,97,115,109,1,0,0,0].concat(typeSection,[2,7,1,1,101,1,102,0,0,7,5,1,1,102,0,0]));var module=new WebAssembly.Module(bytes);var instance=new WebAssembly.Instance(module,{\"e\":{\"f\":func}});var wrappedFunc=instance.exports[\"f\"];return wrappedFunc}var freeTableIndexes=[];var functionsInTableMap;function getEmptyTableSlot(){if(freeTableIndexes.length){return freeTableIndexes.pop()}try{wasmTable.grow(1)}catch(err){if(!(err instanceof RangeError)){throw err}throw\"Unable to grow wasm table. Set ALLOW_TABLE_GROWTH.\"}return wasmTable.length-1}function updateTableMap(offset,count){for(var i=offset;i<offset+count;i++){var item=getWasmTableEntry(i);if(item){functionsInTableMap.set(item,i)}}}function addFunction(func,sig){if(!functionsInTableMap){functionsInTableMap=new WeakMap;updateTableMap(0,wasmTable.length)}if(functionsInTableMap.has(func)){return functionsInTableMap.get(func)}var ret=getEmptyTableSlot();try{setWasmTableEntry(ret,func)}catch(err){if(!(err instanceof TypeError)){throw err}var wrapped=convertJsFunctionToWasm(func,sig);setWasmTableEntry(ret,wrapped)}functionsInTableMap.set(func,ret);return ret}var wasmBinary;if(Module[\"wasmBinary\"])wasmBinary=Module[\"wasmBinary\"];var noExitRuntime=Module[\"noExitRuntime\"]||true;if(typeof WebAssembly!==\"object\"){abort(\"no native wasm support detected\")}var wasmMemory;var ABORT=false;var EXITSTATUS;function assert(condition,text){if(!condition){abort(\"Assertion failed: \"+text)}}var UTF8Decoder=typeof TextDecoder!==\"undefined\"?new TextDecoder(\"utf8\"):undefined;function UTF8ArrayToString(heap,idx,maxBytesToRead){var endIdx=idx+maxBytesToRead;var endPtr=idx;while(heap[endPtr]&&!(endPtr>=endIdx))++endPtr;if(endPtr-idx>16&&heap.subarray&&UTF8Decoder){return UTF8Decoder.decode(heap.subarray(idx,endPtr))}else{var str=\"\";while(idx<endPtr){var u0=heap[idx++];if(!(u0&128)){str+=String.fromCharCode(u0);continue}var u1=heap[idx++]&63;if((u0&224)==192){str+=String.fromCharCode((u0&31)<<6|u1);continue}var u2=heap[idx++]&63;if((u0&240)==224){u0=(u0&15)<<12|u1<<6|u2}else{u0=(u0&7)<<18|u1<<12|u2<<6|heap[idx++]&63}if(u0<65536){str+=String.fromCharCode(u0)}else{var ch=u0-65536;str+=String.fromCharCode(55296|ch>>10,56320|ch&1023)}}}return str}function UTF8ToString(ptr,maxBytesToRead){return ptr?UTF8ArrayToString(HEAPU8,ptr,maxBytesToRead):\"\"}function stringToUTF8Array(str,heap,outIdx,maxBytesToWrite){if(!(maxBytesToWrite>0))return 0;var startIdx=outIdx;var endIdx=outIdx+maxBytesToWrite-1;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343){var u1=str.charCodeAt(++i);u=65536+((u&1023)<<10)|u1&1023}if(u<=127){if(outIdx>=endIdx)break;heap[outIdx++]=u}else if(u<=2047){if(outIdx+1>=endIdx)break;heap[outIdx++]=192|u>>6;heap[outIdx++]=128|u&63}else if(u<=65535){if(outIdx+2>=endIdx)break;heap[outIdx++]=224|u>>12;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}else{if(outIdx+3>=endIdx)break;heap[outIdx++]=240|u>>18;heap[outIdx++]=128|u>>12&63;heap[outIdx++]=128|u>>6&63;heap[outIdx++]=128|u&63}}heap[outIdx]=0;return outIdx-startIdx}function lengthBytesUTF8(str){var len=0;for(var i=0;i<str.length;++i){var u=str.charCodeAt(i);if(u>=55296&&u<=57343)u=65536+((u&1023)<<10)|str.charCodeAt(++i)&1023;if(u<=127)++len;else if(u<=2047)len+=2;else if(u<=65535)len+=3;else len+=4}return len}function writeArrayToMemory(array,buffer){HEAP8.set(array,buffer)}function writeAsciiToMemory(str,buffer,dontAddNull){for(var i=0;i<str.length;++i){HEAP8[buffer++>>0]=str.charCodeAt(i)}if(!dontAddNull)HEAP8[buffer>>0]=0}function alignUp(x,multiple){if(x%multiple>0){x+=multiple-x%multiple}return x}var buffer,HEAP8,HEAPU8,HEAP16,HEAPU16,HEAP32,HEAPU32,HEAPF32,HEAPF64;function updateGlobalBufferAndViews(buf){buffer=buf;Module[\"HEAP8\"]=HEAP8=new Int8Array(buf);Module[\"HEAP16\"]=HEAP16=new Int16Array(buf);Module[\"HEAP32\"]=HEAP32=new Int32Array(buf);Module[\"HEAPU8\"]=HEAPU8=new Uint8Array(buf);Module[\"HEAPU16\"]=HEAPU16=new Uint16Array(buf);Module[\"HEAPU32\"]=HEAPU32=new Uint32Array(buf);Module[\"HEAPF32\"]=HEAPF32=new Float32Array(buf);Module[\"HEAPF64\"]=HEAPF64=new Float64Array(buf)}var INITIAL_MEMORY=Module[\"INITIAL_MEMORY\"]||16777216;var wasmTable;var __ATPRERUN__=[];var __ATINIT__=[];var __ATPOSTRUN__=[];var runtimeInitialized=false;function preRun(){if(Module[\"preRun\"]){if(typeof Module[\"preRun\"]==\"function\")Module[\"preRun\"]=[Module[\"preRun\"]];while(Module[\"preRun\"].length){addOnPreRun(Module[\"preRun\"].shift())}}callRuntimeCallbacks(__ATPRERUN__)}function initRuntime(){runtimeInitialized=true;callRuntimeCallbacks(__ATINIT__)}function postRun(){if(Module[\"postRun\"]){if(typeof Module[\"postRun\"]==\"function\")Module[\"postRun\"]=[Module[\"postRun\"]];while(Module[\"postRun\"].length){addOnPostRun(Module[\"postRun\"].shift())}}callRuntimeCallbacks(__ATPOSTRUN__)}function addOnPreRun(cb){__ATPRERUN__.unshift(cb)}function addOnInit(cb){__ATINIT__.unshift(cb)}function addOnPostRun(cb){__ATPOSTRUN__.unshift(cb)}var runDependencies=0;var runDependencyWatcher=null;var dependenciesFulfilled=null;function addRunDependency(id){runDependencies++;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}}function removeRunDependency(id){runDependencies--;if(Module[\"monitorRunDependencies\"]){Module[\"monitorRunDependencies\"](runDependencies)}if(runDependencies==0){if(runDependencyWatcher!==null){clearInterval(runDependencyWatcher);runDependencyWatcher=null}if(dependenciesFulfilled){var callback=dependenciesFulfilled;dependenciesFulfilled=null;callback()}}}Module[\"preloadedImages\"]={};Module[\"preloadedAudios\"]={};function abort(what){{if(Module[\"onAbort\"]){Module[\"onAbort\"](what)}}what=\"Aborted(\"+what+\")\";err(what);ABORT=true;EXITSTATUS=1;what+=\". Build with -s ASSERTIONS=1 for more info.\";var e=new WebAssembly.RuntimeError(what);readyPromiseReject(e);throw e}var dataURIPrefix=\"data:application/octet-stream;base64,\";function isDataURI(filename){return filename.startsWith(dataURIPrefix)}var wasmBinaryFile;wasmBinaryFile=\"navmesh.wasm\";if(!isDataURI(wasmBinaryFile)){wasmBinaryFile=locateFile(wasmBinaryFile)}function getBinary(file){try{if(file==wasmBinaryFile&&wasmBinary){return new Uint8Array(wasmBinary)}if(readBinary){return readBinary(file)}else{throw\"both async and sync fetching of the wasm failed\"}}catch(err){abort(err)}}function getBinaryPromise(){if(!wasmBinary&&(ENVIRONMENT_IS_WEB||ENVIRONMENT_IS_WORKER)){if(typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){if(!response[\"ok\"]){throw\"failed to load wasm binary file at '\"+wasmBinaryFile+\"'\"}return response[\"arrayBuffer\"]()}).catch(function(){return getBinary(wasmBinaryFile)})}}return Promise.resolve().then(function(){return getBinary(wasmBinaryFile)})}function createWasm(){var info={\"a\":asmLibraryArg};function receiveInstance(instance,module){var exports=instance.exports;Module[\"asm\"]=exports;wasmMemory=Module[\"asm\"][\"m\"];updateGlobalBufferAndViews(wasmMemory.buffer);wasmTable=Module[\"asm\"][\"Jb\"];addOnInit(Module[\"asm\"][\"n\"]);removeRunDependency(\"wasm-instantiate\")}addRunDependency(\"wasm-instantiate\");function receiveInstantiationResult(result){receiveInstance(result[\"instance\"])}function instantiateArrayBuffer(receiver){return getBinaryPromise().then(function(binary){return WebAssembly.instantiate(binary,info)}).then(function(instance){return instance}).then(receiver,function(reason){err(\"failed to asynchronously prepare wasm: \"+reason);abort(reason)})}function instantiateAsync(){if(!wasmBinary&&typeof WebAssembly.instantiateStreaming===\"function\"&&!isDataURI(wasmBinaryFile)&&typeof fetch===\"function\"){return fetch(wasmBinaryFile,{credentials:\"same-origin\"}).then(function(response){var result=WebAssembly.instantiateStreaming(response,info);return result.then(receiveInstantiationResult,function(reason){err(\"wasm streaming compile failed: \"+reason);err(\"falling back to ArrayBuffer instantiation\");return instantiateArrayBuffer(receiveInstantiationResult)})})}else{return instantiateArrayBuffer(receiveInstantiationResult)}}if(Module[\"instantiateWasm\"]){try{var exports=Module[\"instantiateWasm\"](info,receiveInstance);return exports}catch(e){err(\"Module.instantiateWasm callback failed with error: \"+e);return false}}instantiateAsync().catch(readyPromiseReject);return{}}function callRuntimeCallbacks(callbacks){while(callbacks.length>0){var callback=callbacks.shift();if(typeof callback==\"function\"){callback(Module);continue}var func=callback.func;if(typeof func===\"number\"){if(callback.arg===undefined){getWasmTableEntry(func)()}else{getWasmTableEntry(func)(callback.arg)}}else{func(callback.arg===undefined?null:callback.arg)}}}var wasmTableMirror=[];function getWasmTableEntry(funcPtr){var func=wasmTableMirror[funcPtr];if(!func){if(funcPtr>=wasmTableMirror.length)wasmTableMirror.length=funcPtr+1;wasmTableMirror[funcPtr]=func=wasmTable.get(funcPtr)}return func}function setWasmTableEntry(idx,func){wasmTable.set(idx,func);wasmTableMirror[idx]=func}function ___cxa_allocate_exception(size){return _malloc(size+16)+16}function ExceptionInfo(excPtr){this.excPtr=excPtr;this.ptr=excPtr-16;this.set_type=function(type){HEAP32[this.ptr+4>>2]=type};this.get_type=function(){return HEAP32[this.ptr+4>>2]};this.set_destructor=function(destructor){HEAP32[this.ptr+8>>2]=destructor};this.get_destructor=function(){return HEAP32[this.ptr+8>>2]};this.set_refcount=function(refcount){HEAP32[this.ptr>>2]=refcount};this.set_caught=function(caught){caught=caught?1:0;HEAP8[this.ptr+12>>0]=caught};this.get_caught=function(){return HEAP8[this.ptr+12>>0]!=0};this.set_rethrown=function(rethrown){rethrown=rethrown?1:0;HEAP8[this.ptr+13>>0]=rethrown};this.get_rethrown=function(){return HEAP8[this.ptr+13>>0]!=0};this.init=function(type,destructor){this.set_type(type);this.set_destructor(destructor);this.set_refcount(0);this.set_caught(false);this.set_rethrown(false)};this.add_ref=function(){var value=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=value+1};this.release_ref=function(){var prev=HEAP32[this.ptr>>2];HEAP32[this.ptr>>2]=prev-1;return prev===1}}var exceptionLast=0;var uncaughtExceptionCount=0;function ___cxa_throw(ptr,type,destructor){var info=new ExceptionInfo(ptr);info.init(type,destructor);exceptionLast=ptr;uncaughtExceptionCount++;throw ptr}function _abort(){abort(\"\")}function _emscripten_memcpy_big(dest,src,num){HEAPU8.copyWithin(dest,src,src+num)}function emscripten_realloc_buffer(size){try{wasmMemory.grow(size-buffer.byteLength+65535>>>16);updateGlobalBufferAndViews(wasmMemory.buffer);return 1}catch(e){}}function _emscripten_resize_heap(requestedSize){var oldSize=HEAPU8.length;requestedSize=requestedSize>>>0;var maxHeapSize=2147483648;if(requestedSize>maxHeapSize){return false}for(var cutDown=1;cutDown<=4;cutDown*=2){var overGrownHeapSize=oldSize*(1+.2/cutDown);overGrownHeapSize=Math.min(overGrownHeapSize,requestedSize+100663296);var newSize=Math.min(maxHeapSize,alignUp(Math.max(requestedSize,overGrownHeapSize),65536));var replacement=emscripten_realloc_buffer(newSize);if(replacement){return true}}return false}var ENV={};function getExecutableName(){return thisProgram||\"./this.program\"}function getEnvStrings(){if(!getEnvStrings.strings){var lang=(typeof navigator===\"object\"&&navigator.languages&&navigator.languages[0]||\"C\").replace(\"-\",\"_\")+\".UTF-8\";var env={\"USER\":\"web_user\",\"LOGNAME\":\"web_user\",\"PATH\":\"/\",\"PWD\":\"/\",\"HOME\":\"/home/<USER>",\"LANG\":lang,\"_\":getExecutableName()};for(var x in ENV){if(ENV[x]===undefined)delete env[x];else env[x]=ENV[x]}var strings=[];for(var x in env){strings.push(x+\"=\"+env[x])}getEnvStrings.strings=strings}return getEnvStrings.strings}var SYSCALLS={mappings:{},buffers:[null,[],[]],printChar:function(stream,curr){var buffer=SYSCALLS.buffers[stream];if(curr===0||curr===10){(stream===1?out:err)(UTF8ArrayToString(buffer,0));buffer.length=0}else{buffer.push(curr)}},varargs:undefined,get:function(){SYSCALLS.varargs+=4;var ret=HEAP32[SYSCALLS.varargs-4>>2];return ret},getStr:function(ptr){var ret=UTF8ToString(ptr);return ret},get64:function(low,high){return low}};function _environ_get(__environ,environ_buf){var bufSize=0;getEnvStrings().forEach(function(string,i){var ptr=environ_buf+bufSize;HEAP32[__environ+i*4>>2]=ptr;writeAsciiToMemory(string,ptr);bufSize+=string.length+1});return 0}function _environ_sizes_get(penviron_count,penviron_buf_size){var strings=getEnvStrings();HEAP32[penviron_count>>2]=strings.length;var bufSize=0;strings.forEach(function(string){bufSize+=string.length+1});HEAP32[penviron_buf_size>>2]=bufSize;return 0}function _fd_close(fd){return 0}function _fd_read(fd,iov,iovcnt,pnum){var stream=SYSCALLS.getStreamFromFD(fd);var num=SYSCALLS.doReadv(stream,iov,iovcnt);HEAP32[pnum>>2]=num;return 0}function _fd_seek(fd,offset_low,offset_high,whence,newOffset){}function _fd_write(fd,iov,iovcnt,pnum){var num=0;for(var i=0;i<iovcnt;i++){var ptr=HEAP32[iov>>2];var len=HEAP32[iov+4>>2];iov+=8;for(var j=0;j<len;j++){SYSCALLS.printChar(fd,HEAPU8[ptr+j])}num+=len}HEAP32[pnum>>2]=num;return 0}function __isLeapYear(year){return year%4===0&&(year%100!==0||year%400===0)}function __arraySum(array,index){var sum=0;for(var i=0;i<=index;sum+=array[i++]){}return sum}var __MONTH_DAYS_LEAP=[31,29,31,30,31,30,31,31,30,31,30,31];var __MONTH_DAYS_REGULAR=[31,28,31,30,31,30,31,31,30,31,30,31];function __addDays(date,days){var newDate=new Date(date.getTime());while(days>0){var leap=__isLeapYear(newDate.getFullYear());var currentMonth=newDate.getMonth();var daysInCurrentMonth=(leap?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR)[currentMonth];if(days>daysInCurrentMonth-newDate.getDate()){days-=daysInCurrentMonth-newDate.getDate()+1;newDate.setDate(1);if(currentMonth<11){newDate.setMonth(currentMonth+1)}else{newDate.setMonth(0);newDate.setFullYear(newDate.getFullYear()+1)}}else{newDate.setDate(newDate.getDate()+days);return newDate}}return newDate}function _strftime(s,maxsize,format,tm){var tm_zone=HEAP32[tm+40>>2];var date={tm_sec:HEAP32[tm>>2],tm_min:HEAP32[tm+4>>2],tm_hour:HEAP32[tm+8>>2],tm_mday:HEAP32[tm+12>>2],tm_mon:HEAP32[tm+16>>2],tm_year:HEAP32[tm+20>>2],tm_wday:HEAP32[tm+24>>2],tm_yday:HEAP32[tm+28>>2],tm_isdst:HEAP32[tm+32>>2],tm_gmtoff:HEAP32[tm+36>>2],tm_zone:tm_zone?UTF8ToString(tm_zone):\"\"};var pattern=UTF8ToString(format);var EXPANSION_RULES_1={\"%c\":\"%a %b %d %H:%M:%S %Y\",\"%D\":\"%m/%d/%y\",\"%F\":\"%Y-%m-%d\",\"%h\":\"%b\",\"%r\":\"%I:%M:%S %p\",\"%R\":\"%H:%M\",\"%T\":\"%H:%M:%S\",\"%x\":\"%m/%d/%y\",\"%X\":\"%H:%M:%S\",\"%Ec\":\"%c\",\"%EC\":\"%C\",\"%Ex\":\"%m/%d/%y\",\"%EX\":\"%H:%M:%S\",\"%Ey\":\"%y\",\"%EY\":\"%Y\",\"%Od\":\"%d\",\"%Oe\":\"%e\",\"%OH\":\"%H\",\"%OI\":\"%I\",\"%Om\":\"%m\",\"%OM\":\"%M\",\"%OS\":\"%S\",\"%Ou\":\"%u\",\"%OU\":\"%U\",\"%OV\":\"%V\",\"%Ow\":\"%w\",\"%OW\":\"%W\",\"%Oy\":\"%y\"};for(var rule in EXPANSION_RULES_1){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_1[rule])}var WEEKDAYS=[\"Sunday\",\"Monday\",\"Tuesday\",\"Wednesday\",\"Thursday\",\"Friday\",\"Saturday\"];var MONTHS=[\"January\",\"February\",\"March\",\"April\",\"May\",\"June\",\"July\",\"August\",\"September\",\"October\",\"November\",\"December\"];function leadingSomething(value,digits,character){var str=typeof value===\"number\"?value.toString():value||\"\";while(str.length<digits){str=character[0]+str}return str}function leadingNulls(value,digits){return leadingSomething(value,digits,\"0\")}function compareByDay(date1,date2){function sgn(value){return value<0?-1:value>0?1:0}var compare;if((compare=sgn(date1.getFullYear()-date2.getFullYear()))===0){if((compare=sgn(date1.getMonth()-date2.getMonth()))===0){compare=sgn(date1.getDate()-date2.getDate())}}return compare}function getFirstWeekStartDate(janFourth){switch(janFourth.getDay()){case 0:return new Date(janFourth.getFullYear()-1,11,29);case 1:return janFourth;case 2:return new Date(janFourth.getFullYear(),0,3);case 3:return new Date(janFourth.getFullYear(),0,2);case 4:return new Date(janFourth.getFullYear(),0,1);case 5:return new Date(janFourth.getFullYear()-1,11,31);case 6:return new Date(janFourth.getFullYear()-1,11,30)}}function getWeekBasedYear(date){var thisDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);var janFourthThisYear=new Date(thisDate.getFullYear(),0,4);var janFourthNextYear=new Date(thisDate.getFullYear()+1,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);if(compareByDay(firstWeekStartThisYear,thisDate)<=0){if(compareByDay(firstWeekStartNextYear,thisDate)<=0){return thisDate.getFullYear()+1}else{return thisDate.getFullYear()}}else{return thisDate.getFullYear()-1}}var EXPANSION_RULES_2={\"%a\":function(date){return WEEKDAYS[date.tm_wday].substring(0,3)},\"%A\":function(date){return WEEKDAYS[date.tm_wday]},\"%b\":function(date){return MONTHS[date.tm_mon].substring(0,3)},\"%B\":function(date){return MONTHS[date.tm_mon]},\"%C\":function(date){var year=date.tm_year+1900;return leadingNulls(year/100|0,2)},\"%d\":function(date){return leadingNulls(date.tm_mday,2)},\"%e\":function(date){return leadingSomething(date.tm_mday,2,\" \")},\"%g\":function(date){return getWeekBasedYear(date).toString().substring(2)},\"%G\":function(date){return getWeekBasedYear(date)},\"%H\":function(date){return leadingNulls(date.tm_hour,2)},\"%I\":function(date){var twelveHour=date.tm_hour;if(twelveHour==0)twelveHour=12;else if(twelveHour>12)twelveHour-=12;return leadingNulls(twelveHour,2)},\"%j\":function(date){return leadingNulls(date.tm_mday+__arraySum(__isLeapYear(date.tm_year+1900)?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,date.tm_mon-1),3)},\"%m\":function(date){return leadingNulls(date.tm_mon+1,2)},\"%M\":function(date){return leadingNulls(date.tm_min,2)},\"%n\":function(){return\"\\n\"},\"%p\":function(date){if(date.tm_hour>=0&&date.tm_hour<12){return\"AM\"}else{return\"PM\"}},\"%S\":function(date){return leadingNulls(date.tm_sec,2)},\"%t\":function(){return\"\\t\"},\"%u\":function(date){return date.tm_wday||7},\"%U\":function(date){var janFirst=new Date(date.tm_year+1900,0,1);var firstSunday=janFirst.getDay()===0?janFirst:__addDays(janFirst,7-janFirst.getDay());var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstSunday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstSundayUntilEndJanuary=31-firstSunday.getDate();var days=firstSundayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstSunday,janFirst)===0?\"01\":\"00\"},\"%V\":function(date){var janFourthThisYear=new Date(date.tm_year+1900,0,4);var janFourthNextYear=new Date(date.tm_year+1901,0,4);var firstWeekStartThisYear=getFirstWeekStartDate(janFourthThisYear);var firstWeekStartNextYear=getFirstWeekStartDate(janFourthNextYear);var endDate=__addDays(new Date(date.tm_year+1900,0,1),date.tm_yday);if(compareByDay(endDate,firstWeekStartThisYear)<0){return\"53\"}if(compareByDay(firstWeekStartNextYear,endDate)<=0){return\"01\"}var daysDifference;if(firstWeekStartThisYear.getFullYear()<date.tm_year+1900){daysDifference=date.tm_yday+32-firstWeekStartThisYear.getDate()}else{daysDifference=date.tm_yday+1-firstWeekStartThisYear.getDate()}return leadingNulls(Math.ceil(daysDifference/7),2)},\"%w\":function(date){return date.tm_wday},\"%W\":function(date){var janFirst=new Date(date.tm_year,0,1);var firstMonday=janFirst.getDay()===1?janFirst:__addDays(janFirst,janFirst.getDay()===0?1:7-janFirst.getDay()+1);var endDate=new Date(date.tm_year+1900,date.tm_mon,date.tm_mday);if(compareByDay(firstMonday,endDate)<0){var februaryFirstUntilEndMonth=__arraySum(__isLeapYear(endDate.getFullYear())?__MONTH_DAYS_LEAP:__MONTH_DAYS_REGULAR,endDate.getMonth()-1)-31;var firstMondayUntilEndJanuary=31-firstMonday.getDate();var days=firstMondayUntilEndJanuary+februaryFirstUntilEndMonth+endDate.getDate();return leadingNulls(Math.ceil(days/7),2)}return compareByDay(firstMonday,janFirst)===0?\"01\":\"00\"},\"%y\":function(date){return(date.tm_year+1900).toString().substring(2)},\"%Y\":function(date){return date.tm_year+1900},\"%z\":function(date){var off=date.tm_gmtoff;var ahead=off>=0;off=Math.abs(off)/60;off=off/60*100+off%60;return(ahead?\"+\":\"-\")+String(\"0000\"+off).slice(-4)},\"%Z\":function(date){return date.tm_zone},\"%%\":function(){return\"%\"}};for(var rule in EXPANSION_RULES_2){if(pattern.includes(rule)){pattern=pattern.replace(new RegExp(rule,\"g\"),EXPANSION_RULES_2[rule](date))}}var bytes=intArrayFromString(pattern,false);if(bytes.length>maxsize){return 0}writeArrayToMemory(bytes,s);return bytes.length-1}function _strftime_l(s,maxsize,format,tm){return _strftime(s,maxsize,format,tm)}function intArrayFromString(stringy,dontAddNull,length){var len=length>0?length:lengthBytesUTF8(stringy)+1;var u8array=new Array(len);var numBytesWritten=stringToUTF8Array(stringy,u8array,0,u8array.length);if(dontAddNull)u8array.length=numBytesWritten;return u8array}var asmLibraryArg={\"l\":___cxa_allocate_exception,\"k\":___cxa_throw,\"b\":_abort,\"j\":_emscripten_memcpy_big,\"a\":_emscripten_resize_heap,\"g\":_environ_get,\"h\":_environ_sizes_get,\"c\":_fd_close,\"e\":_fd_read,\"i\":_fd_seek,\"d\":_fd_write,\"f\":_strftime_l};var asm=createWasm();var ___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=function(){return(___wasm_call_ctors=Module[\"___wasm_call_ctors\"]=Module[\"asm\"][\"n\"]).apply(null,arguments)};var _emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=function(){return(_emscripten_bind_VoidPtr___destroy___0=Module[\"_emscripten_bind_VoidPtr___destroy___0\"]=Module[\"asm\"][\"o\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=function(){return(_emscripten_bind_rcConfig_rcConfig_0=Module[\"_emscripten_bind_rcConfig_rcConfig_0\"]=Module[\"asm\"][\"p\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=function(){return(_emscripten_bind_rcConfig_get_width_0=Module[\"_emscripten_bind_rcConfig_get_width_0\"]=Module[\"asm\"][\"q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=function(){return(_emscripten_bind_rcConfig_set_width_1=Module[\"_emscripten_bind_rcConfig_set_width_1\"]=Module[\"asm\"][\"r\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=function(){return(_emscripten_bind_rcConfig_get_height_0=Module[\"_emscripten_bind_rcConfig_get_height_0\"]=Module[\"asm\"][\"s\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=function(){return(_emscripten_bind_rcConfig_set_height_1=Module[\"_emscripten_bind_rcConfig_set_height_1\"]=Module[\"asm\"][\"t\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_tileSize_0=Module[\"_emscripten_bind_rcConfig_get_tileSize_0\"]=Module[\"asm\"][\"u\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_tileSize_1=Module[\"_emscripten_bind_rcConfig_set_tileSize_1\"]=Module[\"asm\"][\"v\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=function(){return(_emscripten_bind_rcConfig_get_borderSize_0=Module[\"_emscripten_bind_rcConfig_get_borderSize_0\"]=Module[\"asm\"][\"w\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=function(){return(_emscripten_bind_rcConfig_set_borderSize_1=Module[\"_emscripten_bind_rcConfig_set_borderSize_1\"]=Module[\"asm\"][\"x\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=function(){return(_emscripten_bind_rcConfig_get_cs_0=Module[\"_emscripten_bind_rcConfig_get_cs_0\"]=Module[\"asm\"][\"y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=function(){return(_emscripten_bind_rcConfig_set_cs_1=Module[\"_emscripten_bind_rcConfig_set_cs_1\"]=Module[\"asm\"][\"z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=function(){return(_emscripten_bind_rcConfig_get_ch_0=Module[\"_emscripten_bind_rcConfig_get_ch_0\"]=Module[\"asm\"][\"A\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=function(){return(_emscripten_bind_rcConfig_set_ch_1=Module[\"_emscripten_bind_rcConfig_set_ch_1\"]=Module[\"asm\"][\"B\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmin_1=Module[\"_emscripten_bind_rcConfig_get_bmin_1\"]=Module[\"asm\"][\"C\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmin_2=Module[\"_emscripten_bind_rcConfig_set_bmin_2\"]=Module[\"asm\"][\"D\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=function(){return(_emscripten_bind_rcConfig_get_bmax_1=Module[\"_emscripten_bind_rcConfig_get_bmax_1\"]=Module[\"asm\"][\"E\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=function(){return(_emscripten_bind_rcConfig_set_bmax_2=Module[\"_emscripten_bind_rcConfig_set_bmax_2\"]=Module[\"asm\"][\"F\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableSlopeAngle_0=Module[\"_emscripten_bind_rcConfig_get_walkableSlopeAngle_0\"]=Module[\"asm\"][\"G\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableSlopeAngle_1=Module[\"_emscripten_bind_rcConfig_set_walkableSlopeAngle_1\"]=Module[\"asm\"][\"H\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableHeight_0=Module[\"_emscripten_bind_rcConfig_get_walkableHeight_0\"]=Module[\"asm\"][\"I\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableHeight_1=Module[\"_emscripten_bind_rcConfig_set_walkableHeight_1\"]=Module[\"asm\"][\"J\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableClimb_0=Module[\"_emscripten_bind_rcConfig_get_walkableClimb_0\"]=Module[\"asm\"][\"K\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableClimb_1=Module[\"_emscripten_bind_rcConfig_set_walkableClimb_1\"]=Module[\"asm\"][\"L\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=function(){return(_emscripten_bind_rcConfig_get_walkableRadius_0=Module[\"_emscripten_bind_rcConfig_get_walkableRadius_0\"]=Module[\"asm\"][\"M\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=function(){return(_emscripten_bind_rcConfig_set_walkableRadius_1=Module[\"_emscripten_bind_rcConfig_set_walkableRadius_1\"]=Module[\"asm\"][\"N\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxEdgeLen_0=Module[\"_emscripten_bind_rcConfig_get_maxEdgeLen_0\"]=Module[\"asm\"][\"O\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxEdgeLen_1=Module[\"_emscripten_bind_rcConfig_set_maxEdgeLen_1\"]=Module[\"asm\"][\"P\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxSimplificationError_0=Module[\"_emscripten_bind_rcConfig_get_maxSimplificationError_0\"]=Module[\"asm\"][\"Q\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxSimplificationError_1=Module[\"_emscripten_bind_rcConfig_set_maxSimplificationError_1\"]=Module[\"asm\"][\"R\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_minRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_minRegionArea_0\"]=Module[\"asm\"][\"S\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_minRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_minRegionArea_1\"]=Module[\"asm\"][\"T\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=function(){return(_emscripten_bind_rcConfig_get_mergeRegionArea_0=Module[\"_emscripten_bind_rcConfig_get_mergeRegionArea_0\"]=Module[\"asm\"][\"U\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=function(){return(_emscripten_bind_rcConfig_set_mergeRegionArea_1=Module[\"_emscripten_bind_rcConfig_set_mergeRegionArea_1\"]=Module[\"asm\"][\"V\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=function(){return(_emscripten_bind_rcConfig_get_maxVertsPerPoly_0=Module[\"_emscripten_bind_rcConfig_get_maxVertsPerPoly_0\"]=Module[\"asm\"][\"W\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=function(){return(_emscripten_bind_rcConfig_set_maxVertsPerPoly_1=Module[\"_emscripten_bind_rcConfig_set_maxVertsPerPoly_1\"]=Module[\"asm\"][\"X\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleDist_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleDist_0\"]=Module[\"asm\"][\"Y\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleDist_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleDist_1\"]=Module[\"asm\"][\"Z\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=function(){return(_emscripten_bind_rcConfig_get_detailSampleMaxError_0=Module[\"_emscripten_bind_rcConfig_get_detailSampleMaxError_0\"]=Module[\"asm\"][\"_\"]).apply(null,arguments)};var _emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=function(){return(_emscripten_bind_rcConfig_set_detailSampleMaxError_1=Module[\"_emscripten_bind_rcConfig_set_detailSampleMaxError_1\"]=Module[\"asm\"][\"$\"]).apply(null,arguments)};var _emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=function(){return(_emscripten_bind_rcConfig___destroy___0=Module[\"_emscripten_bind_rcConfig___destroy___0\"]=Module[\"asm\"][\"aa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=function(){return(_emscripten_bind_Vec3_Vec3_0=Module[\"_emscripten_bind_Vec3_Vec3_0\"]=Module[\"asm\"][\"ba\"]).apply(null,arguments)};var _emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=function(){return(_emscripten_bind_Vec3_Vec3_3=Module[\"_emscripten_bind_Vec3_Vec3_3\"]=Module[\"asm\"][\"ca\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=function(){return(_emscripten_bind_Vec3_get_x_0=Module[\"_emscripten_bind_Vec3_get_x_0\"]=Module[\"asm\"][\"da\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=function(){return(_emscripten_bind_Vec3_set_x_1=Module[\"_emscripten_bind_Vec3_set_x_1\"]=Module[\"asm\"][\"ea\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=function(){return(_emscripten_bind_Vec3_get_y_0=Module[\"_emscripten_bind_Vec3_get_y_0\"]=Module[\"asm\"][\"fa\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=function(){return(_emscripten_bind_Vec3_set_y_1=Module[\"_emscripten_bind_Vec3_set_y_1\"]=Module[\"asm\"][\"ga\"]).apply(null,arguments)};var _emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=function(){return(_emscripten_bind_Vec3_get_z_0=Module[\"_emscripten_bind_Vec3_get_z_0\"]=Module[\"asm\"][\"ha\"]).apply(null,arguments)};var _emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=function(){return(_emscripten_bind_Vec3_set_z_1=Module[\"_emscripten_bind_Vec3_set_z_1\"]=Module[\"asm\"][\"ia\"]).apply(null,arguments)};var _emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=function(){return(_emscripten_bind_Vec3___destroy___0=Module[\"_emscripten_bind_Vec3___destroy___0\"]=Module[\"asm\"][\"ja\"]).apply(null,arguments)};var _emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=function(){return(_emscripten_bind_Triangle_Triangle_0=Module[\"_emscripten_bind_Triangle_Triangle_0\"]=Module[\"asm\"][\"ka\"]).apply(null,arguments)};var _emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=function(){return(_emscripten_bind_Triangle_getPoint_1=Module[\"_emscripten_bind_Triangle_getPoint_1\"]=Module[\"asm\"][\"la\"]).apply(null,arguments)};var _emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=function(){return(_emscripten_bind_Triangle___destroy___0=Module[\"_emscripten_bind_Triangle___destroy___0\"]=Module[\"asm\"][\"ma\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=function(){return(_emscripten_bind_DebugNavMesh_DebugNavMesh_0=Module[\"_emscripten_bind_DebugNavMesh_DebugNavMesh_0\"]=Module[\"asm\"][\"na\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangleCount_0=Module[\"_emscripten_bind_DebugNavMesh_getTriangleCount_0\"]=Module[\"asm\"][\"oa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=function(){return(_emscripten_bind_DebugNavMesh_getTriangle_1=Module[\"_emscripten_bind_DebugNavMesh_getTriangle_1\"]=Module[\"asm\"][\"pa\"]).apply(null,arguments)};var _emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=function(){return(_emscripten_bind_DebugNavMesh___destroy___0=Module[\"_emscripten_bind_DebugNavMesh___destroy___0\"]=Module[\"asm\"][\"qa\"]).apply(null,arguments)};var _emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=function(){return(_emscripten_bind_dtNavMesh___destroy___0=Module[\"_emscripten_bind_dtNavMesh___destroy___0\"]=Module[\"asm\"][\"ra\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=function(){return(_emscripten_bind_NavmeshData_NavmeshData_0=Module[\"_emscripten_bind_NavmeshData_NavmeshData_0\"]=Module[\"asm\"][\"sa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=function(){return(_emscripten_bind_NavmeshData_get_dataPointer_0=Module[\"_emscripten_bind_NavmeshData_get_dataPointer_0\"]=Module[\"asm\"][\"ta\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=function(){return(_emscripten_bind_NavmeshData_set_dataPointer_1=Module[\"_emscripten_bind_NavmeshData_set_dataPointer_1\"]=Module[\"asm\"][\"ua\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=function(){return(_emscripten_bind_NavmeshData_get_size_0=Module[\"_emscripten_bind_NavmeshData_get_size_0\"]=Module[\"asm\"][\"va\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=function(){return(_emscripten_bind_NavmeshData_set_size_1=Module[\"_emscripten_bind_NavmeshData_set_size_1\"]=Module[\"asm\"][\"wa\"]).apply(null,arguments)};var _emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=function(){return(_emscripten_bind_NavmeshData___destroy___0=Module[\"_emscripten_bind_NavmeshData___destroy___0\"]=Module[\"asm\"][\"xa\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=function(){return(_emscripten_bind_NavPath_getPointCount_0=Module[\"_emscripten_bind_NavPath_getPointCount_0\"]=Module[\"asm\"][\"ya\"]).apply(null,arguments)};var _emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=function(){return(_emscripten_bind_NavPath_getPoint_1=Module[\"_emscripten_bind_NavPath_getPoint_1\"]=Module[\"asm\"][\"za\"]).apply(null,arguments)};var _emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=function(){return(_emscripten_bind_NavPath___destroy___0=Module[\"_emscripten_bind_NavPath___destroy___0\"]=Module[\"asm\"][\"Aa\"]).apply(null,arguments)};var _emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=function(){return(_emscripten_bind_dtObstacleRef___destroy___0=Module[\"_emscripten_bind_dtObstacleRef___destroy___0\"]=Module[\"asm\"][\"Ba\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0=Module[\"_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0\"]=Module[\"asm\"][\"Ca\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_radius_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_radius_0\"]=Module[\"asm\"][\"Da\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_radius_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_radius_1\"]=Module[\"asm\"][\"Ea\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_height_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_height_0\"]=Module[\"asm\"][\"Fa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_height_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_height_1\"]=Module[\"asm\"][\"Ga\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0\"]=Module[\"asm\"][\"Ha\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1\"]=Module[\"asm\"][\"Ia\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0\"]=Module[\"asm\"][\"Ja\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1\"]=Module[\"asm\"][\"Ka\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0\"]=Module[\"asm\"][\"La\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1\"]=Module[\"asm\"][\"Ma\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0\"]=Module[\"asm\"][\"Na\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1\"]=Module[\"asm\"][\"Oa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_separationWeight_0\"]=Module[\"asm\"][\"Pa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1\"]=Module[\"asm\"][\"Qa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_updateFlags_0\"]=Module[\"asm\"][\"Ra\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1\"]=Module[\"asm\"][\"Sa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0\"]=Module[\"asm\"][\"Ta\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1\"]=Module[\"asm\"][\"Ua\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0\"]=Module[\"asm\"][\"Va\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1\"]=Module[\"asm\"][\"Wa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_get_userData_0=Module[\"_emscripten_bind_dtCrowdAgentParams_get_userData_0\"]=Module[\"asm\"][\"Xa\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=function(){return(_emscripten_bind_dtCrowdAgentParams_set_userData_1=Module[\"_emscripten_bind_dtCrowdAgentParams_set_userData_1\"]=Module[\"asm\"][\"Ya\"]).apply(null,arguments)};var _emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=function(){return(_emscripten_bind_dtCrowdAgentParams___destroy___0=Module[\"_emscripten_bind_dtCrowdAgentParams___destroy___0\"]=Module[\"asm\"][\"Za\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_NavMesh_0=Module[\"_emscripten_bind_NavMesh_NavMesh_0\"]=Module[\"asm\"][\"_a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=function(){return(_emscripten_bind_NavMesh_destroy_0=Module[\"_emscripten_bind_NavMesh_destroy_0\"]=Module[\"asm\"][\"$a\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=function(){return(_emscripten_bind_NavMesh_build_5=Module[\"_emscripten_bind_NavMesh_build_5\"]=Module[\"asm\"][\"ab\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_buildFromNavmeshData_1=Module[\"_emscripten_bind_NavMesh_buildFromNavmeshData_1\"]=Module[\"asm\"][\"bb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=function(){return(_emscripten_bind_NavMesh_getNavmeshData_0=Module[\"_emscripten_bind_NavMesh_getNavmeshData_0\"]=Module[\"asm\"][\"cb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=function(){return(_emscripten_bind_NavMesh_freeNavmeshData_1=Module[\"_emscripten_bind_NavMesh_freeNavmeshData_1\"]=Module[\"asm\"][\"db\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getDebugNavMesh_0=Module[\"_emscripten_bind_NavMesh_getDebugNavMesh_0\"]=Module[\"asm\"][\"eb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=function(){return(_emscripten_bind_NavMesh_getClosestPoint_1=Module[\"_emscripten_bind_NavMesh_getClosestPoint_1\"]=Module[\"asm\"][\"fb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=function(){return(_emscripten_bind_NavMesh_getRandomPointAround_2=Module[\"_emscripten_bind_NavMesh_getRandomPointAround_2\"]=Module[\"asm\"][\"gb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=function(){return(_emscripten_bind_NavMesh_moveAlong_2=Module[\"_emscripten_bind_NavMesh_moveAlong_2\"]=Module[\"asm\"][\"hb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=function(){return(_emscripten_bind_NavMesh_getNavMesh_0=Module[\"_emscripten_bind_NavMesh_getNavMesh_0\"]=Module[\"asm\"][\"ib\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=function(){return(_emscripten_bind_NavMesh_computePath_2=Module[\"_emscripten_bind_NavMesh_computePath_2\"]=Module[\"asm\"][\"jb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_NavMesh_setDefaultQueryExtent_1=Module[\"_emscripten_bind_NavMesh_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"kb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_NavMesh_getDefaultQueryExtent_0=Module[\"_emscripten_bind_NavMesh_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"lb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addCylinderObstacle_3=Module[\"_emscripten_bind_NavMesh_addCylinderObstacle_3\"]=Module[\"asm\"][\"mb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=function(){return(_emscripten_bind_NavMesh_addBoxObstacle_3=Module[\"_emscripten_bind_NavMesh_addBoxObstacle_3\"]=Module[\"asm\"][\"nb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=function(){return(_emscripten_bind_NavMesh_removeObstacle_1=Module[\"_emscripten_bind_NavMesh_removeObstacle_1\"]=Module[\"asm\"][\"ob\"]).apply(null,arguments)};var _emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=function(){return(_emscripten_bind_NavMesh_update_0=Module[\"_emscripten_bind_NavMesh_update_0\"]=Module[\"asm\"][\"pb\"]).apply(null,arguments)};var _emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=function(){return(_emscripten_bind_NavMesh___destroy___0=Module[\"_emscripten_bind_NavMesh___destroy___0\"]=Module[\"asm\"][\"qb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=function(){return(_emscripten_bind_Crowd_Crowd_3=Module[\"_emscripten_bind_Crowd_Crowd_3\"]=Module[\"asm\"][\"rb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=function(){return(_emscripten_bind_Crowd_destroy_0=Module[\"_emscripten_bind_Crowd_destroy_0\"]=Module[\"asm\"][\"sb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=function(){return(_emscripten_bind_Crowd_addAgent_2=Module[\"_emscripten_bind_Crowd_addAgent_2\"]=Module[\"asm\"][\"tb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=function(){return(_emscripten_bind_Crowd_removeAgent_1=Module[\"_emscripten_bind_Crowd_removeAgent_1\"]=Module[\"asm\"][\"ub\"]).apply(null,arguments)};var _emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=function(){return(_emscripten_bind_Crowd_update_1=Module[\"_emscripten_bind_Crowd_update_1\"]=Module[\"asm\"][\"vb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=function(){return(_emscripten_bind_Crowd_getAgentPosition_1=Module[\"_emscripten_bind_Crowd_getAgentPosition_1\"]=Module[\"asm\"][\"wb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=function(){return(_emscripten_bind_Crowd_getAgentVelocity_1=Module[\"_emscripten_bind_Crowd_getAgentVelocity_1\"]=Module[\"asm\"][\"xb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=function(){return(_emscripten_bind_Crowd_getAgentNextTargetPath_1=Module[\"_emscripten_bind_Crowd_getAgentNextTargetPath_1\"]=Module[\"asm\"][\"yb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=function(){return(_emscripten_bind_Crowd_getAgentState_1=Module[\"_emscripten_bind_Crowd_getAgentState_1\"]=Module[\"asm\"][\"zb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=function(){return(_emscripten_bind_Crowd_overOffmeshConnection_1=Module[\"_emscripten_bind_Crowd_overOffmeshConnection_1\"]=Module[\"asm\"][\"Ab\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=function(){return(_emscripten_bind_Crowd_agentGoto_2=Module[\"_emscripten_bind_Crowd_agentGoto_2\"]=Module[\"asm\"][\"Bb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=function(){return(_emscripten_bind_Crowd_agentTeleport_2=Module[\"_emscripten_bind_Crowd_agentTeleport_2\"]=Module[\"asm\"][\"Cb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=function(){return(_emscripten_bind_Crowd_getAgentParameters_1=Module[\"_emscripten_bind_Crowd_getAgentParameters_1\"]=Module[\"asm\"][\"Db\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=function(){return(_emscripten_bind_Crowd_setAgentParameters_2=Module[\"_emscripten_bind_Crowd_setAgentParameters_2\"]=Module[\"asm\"][\"Eb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=function(){return(_emscripten_bind_Crowd_setDefaultQueryExtent_1=Module[\"_emscripten_bind_Crowd_setDefaultQueryExtent_1\"]=Module[\"asm\"][\"Fb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=function(){return(_emscripten_bind_Crowd_getDefaultQueryExtent_0=Module[\"_emscripten_bind_Crowd_getDefaultQueryExtent_0\"]=Module[\"asm\"][\"Gb\"]).apply(null,arguments)};var _emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=function(){return(_emscripten_bind_Crowd_getCorners_1=Module[\"_emscripten_bind_Crowd_getCorners_1\"]=Module[\"asm\"][\"Hb\"]).apply(null,arguments)};var _emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=function(){return(_emscripten_bind_Crowd___destroy___0=Module[\"_emscripten_bind_Crowd___destroy___0\"]=Module[\"asm\"][\"Ib\"]).apply(null,arguments)};var _malloc=Module[\"_malloc\"]=function(){return(_malloc=Module[\"_malloc\"]=Module[\"asm\"][\"Kb\"]).apply(null,arguments)};var _free=Module[\"_free\"]=function(){return(_free=Module[\"_free\"]=Module[\"asm\"][\"Lb\"]).apply(null,arguments)};Module[\"UTF8ToString\"]=UTF8ToString;Module[\"addFunction\"]=addFunction;var calledRun;dependenciesFulfilled=function runCaller(){if(!calledRun)run();if(!calledRun)dependenciesFulfilled=runCaller};function run(args){args=args||arguments_;if(runDependencies>0){return}preRun();if(runDependencies>0){return}function doRun(){if(calledRun)return;calledRun=true;Module[\"calledRun\"]=true;if(ABORT)return;initRuntime();readyPromiseResolve(Module);if(Module[\"onRuntimeInitialized\"])Module[\"onRuntimeInitialized\"]();postRun()}if(Module[\"setStatus\"]){Module[\"setStatus\"](\"Running...\");setTimeout(function(){setTimeout(function(){Module[\"setStatus\"](\"\")},1);doRun()},1)}else{doRun()}}Module[\"run\"]=run;if(Module[\"preInit\"]){if(typeof Module[\"preInit\"]==\"function\")Module[\"preInit\"]=[Module[\"preInit\"]];while(Module[\"preInit\"].length>0){Module[\"preInit\"].pop()()}}run();function WrapperObject(){}WrapperObject.prototype=Object.create(WrapperObject.prototype);WrapperObject.prototype.constructor=WrapperObject;WrapperObject.prototype.__class__=WrapperObject;WrapperObject.__cache__={};Module[\"WrapperObject\"]=WrapperObject;function getCache(__class__){return(__class__||WrapperObject).__cache__}Module[\"getCache\"]=getCache;function wrapPointer(ptr,__class__){var cache=getCache(__class__);var ret=cache[ptr];if(ret)return ret;ret=Object.create((__class__||WrapperObject).prototype);ret.ptr=ptr;return cache[ptr]=ret}Module[\"wrapPointer\"]=wrapPointer;function castObject(obj,__class__){return wrapPointer(obj.ptr,__class__)}Module[\"castObject\"]=castObject;Module[\"NULL\"]=wrapPointer(0);function destroy(obj){if(!obj[\"__destroy__\"])throw\"Error: Cannot destroy object. (Did you create it yourself?)\";obj[\"__destroy__\"]();delete getCache(obj.__class__)[obj.ptr]}Module[\"destroy\"]=destroy;function compare(obj1,obj2){return obj1.ptr===obj2.ptr}Module[\"compare\"]=compare;function getPointer(obj){return obj.ptr}Module[\"getPointer\"]=getPointer;function getClass(obj){return obj.__class__}Module[\"getClass\"]=getClass;var ensureCache={buffer:0,size:0,pos:0,temps:[],needed:0,prepare:function(){if(ensureCache.needed){for(var i=0;i<ensureCache.temps.length;i++){Module[\"_free\"](ensureCache.temps[i])}ensureCache.temps.length=0;Module[\"_free\"](ensureCache.buffer);ensureCache.buffer=0;ensureCache.size+=ensureCache.needed;ensureCache.needed=0}if(!ensureCache.buffer){ensureCache.size+=128;ensureCache.buffer=Module[\"_malloc\"](ensureCache.size);assert(ensureCache.buffer)}ensureCache.pos=0},alloc:function(array,view){assert(ensureCache.buffer);var bytes=view.BYTES_PER_ELEMENT;var len=array.length*bytes;len=len+7&-8;var ret;if(ensureCache.pos+len>=ensureCache.size){assert(len>0);ensureCache.needed+=len;ret=Module[\"_malloc\"](len);ensureCache.temps.push(ret)}else{ret=ensureCache.buffer+ensureCache.pos;ensureCache.pos+=len}return ret},copy:function(array,view,offset){offset>>>=0;var bytes=view.BYTES_PER_ELEMENT;switch(bytes){case 2:offset>>>=1;break;case 4:offset>>>=2;break;case 8:offset>>>=3;break}for(var i=0;i<array.length;i++){view[offset+i]=array[i]}}};function ensureInt32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAP32);ensureCache.copy(value,HEAP32,offset);return offset}return value}function ensureFloat32(value){if(typeof value===\"object\"){var offset=ensureCache.alloc(value,HEAPF32);ensureCache.copy(value,HEAPF32,offset);return offset}return value}function VoidPtr(){throw\"cannot construct a VoidPtr, no constructor in IDL\"}VoidPtr.prototype=Object.create(WrapperObject.prototype);VoidPtr.prototype.constructor=VoidPtr;VoidPtr.prototype.__class__=VoidPtr;VoidPtr.__cache__={};Module[\"VoidPtr\"]=VoidPtr;VoidPtr.prototype[\"__destroy__\"]=VoidPtr.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_VoidPtr___destroy___0(self)};function rcConfig(){this.ptr=_emscripten_bind_rcConfig_rcConfig_0();getCache(rcConfig)[this.ptr]=this}rcConfig.prototype=Object.create(WrapperObject.prototype);rcConfig.prototype.constructor=rcConfig;rcConfig.prototype.__class__=rcConfig;rcConfig.__cache__={};Module[\"rcConfig\"]=rcConfig;rcConfig.prototype[\"get_width\"]=rcConfig.prototype.get_width=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_width_0(self)};rcConfig.prototype[\"set_width\"]=rcConfig.prototype.set_width=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_width_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"width\",{get:rcConfig.prototype.get_width,set:rcConfig.prototype.set_width});rcConfig.prototype[\"get_height\"]=rcConfig.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_height_0(self)};rcConfig.prototype[\"set_height\"]=rcConfig.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_height_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"height\",{get:rcConfig.prototype.get_height,set:rcConfig.prototype.set_height});rcConfig.prototype[\"get_tileSize\"]=rcConfig.prototype.get_tileSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_tileSize_0(self)};rcConfig.prototype[\"set_tileSize\"]=rcConfig.prototype.set_tileSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_tileSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"tileSize\",{get:rcConfig.prototype.get_tileSize,set:rcConfig.prototype.set_tileSize});rcConfig.prototype[\"get_borderSize\"]=rcConfig.prototype.get_borderSize=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_borderSize_0(self)};rcConfig.prototype[\"set_borderSize\"]=rcConfig.prototype.set_borderSize=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_borderSize_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"borderSize\",{get:rcConfig.prototype.get_borderSize,set:rcConfig.prototype.set_borderSize});rcConfig.prototype[\"get_cs\"]=rcConfig.prototype.get_cs=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_cs_0(self)};rcConfig.prototype[\"set_cs\"]=rcConfig.prototype.set_cs=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_cs_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"cs\",{get:rcConfig.prototype.get_cs,set:rcConfig.prototype.set_cs});rcConfig.prototype[\"get_ch\"]=rcConfig.prototype.get_ch=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_ch_0(self)};rcConfig.prototype[\"set_ch\"]=rcConfig.prototype.set_ch=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_ch_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"ch\",{get:rcConfig.prototype.get_ch,set:rcConfig.prototype.set_ch});rcConfig.prototype[\"get_bmin\"]=rcConfig.prototype.get_bmin=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmin_1(self,arg0)};rcConfig.prototype[\"set_bmin\"]=rcConfig.prototype.set_bmin=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmin_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmin\",{get:rcConfig.prototype.get_bmin,set:rcConfig.prototype.set_bmin});rcConfig.prototype[\"get_bmax\"]=rcConfig.prototype.get_bmax=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;return _emscripten_bind_rcConfig_get_bmax_1(self,arg0)};rcConfig.prototype[\"set_bmax\"]=rcConfig.prototype.set_bmax=function(arg0,arg1){var self=this.ptr;ensureCache.prepare();if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;if(arg1&&typeof arg1===\"object\")arg1=arg1.ptr;_emscripten_bind_rcConfig_set_bmax_2(self,arg0,arg1)};Object.defineProperty(rcConfig.prototype,\"bmax\",{get:rcConfig.prototype.get_bmax,set:rcConfig.prototype.set_bmax});rcConfig.prototype[\"get_walkableSlopeAngle\"]=rcConfig.prototype.get_walkableSlopeAngle=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableSlopeAngle_0(self)};rcConfig.prototype[\"set_walkableSlopeAngle\"]=rcConfig.prototype.set_walkableSlopeAngle=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableSlopeAngle_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableSlopeAngle\",{get:rcConfig.prototype.get_walkableSlopeAngle,set:rcConfig.prototype.set_walkableSlopeAngle});rcConfig.prototype[\"get_walkableHeight\"]=rcConfig.prototype.get_walkableHeight=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableHeight_0(self)};rcConfig.prototype[\"set_walkableHeight\"]=rcConfig.prototype.set_walkableHeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableHeight_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableHeight\",{get:rcConfig.prototype.get_walkableHeight,set:rcConfig.prototype.set_walkableHeight});rcConfig.prototype[\"get_walkableClimb\"]=rcConfig.prototype.get_walkableClimb=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableClimb_0(self)};rcConfig.prototype[\"set_walkableClimb\"]=rcConfig.prototype.set_walkableClimb=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableClimb_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableClimb\",{get:rcConfig.prototype.get_walkableClimb,set:rcConfig.prototype.set_walkableClimb});rcConfig.prototype[\"get_walkableRadius\"]=rcConfig.prototype.get_walkableRadius=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_walkableRadius_0(self)};rcConfig.prototype[\"set_walkableRadius\"]=rcConfig.prototype.set_walkableRadius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_walkableRadius_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"walkableRadius\",{get:rcConfig.prototype.get_walkableRadius,set:rcConfig.prototype.set_walkableRadius});rcConfig.prototype[\"get_maxEdgeLen\"]=rcConfig.prototype.get_maxEdgeLen=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxEdgeLen_0(self)};rcConfig.prototype[\"set_maxEdgeLen\"]=rcConfig.prototype.set_maxEdgeLen=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxEdgeLen_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxEdgeLen\",{get:rcConfig.prototype.get_maxEdgeLen,set:rcConfig.prototype.set_maxEdgeLen});rcConfig.prototype[\"get_maxSimplificationError\"]=rcConfig.prototype.get_maxSimplificationError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxSimplificationError_0(self)};rcConfig.prototype[\"set_maxSimplificationError\"]=rcConfig.prototype.set_maxSimplificationError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxSimplificationError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxSimplificationError\",{get:rcConfig.prototype.get_maxSimplificationError,set:rcConfig.prototype.set_maxSimplificationError});rcConfig.prototype[\"get_minRegionArea\"]=rcConfig.prototype.get_minRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_minRegionArea_0(self)};rcConfig.prototype[\"set_minRegionArea\"]=rcConfig.prototype.set_minRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_minRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"minRegionArea\",{get:rcConfig.prototype.get_minRegionArea,set:rcConfig.prototype.set_minRegionArea});rcConfig.prototype[\"get_mergeRegionArea\"]=rcConfig.prototype.get_mergeRegionArea=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_mergeRegionArea_0(self)};rcConfig.prototype[\"set_mergeRegionArea\"]=rcConfig.prototype.set_mergeRegionArea=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_mergeRegionArea_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"mergeRegionArea\",{get:rcConfig.prototype.get_mergeRegionArea,set:rcConfig.prototype.set_mergeRegionArea});rcConfig.prototype[\"get_maxVertsPerPoly\"]=rcConfig.prototype.get_maxVertsPerPoly=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_maxVertsPerPoly_0(self)};rcConfig.prototype[\"set_maxVertsPerPoly\"]=rcConfig.prototype.set_maxVertsPerPoly=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_maxVertsPerPoly_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"maxVertsPerPoly\",{get:rcConfig.prototype.get_maxVertsPerPoly,set:rcConfig.prototype.set_maxVertsPerPoly});rcConfig.prototype[\"get_detailSampleDist\"]=rcConfig.prototype.get_detailSampleDist=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleDist_0(self)};rcConfig.prototype[\"set_detailSampleDist\"]=rcConfig.prototype.set_detailSampleDist=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleDist_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleDist\",{get:rcConfig.prototype.get_detailSampleDist,set:rcConfig.prototype.set_detailSampleDist});rcConfig.prototype[\"get_detailSampleMaxError\"]=rcConfig.prototype.get_detailSampleMaxError=function(){var self=this.ptr;return _emscripten_bind_rcConfig_get_detailSampleMaxError_0(self)};rcConfig.prototype[\"set_detailSampleMaxError\"]=rcConfig.prototype.set_detailSampleMaxError=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_rcConfig_set_detailSampleMaxError_1(self,arg0)};Object.defineProperty(rcConfig.prototype,\"detailSampleMaxError\",{get:rcConfig.prototype.get_detailSampleMaxError,set:rcConfig.prototype.set_detailSampleMaxError});rcConfig.prototype[\"__destroy__\"]=rcConfig.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_rcConfig___destroy___0(self)};function Vec3(x,y,z){if(x&&typeof x===\"object\")x=x.ptr;if(y&&typeof y===\"object\")y=y.ptr;if(z&&typeof z===\"object\")z=z.ptr;if(x===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_0();getCache(Vec3)[this.ptr]=this;return}if(y===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_1(x);getCache(Vec3)[this.ptr]=this;return}if(z===undefined){this.ptr=_emscripten_bind_Vec3_Vec3_2(x,y);getCache(Vec3)[this.ptr]=this;return}this.ptr=_emscripten_bind_Vec3_Vec3_3(x,y,z);getCache(Vec3)[this.ptr]=this}Vec3.prototype=Object.create(WrapperObject.prototype);Vec3.prototype.constructor=Vec3;Vec3.prototype.__class__=Vec3;Vec3.__cache__={};Module[\"Vec3\"]=Vec3;Vec3.prototype[\"get_x\"]=Vec3.prototype.get_x=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_x_0(self)};Vec3.prototype[\"set_x\"]=Vec3.prototype.set_x=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_x_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"x\",{get:Vec3.prototype.get_x,set:Vec3.prototype.set_x});Vec3.prototype[\"get_y\"]=Vec3.prototype.get_y=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_y_0(self)};Vec3.prototype[\"set_y\"]=Vec3.prototype.set_y=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_y_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"y\",{get:Vec3.prototype.get_y,set:Vec3.prototype.set_y});Vec3.prototype[\"get_z\"]=Vec3.prototype.get_z=function(){var self=this.ptr;return _emscripten_bind_Vec3_get_z_0(self)};Vec3.prototype[\"set_z\"]=Vec3.prototype.set_z=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_Vec3_set_z_1(self,arg0)};Object.defineProperty(Vec3.prototype,\"z\",{get:Vec3.prototype.get_z,set:Vec3.prototype.set_z});Vec3.prototype[\"__destroy__\"]=Vec3.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Vec3___destroy___0(self)};function Triangle(){this.ptr=_emscripten_bind_Triangle_Triangle_0();getCache(Triangle)[this.ptr]=this}Triangle.prototype=Object.create(WrapperObject.prototype);Triangle.prototype.constructor=Triangle;Triangle.prototype.__class__=Triangle;Triangle.__cache__={};Module[\"Triangle\"]=Triangle;Triangle.prototype[\"getPoint\"]=Triangle.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_Triangle_getPoint_1(self,n),Vec3)};Triangle.prototype[\"__destroy__\"]=Triangle.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Triangle___destroy___0(self)};function DebugNavMesh(){this.ptr=_emscripten_bind_DebugNavMesh_DebugNavMesh_0();getCache(DebugNavMesh)[this.ptr]=this}DebugNavMesh.prototype=Object.create(WrapperObject.prototype);DebugNavMesh.prototype.constructor=DebugNavMesh;DebugNavMesh.prototype.__class__=DebugNavMesh;DebugNavMesh.__cache__={};Module[\"DebugNavMesh\"]=DebugNavMesh;DebugNavMesh.prototype[\"getTriangleCount\"]=DebugNavMesh.prototype.getTriangleCount=function(){var self=this.ptr;return _emscripten_bind_DebugNavMesh_getTriangleCount_0(self)};DebugNavMesh.prototype[\"getTriangle\"]=DebugNavMesh.prototype.getTriangle=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_DebugNavMesh_getTriangle_1(self,n),Triangle)};DebugNavMesh.prototype[\"__destroy__\"]=DebugNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_DebugNavMesh___destroy___0(self)};function dtNavMesh(){throw\"cannot construct a dtNavMesh, no constructor in IDL\"}dtNavMesh.prototype=Object.create(WrapperObject.prototype);dtNavMesh.prototype.constructor=dtNavMesh;dtNavMesh.prototype.__class__=dtNavMesh;dtNavMesh.__cache__={};Module[\"dtNavMesh\"]=dtNavMesh;dtNavMesh.prototype[\"__destroy__\"]=dtNavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtNavMesh___destroy___0(self)};function NavmeshData(){this.ptr=_emscripten_bind_NavmeshData_NavmeshData_0();getCache(NavmeshData)[this.ptr]=this}NavmeshData.prototype=Object.create(WrapperObject.prototype);NavmeshData.prototype.constructor=NavmeshData;NavmeshData.prototype.__class__=NavmeshData;NavmeshData.__cache__={};Module[\"NavmeshData\"]=NavmeshData;NavmeshData.prototype[\"get_dataPointer\"]=NavmeshData.prototype.get_dataPointer=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_dataPointer_0(self)};NavmeshData.prototype[\"set_dataPointer\"]=NavmeshData.prototype.set_dataPointer=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_dataPointer_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"dataPointer\",{get:NavmeshData.prototype.get_dataPointer,set:NavmeshData.prototype.set_dataPointer});NavmeshData.prototype[\"get_size\"]=NavmeshData.prototype.get_size=function(){var self=this.ptr;return _emscripten_bind_NavmeshData_get_size_0(self)};NavmeshData.prototype[\"set_size\"]=NavmeshData.prototype.set_size=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_NavmeshData_set_size_1(self,arg0)};Object.defineProperty(NavmeshData.prototype,\"size\",{get:NavmeshData.prototype.get_size,set:NavmeshData.prototype.set_size});NavmeshData.prototype[\"__destroy__\"]=NavmeshData.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavmeshData___destroy___0(self)};function NavPath(){throw\"cannot construct a NavPath, no constructor in IDL\"}NavPath.prototype=Object.create(WrapperObject.prototype);NavPath.prototype.constructor=NavPath;NavPath.prototype.__class__=NavPath;NavPath.__cache__={};Module[\"NavPath\"]=NavPath;NavPath.prototype[\"getPointCount\"]=NavPath.prototype.getPointCount=function(){var self=this.ptr;return _emscripten_bind_NavPath_getPointCount_0(self)};NavPath.prototype[\"getPoint\"]=NavPath.prototype.getPoint=function(n){var self=this.ptr;if(n&&typeof n===\"object\")n=n.ptr;return wrapPointer(_emscripten_bind_NavPath_getPoint_1(self,n),Vec3)};NavPath.prototype[\"__destroy__\"]=NavPath.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavPath___destroy___0(self)};function dtObstacleRef(){throw\"cannot construct a dtObstacleRef, no constructor in IDL\"}dtObstacleRef.prototype=Object.create(WrapperObject.prototype);dtObstacleRef.prototype.constructor=dtObstacleRef;dtObstacleRef.prototype.__class__=dtObstacleRef;dtObstacleRef.__cache__={};Module[\"dtObstacleRef\"]=dtObstacleRef;dtObstacleRef.prototype[\"__destroy__\"]=dtObstacleRef.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtObstacleRef___destroy___0(self)};function dtCrowdAgentParams(){this.ptr=_emscripten_bind_dtCrowdAgentParams_dtCrowdAgentParams_0();getCache(dtCrowdAgentParams)[this.ptr]=this}dtCrowdAgentParams.prototype=Object.create(WrapperObject.prototype);dtCrowdAgentParams.prototype.constructor=dtCrowdAgentParams;dtCrowdAgentParams.prototype.__class__=dtCrowdAgentParams;dtCrowdAgentParams.__cache__={};Module[\"dtCrowdAgentParams\"]=dtCrowdAgentParams;dtCrowdAgentParams.prototype[\"get_radius\"]=dtCrowdAgentParams.prototype.get_radius=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_radius_0(self)};dtCrowdAgentParams.prototype[\"set_radius\"]=dtCrowdAgentParams.prototype.set_radius=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_radius_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"radius\",{get:dtCrowdAgentParams.prototype.get_radius,set:dtCrowdAgentParams.prototype.set_radius});dtCrowdAgentParams.prototype[\"get_height\"]=dtCrowdAgentParams.prototype.get_height=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_height_0(self)};dtCrowdAgentParams.prototype[\"set_height\"]=dtCrowdAgentParams.prototype.set_height=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_height_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"height\",{get:dtCrowdAgentParams.prototype.get_height,set:dtCrowdAgentParams.prototype.set_height});dtCrowdAgentParams.prototype[\"get_maxAcceleration\"]=dtCrowdAgentParams.prototype.get_maxAcceleration=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxAcceleration_0(self)};dtCrowdAgentParams.prototype[\"set_maxAcceleration\"]=dtCrowdAgentParams.prototype.set_maxAcceleration=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxAcceleration_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxAcceleration\",{get:dtCrowdAgentParams.prototype.get_maxAcceleration,set:dtCrowdAgentParams.prototype.set_maxAcceleration});dtCrowdAgentParams.prototype[\"get_maxSpeed\"]=dtCrowdAgentParams.prototype.get_maxSpeed=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_maxSpeed_0(self)};dtCrowdAgentParams.prototype[\"set_maxSpeed\"]=dtCrowdAgentParams.prototype.set_maxSpeed=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_maxSpeed_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"maxSpeed\",{get:dtCrowdAgentParams.prototype.get_maxSpeed,set:dtCrowdAgentParams.prototype.set_maxSpeed});dtCrowdAgentParams.prototype[\"get_collisionQueryRange\"]=dtCrowdAgentParams.prototype.get_collisionQueryRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_collisionQueryRange_0(self)};dtCrowdAgentParams.prototype[\"set_collisionQueryRange\"]=dtCrowdAgentParams.prototype.set_collisionQueryRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_collisionQueryRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"collisionQueryRange\",{get:dtCrowdAgentParams.prototype.get_collisionQueryRange,set:dtCrowdAgentParams.prototype.set_collisionQueryRange});dtCrowdAgentParams.prototype[\"get_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.get_pathOptimizationRange=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_pathOptimizationRange_0(self)};dtCrowdAgentParams.prototype[\"set_pathOptimizationRange\"]=dtCrowdAgentParams.prototype.set_pathOptimizationRange=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_pathOptimizationRange_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"pathOptimizationRange\",{get:dtCrowdAgentParams.prototype.get_pathOptimizationRange,set:dtCrowdAgentParams.prototype.set_pathOptimizationRange});dtCrowdAgentParams.prototype[\"get_separationWeight\"]=dtCrowdAgentParams.prototype.get_separationWeight=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_separationWeight_0(self)};dtCrowdAgentParams.prototype[\"set_separationWeight\"]=dtCrowdAgentParams.prototype.set_separationWeight=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_separationWeight_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"separationWeight\",{get:dtCrowdAgentParams.prototype.get_separationWeight,set:dtCrowdAgentParams.prototype.set_separationWeight});dtCrowdAgentParams.prototype[\"get_updateFlags\"]=dtCrowdAgentParams.prototype.get_updateFlags=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_updateFlags_0(self)};dtCrowdAgentParams.prototype[\"set_updateFlags\"]=dtCrowdAgentParams.prototype.set_updateFlags=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_updateFlags_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"updateFlags\",{get:dtCrowdAgentParams.prototype.get_updateFlags,set:dtCrowdAgentParams.prototype.set_updateFlags});dtCrowdAgentParams.prototype[\"get_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.get_obstacleAvoidanceType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_obstacleAvoidanceType_0(self)};dtCrowdAgentParams.prototype[\"set_obstacleAvoidanceType\"]=dtCrowdAgentParams.prototype.set_obstacleAvoidanceType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_obstacleAvoidanceType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"obstacleAvoidanceType\",{get:dtCrowdAgentParams.prototype.get_obstacleAvoidanceType,set:dtCrowdAgentParams.prototype.set_obstacleAvoidanceType});dtCrowdAgentParams.prototype[\"get_queryFilterType\"]=dtCrowdAgentParams.prototype.get_queryFilterType=function(){var self=this.ptr;return _emscripten_bind_dtCrowdAgentParams_get_queryFilterType_0(self)};dtCrowdAgentParams.prototype[\"set_queryFilterType\"]=dtCrowdAgentParams.prototype.set_queryFilterType=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_queryFilterType_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"queryFilterType\",{get:dtCrowdAgentParams.prototype.get_queryFilterType,set:dtCrowdAgentParams.prototype.set_queryFilterType});dtCrowdAgentParams.prototype[\"get_userData\"]=dtCrowdAgentParams.prototype.get_userData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_dtCrowdAgentParams_get_userData_0(self),VoidPtr)};dtCrowdAgentParams.prototype[\"set_userData\"]=dtCrowdAgentParams.prototype.set_userData=function(arg0){var self=this.ptr;if(arg0&&typeof arg0===\"object\")arg0=arg0.ptr;_emscripten_bind_dtCrowdAgentParams_set_userData_1(self,arg0)};Object.defineProperty(dtCrowdAgentParams.prototype,\"userData\",{get:dtCrowdAgentParams.prototype.get_userData,set:dtCrowdAgentParams.prototype.set_userData});dtCrowdAgentParams.prototype[\"__destroy__\"]=dtCrowdAgentParams.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_dtCrowdAgentParams___destroy___0(self)};function NavMesh(){this.ptr=_emscripten_bind_NavMesh_NavMesh_0();getCache(NavMesh)[this.ptr]=this}NavMesh.prototype=Object.create(WrapperObject.prototype);NavMesh.prototype.constructor=NavMesh;NavMesh.prototype.__class__=NavMesh;NavMesh.__cache__={};Module[\"NavMesh\"]=NavMesh;NavMesh.prototype[\"destroy\"]=NavMesh.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_NavMesh_destroy_0(self)};NavMesh.prototype[\"build\"]=NavMesh.prototype.build=function(positions,positionCount,indices,indexCount,config){var self=this.ptr;ensureCache.prepare();if(typeof positions==\"object\"){positions=ensureFloat32(positions)}if(positionCount&&typeof positionCount===\"object\")positionCount=positionCount.ptr;if(typeof indices==\"object\"){indices=ensureInt32(indices)}if(indexCount&&typeof indexCount===\"object\")indexCount=indexCount.ptr;if(config&&typeof config===\"object\")config=config.ptr;_emscripten_bind_NavMesh_build_5(self,positions,positionCount,indices,indexCount,config)};NavMesh.prototype[\"buildFromNavmeshData\"]=NavMesh.prototype.buildFromNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_buildFromNavmeshData_1(self,data)};NavMesh.prototype[\"getNavmeshData\"]=NavMesh.prototype.getNavmeshData=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavmeshData_0(self),NavmeshData)};NavMesh.prototype[\"freeNavmeshData\"]=NavMesh.prototype.freeNavmeshData=function(data){var self=this.ptr;if(data&&typeof data===\"object\")data=data.ptr;_emscripten_bind_NavMesh_freeNavmeshData_1(self,data)};NavMesh.prototype[\"getDebugNavMesh\"]=NavMesh.prototype.getDebugNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDebugNavMesh_0(self),DebugNavMesh)};NavMesh.prototype[\"getClosestPoint\"]=NavMesh.prototype.getClosestPoint=function(position){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;return wrapPointer(_emscripten_bind_NavMesh_getClosestPoint_1(self,position),Vec3)};NavMesh.prototype[\"getRandomPointAround\"]=NavMesh.prototype.getRandomPointAround=function(position,maxRadius){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(maxRadius&&typeof maxRadius===\"object\")maxRadius=maxRadius.ptr;return wrapPointer(_emscripten_bind_NavMesh_getRandomPointAround_2(self,position,maxRadius),Vec3)};NavMesh.prototype[\"moveAlong\"]=NavMesh.prototype.moveAlong=function(position,destination){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;return wrapPointer(_emscripten_bind_NavMesh_moveAlong_2(self,position,destination),Vec3)};NavMesh.prototype[\"getNavMesh\"]=NavMesh.prototype.getNavMesh=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getNavMesh_0(self),dtNavMesh)};NavMesh.prototype[\"computePath\"]=NavMesh.prototype.computePath=function(start,end){var self=this.ptr;if(start&&typeof start===\"object\")start=start.ptr;if(end&&typeof end===\"object\")end=end.ptr;return wrapPointer(_emscripten_bind_NavMesh_computePath_2(self,start,end),NavPath)};NavMesh.prototype[\"setDefaultQueryExtent\"]=NavMesh.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_NavMesh_setDefaultQueryExtent_1(self,extent)};NavMesh.prototype[\"getDefaultQueryExtent\"]=NavMesh.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_NavMesh_getDefaultQueryExtent_0(self),Vec3)};NavMesh.prototype[\"addCylinderObstacle\"]=NavMesh.prototype.addCylinderObstacle=function(position,radius,height){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(radius&&typeof radius===\"object\")radius=radius.ptr;if(height&&typeof height===\"object\")height=height.ptr;return wrapPointer(_emscripten_bind_NavMesh_addCylinderObstacle_3(self,position,radius,height),dtObstacleRef)};NavMesh.prototype[\"addBoxObstacle\"]=NavMesh.prototype.addBoxObstacle=function(position,extent,angle){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;if(angle&&typeof angle===\"object\")angle=angle.ptr;return wrapPointer(_emscripten_bind_NavMesh_addBoxObstacle_3(self,position,extent,angle),dtObstacleRef)};NavMesh.prototype[\"removeObstacle\"]=NavMesh.prototype.removeObstacle=function(obstacle){var self=this.ptr;if(obstacle&&typeof obstacle===\"object\")obstacle=obstacle.ptr;_emscripten_bind_NavMesh_removeObstacle_1(self,obstacle)};NavMesh.prototype[\"update\"]=NavMesh.prototype.update=function(){var self=this.ptr;_emscripten_bind_NavMesh_update_0(self)};NavMesh.prototype[\"__destroy__\"]=NavMesh.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_NavMesh___destroy___0(self)};function Crowd(maxAgents,maxAgentRadius,nav){if(maxAgents&&typeof maxAgents===\"object\")maxAgents=maxAgents.ptr;if(maxAgentRadius&&typeof maxAgentRadius===\"object\")maxAgentRadius=maxAgentRadius.ptr;if(nav&&typeof nav===\"object\")nav=nav.ptr;this.ptr=_emscripten_bind_Crowd_Crowd_3(maxAgents,maxAgentRadius,nav);getCache(Crowd)[this.ptr]=this}Crowd.prototype=Object.create(WrapperObject.prototype);Crowd.prototype.constructor=Crowd;Crowd.prototype.__class__=Crowd;Crowd.__cache__={};Module[\"Crowd\"]=Crowd;Crowd.prototype[\"destroy\"]=Crowd.prototype.destroy=function(){var self=this.ptr;_emscripten_bind_Crowd_destroy_0(self)};Crowd.prototype[\"addAgent\"]=Crowd.prototype.addAgent=function(position,params){var self=this.ptr;if(position&&typeof position===\"object\")position=position.ptr;if(params&&typeof params===\"object\")params=params.ptr;return _emscripten_bind_Crowd_addAgent_2(self,position,params)};Crowd.prototype[\"removeAgent\"]=Crowd.prototype.removeAgent=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;_emscripten_bind_Crowd_removeAgent_1(self,idx)};Crowd.prototype[\"update\"]=Crowd.prototype.update=function(dt){var self=this.ptr;if(dt&&typeof dt===\"object\")dt=dt.ptr;_emscripten_bind_Crowd_update_1(self,dt)};Crowd.prototype[\"getAgentPosition\"]=Crowd.prototype.getAgentPosition=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentPosition_1(self,idx),Vec3)};Crowd.prototype[\"getAgentVelocity\"]=Crowd.prototype.getAgentVelocity=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentVelocity_1(self,idx),Vec3)};Crowd.prototype[\"getAgentNextTargetPath\"]=Crowd.prototype.getAgentNextTargetPath=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentNextTargetPath_1(self,idx),Vec3)};Crowd.prototype[\"getAgentState\"]=Crowd.prototype.getAgentState=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return _emscripten_bind_Crowd_getAgentState_1(self,idx)};Crowd.prototype[\"overOffmeshConnection\"]=Crowd.prototype.overOffmeshConnection=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return!!_emscripten_bind_Crowd_overOffmeshConnection_1(self,idx)};Crowd.prototype[\"agentGoto\"]=Crowd.prototype.agentGoto=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentGoto_2(self,idx,destination)};Crowd.prototype[\"agentTeleport\"]=Crowd.prototype.agentTeleport=function(idx,destination){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(destination&&typeof destination===\"object\")destination=destination.ptr;_emscripten_bind_Crowd_agentTeleport_2(self,idx,destination)};Crowd.prototype[\"getAgentParameters\"]=Crowd.prototype.getAgentParameters=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getAgentParameters_1(self,idx),dtCrowdAgentParams)};Crowd.prototype[\"setAgentParameters\"]=Crowd.prototype.setAgentParameters=function(idx,params){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;if(params&&typeof params===\"object\")params=params.ptr;_emscripten_bind_Crowd_setAgentParameters_2(self,idx,params)};Crowd.prototype[\"setDefaultQueryExtent\"]=Crowd.prototype.setDefaultQueryExtent=function(extent){var self=this.ptr;if(extent&&typeof extent===\"object\")extent=extent.ptr;_emscripten_bind_Crowd_setDefaultQueryExtent_1(self,extent)};Crowd.prototype[\"getDefaultQueryExtent\"]=Crowd.prototype.getDefaultQueryExtent=function(){var self=this.ptr;return wrapPointer(_emscripten_bind_Crowd_getDefaultQueryExtent_0(self),Vec3)};Crowd.prototype[\"getCorners\"]=Crowd.prototype.getCorners=function(idx){var self=this.ptr;if(idx&&typeof idx===\"object\")idx=idx.ptr;return wrapPointer(_emscripten_bind_Crowd_getCorners_1(self,idx),NavPath)};Crowd.prototype[\"__destroy__\"]=Crowd.prototype.__destroy__=function(){var self=this.ptr;_emscripten_bind_Crowd___destroy___0(self)};\n\n\n  return Module.ready\n}\n);\n})();\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Module);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/navmesh.js\n"));

/***/ })

}]);