import React from 'react';

export default function Conclusion() {
    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-gradient-to-r from-blue-500 via-teal-500 to-green-500 text-white p-10">
            <style>
                {`
                .highlight {
                    color: #ffeb3b;
                    font-weight: bold;
                }

                .card {
                    background-color: rgba(255, 255, 255, 0.2);
                    padding: 20px;
                    border-radius: 12px;
                    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
                    max-width: 600px;
                    text-align: center;
                    margin-top: 20px;
                }

                .celebration {
                    width: 250px;
                    height: 250px;
                    margin-bottom: 20px;
                    animation: bounce 2s infinite;
                }

                @keyframes bounce {
                    0%, 20%, 50%, 80%, 100% {
                        transform: translateY(0);
                    }
                    40% {
                        transform: translateY(-20px);
                    }
                    60% {
                        transform: translateY(-10px);
                    }
                }
                `}
            </style>

            {/* Celebration Image Placeholder */}
            <img
                src="/glitch-mascot.png" /* Using available mascot image for celebration */
                alt="Celebration"
                className="celebration"
            />

            {/* Congratulations Message */}
            <h1 className="text-5xl font-bold mb-6">🎉 Congratulations! 🎉</h1>
            <p className="text-xl text-center max-w-3xl mb-8">
                You’ve completed the Level 3 curriculum and taken significant steps in your <span className="highlight">data science</span> journey.
            </p>

            {/* Motivation Section */}
            <div className="card">
                <h2 className="text-3xl font-bold mb-4">🚀 Keep Exploring!</h2>
                <p className="text-lg">
                    The world of data science is vast and exciting. Remember:
                    <br />
                    - Keep practicing and honing your skills.<br />
                    - Explore new tools and techniques.<br />
                    - Never stop learning—there’s always something new to discover.
                </p>
            </div>

            {/* Encouragement */}
            <p className="text-center text-xl font-semibold mt-12 max-w-2xl">
                This is just the beginning. Go out there and achieve amazing things with data!
            </p>
        </div>
    );
}
