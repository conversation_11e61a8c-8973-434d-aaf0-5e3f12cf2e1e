// Progress tracking utility for LMS
const API_BASE_URL = process.env.NODE_ENV === 'production' 
  ? 'https://your-backend-url.com/api' 
  : 'http://localhost:5000/api';

// Get user's progress from backend
export const getUserProgress = async () => {
  try {
    const token = localStorage.getItem('token');

    // If no token, use local storage
    if (!token) {
      console.log('No token found, using local storage for progress');
      return getProgressLocally();
    }

    const response = await fetch(`${API_BASE_URL}/progress`, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      console.log('Backend not available, using local storage fallback');
      return getProgressLocally();
    }

    const data = await response.json();
    return data.progress;
  } catch (error) {
    console.log('Error fetching progress, using local storage fallback:', error.message);
    return getProgressLocally();
  }
};

// Get progress from local storage
const getProgressLocally = () => {
  try {
    const progress = JSON.parse(localStorage.getItem('userProgress') || '{}');
    return {
      currentLevel: progress.currentLevel || 'level1',
      currentLesson: progress.currentLesson || '1.1WhatIsComputer',
      completedLessons: progress.completedLessons || [],
      accessedLessons: progress.accessedLessons || [],
      lastAccessedAt: progress.lastAccessedAt || new Date().toISOString(),
      totalLessonsCompleted: progress.totalLessonsCompleted || 0,
      progressPercentage: progress.progressPercentage || 0
    };
  } catch (error) {
    console.error('Error reading local progress:', error);
    return {
      currentLevel: 'level1',
      currentLesson: '1.1WhatIsComputer',
      completedLessons: [],
      accessedLessons: [],
      lastAccessedAt: new Date().toISOString(),
      totalLessonsCompleted: 0,
      progressPercentage: 0
    };
  }
};

// Local storage fallback for progress tracking
const updateProgressLocally = (lessonId, level, action) => {
  try {
    // Get existing progress from localStorage
    const existingProgress = JSON.parse(localStorage.getItem('userProgress') || '{}');

    // Initialize if empty
    if (!existingProgress.completedLessons) {
      existingProgress.completedLessons = [];
    }
    if (!existingProgress.accessedLessons) {
      existingProgress.accessedLessons = [];
    }

    // Update progress based on action
    if (action === 'access' && !existingProgress.accessedLessons.includes(lessonId)) {
      existingProgress.accessedLessons.push(lessonId);
    }

    if (action === 'complete' && !existingProgress.completedLessons.includes(lessonId)) {
      existingProgress.completedLessons.push(lessonId);
    }

    // Update other fields
    existingProgress.currentLevel = level;
    existingProgress.currentLesson = lessonId;
    existingProgress.lastAccessedAt = new Date().toISOString();
    existingProgress.totalLessonsCompleted = existingProgress.completedLessons.length;
    existingProgress.progressPercentage = calculateOverallProgress(existingProgress.completedLessons);

    // Save back to localStorage
    localStorage.setItem('userProgress', JSON.stringify(existingProgress));

    console.log(`Progress updated locally: ${action} for ${lessonId} in ${level}`);
    return existingProgress;
  } catch (error) {
    console.error('Error updating progress locally:', error);
    return {
      currentLevel: level,
      currentLesson: lessonId,
      completedLessons: [],
      accessedLessons: [],
      lastAccessedAt: new Date().toISOString(),
      totalLessonsCompleted: 0,
      progressPercentage: 0
    };
  }
};

// Update user's progress
export const updateProgress = async (lessonId, level, action = 'access') => {
  try {
    const token = localStorage.getItem('token');

    // If no token, use local storage fallback
    if (!token) {
      console.log('No token found, using local storage for progress tracking');
      return updateProgressLocally(lessonId, level, action);
    }

    const response = await fetch(`${API_BASE_URL}/progress/update`, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        lessonId,
        level,
        action
      }),
    });

    if (!response.ok) {
      console.log('Backend not available, using local storage fallback');
      return updateProgressLocally(lessonId, level, action);
    }

    const data = await response.json();
    return data.progress;
  } catch (error) {
    console.log('Error updating progress, using local storage fallback:', error.message);
    return updateProgressLocally(lessonId, level, action);
  }
};

// Mark lesson as completed
export const markLessonComplete = async (lessonId, level) => {
  return await updateProgress(lessonId, level, 'complete');
};

// Track lesson access
export const trackLessonAccess = async (lessonId, level) => {
  return await updateProgress(lessonId, level, 'access');
};

// Check if lesson is completed
export const isLessonCompleted = (lessonId, completedLessons) => {
  return completedLessons.includes(lessonId);
};

// Get next lesson based on current progress
export const getNextLesson = (currentLevel, currentLesson) => {
  // Define lesson sequences for each level
  const lessonSequences = {
    level1: [
      '1.1WhatIsComputer',
      '1.2HowComputersWork',
      '1.3WhatIsAProgram',
      '1.4TypesOfComputers',
      '1.5HardwareAndSoftware',
      '1.6OperatingSystem',
      '2.1WhatIsScratch',
      '3.1WhatIsIoT',
      '3.2HowIoTWorks',
      '3.3IoTExamples',
      '3.4IoTBenefits',
      '4.1WhatIsComputerVision',
      '4.2HowComputerVisionWorks',
      '4.3ComputerVisionApplications',
      '5.1Summary',
      '5.3CompletionMessage'
    ],
    level2: [
      '0.0CourseContent',
      '0.1DisplayLevel2',
      '01.0MetaverseAndAugmentedReality',
      '01.1CoolTechnologyBehindMetaverse',
      '02.01WhatIsAugmentedReality',
      '02.0AugmentedReality',
      '02.1MarkerBasedAR',
      '02.2MarkerlessAR',
      '02.3UsesOfAugmentedReality',
      '03.0VirtualReality',
      '03.2ApplicationsOfVirtualReality',
      '04.1UnderstandingDNA',
      '04.2CoolAIToolsForGenomics',
      '04.3ApplicationsInMedicine',
      '04.4FunFactsGenomicAI',
      '2.0CyberSecurity',
      '2.1WhatIsCybersecurity',
      '2.2ThreatsAndAttacks',
      '2.3ProtectingYourselfOnline',
      '2.4FutureOfCybersecurity',
      '3.0QuantumComputing',
      '3.1WhatIsQuantumComputing',
      '3.2QuantumBitsAndEntanglement',
      '4.0GenomicAI'
    ],
    level3: [
      '0.0CourseContent',
      '1.0IntroductionToDataScience',
      '1.1GlitchSaysHi',
      '1.2WhatIsDataScience',
      '1.3UnderstandingDataScience',
      '2.1HowDataIsCollected',
      '2.2UnderstandingMeanMedianMode',
      '2.3ExploringDataTypes',
      '3.0DataVisualization',
      '3.1WhyVisualizeData',
      '3.2CreateYourOwnBarGraph',
      '3.3IntroductionToCharts',
      '3.4DataStorytelling',
      '3.5AdvancedVisualization',
      '4.0Quiz',
      '4.1Conclusion'
    ],
    level4: [
      // Add level 4 lessons here
      '1.1HelloWorld',
      '1.2Pyramid',
      '1.3Ascii',
      '1.4Letter',
      '2.1DataTypes',
      '2.2DataTypes',
      '2.3whew',
      '3.1ControlFlow',
      '3.2AreYouACatPerson',
      '3.3TheGoodTheBadAndTheElif',
      '3.4BookOfAnswers',
      '3.5GodOfThunder',
      '4.1DontMakeMeGuess',
      '4.2OhYouAreActuallyMakingMeGuess',
      '4.3PlierEtendreReleverElancer',
      '4.4MyTrueLoveSentToMe',
      '4.5GoodOlFizzBuzz'
    ],
    level5: [
      // Add level 5 lessons here
      'Ai',
      'MachineLearning',
      'NeuralNetworks',
      'DeepLearning',
      'Future',
      'Final',
      'Whatisnlp',
      'Human',
      'Breakingitdownwithtokenization',
      'Cleaningupthemess',
      'Letsgetsentimental',
      'Buildingachatbot'
    ]
  };

  const currentSequence = lessonSequences[currentLevel];
  if (!currentSequence) return null;

  const currentIndex = currentSequence.indexOf(currentLesson);
  if (currentIndex === -1 || currentIndex === currentSequence.length - 1) {
    // Move to next level if current level is complete
    const levelNumber = parseInt(currentLevel.replace('level', ''));
    const nextLevel = `level${levelNumber + 1}`;
    if (lessonSequences[nextLevel]) {
      return {
        level: nextLevel,
        lesson: lessonSequences[nextLevel][0]
      };
    }
    return null; // All levels completed
  }

  return {
    level: currentLevel,
    lesson: currentSequence[currentIndex + 1]
  };
};

// Calculate overall progress percentage
export const calculateOverallProgress = (completedLessons) => {
  const totalLessons = 100; // Adjust based on actual total
  return Math.round((completedLessons.length / totalLessons) * 100);
};

// Get lesson display name
export const getLessonDisplayName = (lessonId) => {
  const lessonNames = {
    '1.1WhatIsComputer': 'What is a Computer?',
    '1.2HowComputersWork': 'How Computers Work',
    '1.3WhatIsAProgram': 'What is a Program?',
    '1.4TypesOfComputers': 'Types of Computers',
    '1.5HardwareAndSoftware': 'Hardware and Software',
    '1.6OperatingSystem': 'Operating System',
    '2.1WhatIsScratch': 'What is Scratch?',
    '3.1WhatIsIoT': 'What is IoT?',
    '3.2HowIoTWorks': 'How IoT Works',
    '3.3IoTExamples': 'IoT Examples',
    '3.4IoTBenefits': 'IoT Benefits',
    '4.1WhatIsComputerVision': 'What is Computer Vision?',
    '4.2HowComputerVisionWorks': 'How Computer Vision Works',
    '4.3ComputerVisionApplications': 'Computer Vision Applications',
    '5.1Summary': 'Summary',
    '5.3CompletionMessage': 'Completion Message'
    // Add more lesson names as needed
  };

  return lessonNames[lessonId] || lessonId;
};
