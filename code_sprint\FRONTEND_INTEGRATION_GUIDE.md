# 🔄 SPRINT - AI Frontend Integration Guide

## 🎯 **The Problem**
You have **TWO separate Next.js applications**:
1. **Root App** (`code_sprint/`) - Has all the lessons but old branding
2. **Frontend App** (`code_sprint/frontend/`) - Has new branding but missing lessons

## ✅ **The Solution**
Integrate everything into the **frontend app** with new SPRINT - AI branding.

---

## 🚀 **Step 1: Run Integration Script**

```bash
npm run integrate-frontend
```

This will:
- ✅ Copy all lesson pages from root to frontend
- ✅ Copy data files and utilities
- ✅ Fix import paths
- ✅ Update logo references
- ✅ Create proper routing

---

## 🚀 **Step 2: Choose Your Frontend**

### **Option A: Use New Frontend (Recommended)**
```bash
# Run the new integrated frontend
npm run dev:full
```
**URLs:**
- Frontend: http://localhost:3000 (from frontend folder)
- Backend: http://localhost:5000

### **Option B: Use Root Frontend (Current)**
```bash
# Run the root frontend (where lessons currently work)
npm run dev:full-root
```
**URLs:**
- Frontend: http://localhost:3000 (from root folder)
- Backend: http://localhost:5000

---

## 🔍 **Step 3: Test Your Lessons**

### **After Integration, test these URLs:**

#### **Level 1 Lessons:**
- http://localhost:3000/lessons/level-1
- http://localhost:3000/lessons/level1/1.1WhatIsComputer
- http://localhost:3000/lessons/level1/1.2HowComputersWork

#### **Level 2 Lessons:**
- http://localhost:3000/lessons/level-2
- http://localhost:3000/lessons/level2/2.1WhatIsCybersecurity

#### **Level 3 Lessons:**
- http://localhost:3000/lessons/level-3
- http://localhost:3000/lessons/level3/3.1WhyVisualizeData

#### **Level 4 & 5:**
- http://localhost:3000/lessons/level-4
- http://localhost:3000/lessons/level-5

---

## 🎨 **Step 4: Verify SPRINT - AI Logos**

Check these pages for updated logos:
- ✅ Home: http://localhost:3000
- ✅ Login: http://localhost:3000/login
- ✅ Dashboard: http://localhost:3000/dashboard
- ✅ Courses: http://localhost:3000/courses

---

## 🐛 **Troubleshooting**

### **Issue 1: Lessons Not Found**
```bash
# Re-run integration
npm run integrate-frontend

# Then start frontend
cd frontend
npm run dev
```

### **Issue 2: Import Errors**
```bash
# Install dependencies in frontend
cd frontend
npm install --legacy-peer-deps
npm run dev
```

### **Issue 3: Logo Not Showing**
```bash
# Fix logo file names
npm run fix-logo-names

# Check if logos exist
npm run check-images
```

### **Issue 4: Port Conflicts**
```bash
# Kill existing processes
npx kill-port 3000
npx kill-port 5000

# Restart
npm run dev:full
```

---

## 📁 **File Structure After Integration**

```
code_sprint/
├── frontend/                    # ← Main frontend app
│   ├── src/
│   │   ├── pages/
│   │   │   ├── lessons/         # ← All lessons moved here
│   │   │   │   ├── level-1.js
│   │   │   │   ├── level-2.js
│   │   │   │   ├── level-3.js
│   │   │   │   ├── level1/      # ← Individual lesson files
│   │   │   │   ├── level2/
│   │   │   │   └── level3/
│   │   │   ├── index.jsx        # ← SPRINT - AI logo
│   │   │   ├── dashboard.jsx    # ← SPRINT - AI logo
│   │   │   └── login.jsx        # ← SPRINT - AI logo
│   │   ├── data/                # ← Lesson data
│   │   └── utils/               # ← Utilities
│   └── public/                  # ← SPRINT - AI logos
├── backend/                     # ← API server
└── pages/                       # ← Old structure (can be removed later)
```

---

## 🎯 **Expected Results**

### ✅ **Success Indicators:**
- All lesson URLs work: `/lessons/level-1`, `/lessons/level-2`, etc.
- SPRINT - AI logos appear on all pages
- No 404 errors for images or pages
- Both frontend and backend running smoothly

### ❌ **If Something's Wrong:**
1. Check which frontend is running (root vs frontend folder)
2. Verify lesson files were copied correctly
3. Check browser console for errors
4. Ensure logos are in the right public folder

---

## 🚀 **Quick Commands Reference**

```bash
# Integrate everything
npm run integrate-frontend

# Run new frontend + backend
npm run dev:full

# Run old frontend + backend (fallback)
npm run dev:full-root

# Check images
npm run check-images

# Fix logo names
npm run fix-logo-names
```

---

## 🎉 **Final Test Checklist**

- [ ] Integration script completed successfully
- [ ] Frontend starts without errors
- [ ] Backend connects properly
- [ ] Home page shows SPRINT - AI logo
- [ ] Login page shows SPRINT - AI logo
- [ ] Dashboard shows SPRINT - AI logo
- [ ] Level 1 lessons accessible: `/lessons/level-1`
- [ ] Individual lessons work: `/lessons/level1/1.1WhatIsComputer`
- [ ] All 5 levels accessible
- [ ] No 404 errors in browser console

---

**Ready to integrate? Run `npm run integrate-frontend` and then `npm run dev:full`!** 🚀
