"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["_pages-dir-browser_node_modules_splinetool_runtime_build_gaussian-splat-compression_js"],{

/***/ "(pages-dir-browser)/./node_modules/@splinetool/runtime/build/gaussian-splat-compression.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@splinetool/runtime/build/gaussian-splat-compression.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CompressedGaussianSplats: () => (/* binding */ X),\n/* harmony export */   GSplineBuffer: () => (/* binding */ ar),\n/* harmony export */   GaussianPLYData: () => (/* binding */ U)\n/* harmony export */ });\nvar Re=Object.create;var fr=Object.defineProperty;var Be=Object.getOwnPropertyDescriptor;var Ue=Object.getOwnPropertyNames;var Ye=Object.getPrototypeOf,Ge=Object.prototype.hasOwnProperty;var Qe=(r,e,n)=>e in r?fr(r,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):r[e]=n;var K=(r,e)=>()=>(e||r((e={exports:{}}).exports,e),e.exports),ur=(r,e)=>{for(var n in e)fr(r,n,{get:e[n],enumerable:!0})},$e=(r,e,n,a)=>{if(e&&typeof e==\"object\"||typeof e==\"function\")for(let t of Ue(e))!Ge.call(r,t)&&t!==n&&fr(r,t,{get:()=>e[t],enumerable:!(a=Be(e,t))||a.enumerable});return r};var Q=(r,e,n)=>(n=r!=null?Re(Ye(r)):{},$e(e||!r||!r.__esModule?fr(n,\"default\",{value:r,enumerable:!0}):n,r));var $=(r,e,n)=>(Qe(r,typeof e!=\"symbol\"?e+\"\":e,n),n);var Lr=K((gt,Cr)=>{\"use strict\";function He(r){for(var e=new Array(r),n=0;n<r;++n)e[n]=n;return e}Cr.exports=He});var Tr=K((Mt,Er)=>{Er.exports=function(r){return r!=null&&(Dr(r)||We(r)||!!r._isBuffer)};function Dr(r){return!!r.constructor&&typeof r.constructor.isBuffer==\"function\"&&r.constructor.isBuffer(r)}function We(r){return typeof r.readFloatLE==\"function\"&&typeof r.slice==\"function\"&&Dr(r.slice(0,0))}});var tr=K((_t,Pr)=>{var Ze=Lr(),Xe=Tr(),Ke=typeof Float64Array<\"u\";function Je(r,e){return r[0]-e[0]}function rn(){var r=this.stride,e=new Array(r.length),n;for(n=0;n<e.length;++n)e[n]=[Math.abs(r[n]),n];e.sort(Je);var a=new Array(e.length);for(n=0;n<a.length;++n)a[n]=e[n][1];return a}function en(r,e){var n=[\"View\",e,\"d\",r].join(\"\");e<0&&(n=\"View_Nil\"+r);var a=r===\"generic\";if(e===-1){var t=\"function \"+n+\"(a){this.data=a;};var proto=\"+n+\".prototype;proto.dtype='\"+r+\"';proto.index=function(){return -1};proto.size=0;proto.dimension=-1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function(){return new \"+n+\"(this.data);};proto.get=proto.set=function(){};proto.pick=function(){return null};return function construct_\"+n+\"(a){return new \"+n+\"(a);}\",d=new Function(t);return d()}else if(e===0){var t=\"function \"+n+\"(a,d) {this.data = a;this.offset = d};var proto=\"+n+\".prototype;proto.dtype='\"+r+\"';proto.index=function(){return this.offset};proto.dimension=0;proto.size=1;proto.shape=proto.stride=proto.order=[];proto.lo=proto.hi=proto.transpose=proto.step=function \"+n+\"_copy() {return new \"+n+\"(this.data,this.offset)};proto.pick=function \"+n+\"_pick(){return TrivialArray(this.data);};proto.valueOf=proto.get=function \"+n+\"_get(){return \"+(a?\"this.data.get(this.offset)\":\"this.data[this.offset]\")+\"};proto.set=function \"+n+\"_set(v){return \"+(a?\"this.data.set(this.offset,v)\":\"this.data[this.offset]=v\")+\"};return function construct_\"+n+\"(a,b,c,d){return new \"+n+\"(a,d)}\",d=new Function(\"TrivialArray\",t);return d(yr[r][0])}var t=[\"'use strict'\"],s=Ze(e),i=s.map(function(y){return\"i\"+y}),h=\"this.offset+\"+s.map(function(y){return\"this.stride[\"+y+\"]*i\"+y}).join(\"+\"),o=s.map(function(y){return\"b\"+y}).join(\",\"),c=s.map(function(y){return\"c\"+y}).join(\",\");t.push(\"function \"+n+\"(a,\"+o+\",\"+c+\",d){this.data=a\",\"this.shape=[\"+o+\"]\",\"this.stride=[\"+c+\"]\",\"this.offset=d|0}\",\"var proto=\"+n+\".prototype\",\"proto.dtype='\"+r+\"'\",\"proto.dimension=\"+e),t.push(\"Object.defineProperty(proto,'size',{get:function \"+n+\"_size(){return \"+s.map(function(y){return\"this.shape[\"+y+\"]\"}).join(\"*\"),\"}})\"),e===1?t.push(\"proto.order=[0]\"):(t.push(\"Object.defineProperty(proto,'order',{get:\"),e<4?(t.push(\"function \"+n+\"_order(){\"),e===2?t.push(\"return (Math.abs(this.stride[0])>Math.abs(this.stride[1]))?[1,0]:[0,1]}})\"):e===3&&t.push(\"var s0=Math.abs(this.stride[0]),s1=Math.abs(this.stride[1]),s2=Math.abs(this.stride[2]);if(s0>s1){if(s1>s2){return [2,1,0];}else if(s0>s2){return [1,2,0];}else{return [1,0,2];}}else if(s0>s2){return [2,0,1];}else if(s2>s1){return [0,1,2];}else{return [0,2,1];}}})\")):t.push(\"ORDER})\")),t.push(\"proto.set=function \"+n+\"_set(\"+i.join(\",\")+\",v){\"),a?t.push(\"return this.data.set(\"+h+\",v)}\"):t.push(\"return this.data[\"+h+\"]=v}\"),t.push(\"proto.get=function \"+n+\"_get(\"+i.join(\",\")+\"){\"),a?t.push(\"return this.data.get(\"+h+\")}\"):t.push(\"return this.data[\"+h+\"]}\"),t.push(\"proto.index=function \"+n+\"_index(\",i.join(),\"){return \"+h+\"}\"),t.push(\"proto.hi=function \"+n+\"_hi(\"+i.join(\",\")+\"){return new \"+n+\"(this.data,\"+s.map(function(y){return[\"(typeof i\",y,\"!=='number'||i\",y,\"<0)?this.shape[\",y,\"]:i\",y,\"|0\"].join(\"\")}).join(\",\")+\",\"+s.map(function(y){return\"this.stride[\"+y+\"]\"}).join(\",\")+\",this.offset)}\");var l=s.map(function(y){return\"a\"+y+\"=this.shape[\"+y+\"]\"}),f=s.map(function(y){return\"c\"+y+\"=this.stride[\"+y+\"]\"});t.push(\"proto.lo=function \"+n+\"_lo(\"+i.join(\",\")+\"){var b=this.offset,d=0,\"+l.join(\",\")+\",\"+f.join(\",\"));for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'&&i\"+p+\">=0){d=i\"+p+\"|0;b+=c\"+p+\"*d;a\"+p+\"-=d}\");t.push(\"return new \"+n+\"(this.data,\"+s.map(function(y){return\"a\"+y}).join(\",\")+\",\"+s.map(function(y){return\"c\"+y}).join(\",\")+\",b)}\"),t.push(\"proto.step=function \"+n+\"_step(\"+i.join(\",\")+\"){var \"+s.map(function(y){return\"a\"+y+\"=this.shape[\"+y+\"]\"}).join(\",\")+\",\"+s.map(function(y){return\"b\"+y+\"=this.stride[\"+y+\"]\"}).join(\",\")+\",c=this.offset,d=0,ceil=Math.ceil\");for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'){d=i\"+p+\"|0;if(d<0){c+=b\"+p+\"*(a\"+p+\"-1);a\"+p+\"=ceil(-a\"+p+\"/d)}else{a\"+p+\"=ceil(a\"+p+\"/d)}b\"+p+\"*=d}\");t.push(\"return new \"+n+\"(this.data,\"+s.map(function(y){return\"a\"+y}).join(\",\")+\",\"+s.map(function(y){return\"b\"+y}).join(\",\")+\",c)}\");for(var v=new Array(e),m=new Array(e),p=0;p<e;++p)v[p]=\"a[i\"+p+\"]\",m[p]=\"b[i\"+p+\"]\";t.push(\"proto.transpose=function \"+n+\"_transpose(\"+i+\"){\"+i.map(function(y,u){return y+\"=(\"+y+\"===undefined?\"+u+\":\"+y+\"|0)\"}).join(\";\"),\"var a=this.shape,b=this.stride;return new \"+n+\"(this.data,\"+v.join(\",\")+\",\"+m.join(\",\")+\",this.offset)}\"),t.push(\"proto.pick=function \"+n+\"_pick(\"+i+\"){var a=[],b=[],c=this.offset\");for(var p=0;p<e;++p)t.push(\"if(typeof i\"+p+\"==='number'&&i\"+p+\">=0){c=(c+this.stride[\"+p+\"]*i\"+p+\")|0}else{a.push(this.shape[\"+p+\"]);b.push(this.stride[\"+p+\"])}\");t.push(\"var ctor=CTOR_LIST[a.length+1];return ctor(this.data,a,b,c)}\"),t.push(\"return function construct_\"+n+\"(data,shape,stride,offset){return new \"+n+\"(data,\"+s.map(function(y){return\"shape[\"+y+\"]\"}).join(\",\")+\",\"+s.map(function(y){return\"stride[\"+y+\"]\"}).join(\",\")+\",offset)}\");var d=new Function(\"CTOR_LIST\",\"ORDER\",t.join(`\n`));return d(yr[r],rn)}function nn(r){if(Xe(r))return\"buffer\";if(Ke)switch(Object.prototype.toString.call(r)){case\"[object Float64Array]\":return\"float64\";case\"[object Float32Array]\":return\"float32\";case\"[object Int8Array]\":return\"int8\";case\"[object Int16Array]\":return\"int16\";case\"[object Int32Array]\":return\"int32\";case\"[object Uint8Array]\":return\"uint8\";case\"[object Uint16Array]\":return\"uint16\";case\"[object Uint32Array]\":return\"uint32\";case\"[object Uint8ClampedArray]\":return\"uint8_clamped\";case\"[object BigInt64Array]\":return\"bigint64\";case\"[object BigUint64Array]\":return\"biguint64\"}return Array.isArray(r)?\"array\":\"generic\"}var yr={float32:[],float64:[],int8:[],int16:[],int32:[],uint8:[],uint16:[],uint32:[],array:[],uint8_clamped:[],bigint64:[],biguint64:[],buffer:[],generic:[]};function an(r,e,n,a){if(r===void 0){var c=yr.array[0];return c([])}else typeof r==\"number\"&&(r=[r]);e===void 0&&(e=[r.length]);var t=e.length;if(n===void 0){n=new Array(t);for(var s=t-1,i=1;s>=0;--s)n[s]=i,i*=e[s]}if(a===void 0){a=0;for(var s=0;s<t;++s)n[s]<0&&(a-=(e[s]-1)*n[s])}for(var h=nn(r),o=yr[h];o.length<=t+1;)o.push(en(h,o.length-1));var c=o[t+1];return c(r,e,n,a)}Pr.exports=an});var Or=K((bt,Vr)=>{\"use strict\";function tn(r,e){for(var n=1,a=r.length,t=r[0],s=r[0],i=1;i<a;++i)if(s=t,t=r[i],e(t,s)){if(i===n){n++;continue}r[n++]=t}return r.length=n,r}function sn(r){for(var e=1,n=r.length,a=r[0],t=r[0],s=1;s<n;++s,t=a)if(t=a,a=r[s],a!==t){if(s===e){e++;continue}r[e++]=a}return r.length=e,r}function on(r,e,n){return r.length===0?r:e?(n||r.sort(e),tn(r,e)):(n||r.sort(),sn(r))}Vr.exports=on});var Br=K((At,Rr)=>{\"use strict\";var hn=Or();function jr(r,e,n){var a=r.length,t=e.arrayArgs.length,s=e.indexArgs.length>0,i=[],h=[],o=0,c=0,l,f;for(l=0;l<a;++l)h.push([\"i\",l,\"=0\"].join(\"\"));for(f=0;f<t;++f)for(l=0;l<a;++l)c=o,o=r[l],l===0?h.push([\"d\",f,\"s\",l,\"=t\",f,\"p\",o].join(\"\")):h.push([\"d\",f,\"s\",l,\"=(t\",f,\"p\",o,\"-s\",c,\"*t\",f,\"p\",c,\")\"].join(\"\"));for(h.length>0&&i.push(\"var \"+h.join(\",\")),l=a-1;l>=0;--l)o=r[l],i.push([\"for(i\",l,\"=0;i\",l,\"<s\",o,\";++i\",l,\"){\"].join(\"\"));for(i.push(n),l=0;l<a;++l){for(c=o,o=r[l],f=0;f<t;++f)i.push([\"p\",f,\"+=d\",f,\"s\",l].join(\"\"));s&&(l>0&&i.push([\"index[\",c,\"]-=s\",c].join(\"\")),i.push([\"++index[\",o,\"]\"].join(\"\"))),i.push(\"}\")}return i.join(`\n`)}function cn(r,e,n,a){for(var t=e.length,s=n.arrayArgs.length,i=n.blockSize,h=n.indexArgs.length>0,o=[],c=0;c<s;++c)o.push([\"var offset\",c,\"=p\",c].join(\"\"));for(var c=r;c<t;++c)o.push([\"for(var j\"+c+\"=SS[\",e[c],\"]|0;j\",c,\">0;){\"].join(\"\")),o.push([\"if(j\",c,\"<\",i,\"){\"].join(\"\")),o.push([\"s\",e[c],\"=j\",c].join(\"\")),o.push([\"j\",c,\"=0\"].join(\"\")),o.push([\"}else{s\",e[c],\"=\",i].join(\"\")),o.push([\"j\",c,\"-=\",i,\"}\"].join(\"\")),h&&o.push([\"index[\",e[c],\"]=j\",c].join(\"\"));for(var c=0;c<s;++c){for(var l=[\"offset\"+c],f=r;f<t;++f)l.push([\"j\",f,\"*t\",c,\"p\",e[f]].join(\"\"));o.push([\"p\",c,\"=(\",l.join(\"+\"),\")\"].join(\"\"))}o.push(jr(e,n,a));for(var c=r;c<t;++c)o.push(\"}\");return o.join(`\n`)}function ln(r){for(var e=0,n=r[0].length;e<n;){for(var a=1;a<r.length;++a)if(r[a][e]!==r[0][e])return e;++e}return e}function xr(r,e,n){for(var a=r.body,t=[],s=[],i=0;i<r.args.length;++i){var h=r.args[i];if(!(h.count<=0)){var o=new RegExp(h.name,\"g\"),c=\"\",l=e.arrayArgs.indexOf(i);switch(e.argTypes[i]){case\"offset\":var f=e.offsetArgIndex.indexOf(i),p=e.offsetArgs[f];l=p.array,c=\"+q\"+f;case\"array\":c=\"p\"+l+c;var v=\"l\"+i,m=\"a\"+l;if(e.arrayBlockIndices[l]===0)h.count===1?n[l]===\"generic\"?h.lvalue?(t.push([\"var \",v,\"=\",m,\".get(\",c,\")\"].join(\"\")),a=a.replace(o,v),s.push([m,\".set(\",c,\",\",v,\")\"].join(\"\"))):a=a.replace(o,[m,\".get(\",c,\")\"].join(\"\")):a=a.replace(o,[m,\"[\",c,\"]\"].join(\"\")):n[l]===\"generic\"?(t.push([\"var \",v,\"=\",m,\".get(\",c,\")\"].join(\"\")),a=a.replace(o,v),h.lvalue&&s.push([m,\".set(\",c,\",\",v,\")\"].join(\"\"))):(t.push([\"var \",v,\"=\",m,\"[\",c,\"]\"].join(\"\")),a=a.replace(o,v),h.lvalue&&s.push([m,\"[\",c,\"]=\",v].join(\"\")));else{for(var d=[h.name],y=[c],u=0;u<Math.abs(e.arrayBlockIndices[l]);u++)d.push(\"\\\\s*\\\\[([^\\\\]]+)\\\\]\"),y.push(\"$\"+(u+1)+\"*t\"+l+\"b\"+u);if(o=new RegExp(d.join(\"\"),\"g\"),c=y.join(\"+\"),n[l]===\"generic\")throw new Error(\"cwise: Generic arrays not supported in combination with blocks!\");a=a.replace(o,[m,\"[\",c,\"]\"].join(\"\"))}break;case\"scalar\":a=a.replace(o,\"Y\"+e.scalarArgs.indexOf(i));break;case\"index\":a=a.replace(o,\"index\");break;case\"shape\":a=a.replace(o,\"shape\");break}}}return[t.join(`\n`),a,s.join(`\n`)].join(`\n`).trim()}function pn(r){for(var e=new Array(r.length),n=!0,a=0;a<r.length;++a){var t=r[a],s=t.match(/\\d+/);s?s=s[0]:s=\"\",t.charAt(0)===0?e[a]=\"u\"+t.charAt(1)+s:e[a]=t.charAt(0)+s,a>0&&(n=n&&e[a]===e[a-1])}return n?e[0]:e.join(\"\")}function fn(r,e){for(var n=e[1].length-Math.abs(r.arrayBlockIndices[0])|0,a=new Array(r.arrayArgs.length),t=new Array(r.arrayArgs.length),s=0;s<r.arrayArgs.length;++s)t[s]=e[2*s],a[s]=e[2*s+1];for(var i=[],h=[],o=[],c=[],l=[],s=0;s<r.arrayArgs.length;++s){r.arrayBlockIndices[s]<0?(o.push(0),c.push(n),i.push(n),h.push(n+r.arrayBlockIndices[s])):(o.push(r.arrayBlockIndices[s]),c.push(r.arrayBlockIndices[s]+n),i.push(0),h.push(r.arrayBlockIndices[s]));for(var f=[],p=0;p<a[s].length;p++)o[s]<=a[s][p]&&a[s][p]<c[s]&&f.push(a[s][p]-o[s]);l.push(f)}for(var v=[\"SS\"],m=[\"'use strict'\"],d=[],p=0;p<n;++p)d.push([\"s\",p,\"=SS[\",p,\"]\"].join(\"\"));for(var s=0;s<r.arrayArgs.length;++s){v.push(\"a\"+s),v.push(\"t\"+s),v.push(\"p\"+s);for(var p=0;p<n;++p)d.push([\"t\",s,\"p\",p,\"=t\",s,\"[\",o[s]+p,\"]\"].join(\"\"));for(var p=0;p<Math.abs(r.arrayBlockIndices[s]);++p)d.push([\"t\",s,\"b\",p,\"=t\",s,\"[\",i[s]+p,\"]\"].join(\"\"))}for(var s=0;s<r.scalarArgs.length;++s)v.push(\"Y\"+s);if(r.shapeArgs.length>0&&d.push(\"shape=SS.slice(0)\"),r.indexArgs.length>0){for(var y=new Array(n),s=0;s<n;++s)y[s]=\"0\";d.push([\"index=[\",y.join(\",\"),\"]\"].join(\"\"))}for(var s=0;s<r.offsetArgs.length;++s){for(var u=r.offsetArgs[s],x=[],p=0;p<u.offset.length;++p)u.offset[p]!==0&&(u.offset[p]===1?x.push([\"t\",u.array,\"p\",p].join(\"\")):x.push([u.offset[p],\"*t\",u.array,\"p\",p].join(\"\")));x.length===0?d.push(\"q\"+s+\"=0\"):d.push([\"q\",s,\"=\",x.join(\"+\")].join(\"\"))}var M=hn([].concat(r.pre.thisVars).concat(r.body.thisVars).concat(r.post.thisVars));d=d.concat(M),d.length>0&&m.push(\"var \"+d.join(\",\"));for(var s=0;s<r.arrayArgs.length;++s)m.push(\"p\"+s+\"|=0\");r.pre.body.length>3&&m.push(xr(r.pre,r,t));var b=xr(r.body,r,t),_=ln(l);_<n?m.push(cn(_,l[0],r,b)):m.push(jr(l[0],r,b)),r.post.body.length>3&&m.push(xr(r.post,r,t)),r.debug&&console.log(\"-----Generated cwise routine for \",e,`:\n`+m.join(`\n`)+`\n----------`);var g=[r.funcName||\"unnamed\",\"_cwise_loop_\",a[0].join(\"s\"),\"m\",_,pn(t)].join(\"\"),N=new Function([\"function \",g,\"(\",v.join(\",\"),\"){\",m.join(`\n`),\"} return \",g].join(\"\"));return N()}Rr.exports=fn});var Yr=K((wt,Ur)=>{\"use strict\";var yn=Br();function dn(r){var e=[\"'use strict'\",\"var CACHED={}\"],n=[],a=r.funcName+\"_cwise_thunk\";e.push([\"return function \",a,\"(\",r.shimArgs.join(\",\"),\"){\"].join(\"\"));for(var t=[],s=[],i=[[\"array\",r.arrayArgs[0],\".shape.slice(\",Math.max(0,r.arrayBlockIndices[0]),r.arrayBlockIndices[0]<0?\",\"+r.arrayBlockIndices[0]+\")\":\")\"].join(\"\")],h=[],o=[],c=0;c<r.arrayArgs.length;++c){var l=r.arrayArgs[c];n.push([\"t\",l,\"=array\",l,\".dtype,\",\"r\",l,\"=array\",l,\".order\"].join(\"\")),t.push(\"t\"+l),t.push(\"r\"+l),s.push(\"t\"+l),s.push(\"r\"+l+\".join()\"),i.push(\"array\"+l+\".data\"),i.push(\"array\"+l+\".stride\"),i.push(\"array\"+l+\".offset|0\"),c>0&&(h.push(\"array\"+r.arrayArgs[0]+\".shape.length===array\"+l+\".shape.length+\"+(Math.abs(r.arrayBlockIndices[0])-Math.abs(r.arrayBlockIndices[c]))),o.push(\"array\"+r.arrayArgs[0]+\".shape[shapeIndex+\"+Math.max(0,r.arrayBlockIndices[0])+\"]===array\"+l+\".shape[shapeIndex+\"+Math.max(0,r.arrayBlockIndices[c])+\"]\"))}r.arrayArgs.length>1&&(e.push(\"if (!(\"+h.join(\" && \")+\")) throw new Error('cwise: Arrays do not all have the same dimensionality!')\"),e.push(\"for(var shapeIndex=array\"+r.arrayArgs[0]+\".shape.length-\"+Math.abs(r.arrayBlockIndices[0])+\"; shapeIndex-->0;) {\"),e.push(\"if (!(\"+o.join(\" && \")+\")) throw new Error('cwise: Arrays do not all have the same shape!')\"),e.push(\"}\"));for(var c=0;c<r.scalarArgs.length;++c)i.push(\"scalar\"+r.scalarArgs[c]);n.push([\"type=[\",s.join(\",\"),\"].join()\"].join(\"\")),n.push(\"proc=CACHED[type]\"),e.push(\"var \"+n.join(\",\")),e.push([\"if(!proc){\",\"CACHED[type]=proc=compile([\",t.join(\",\"),\"])}\",\"return proc(\",i.join(\",\"),\")}\"].join(\"\")),r.debug&&console.log(`-----Generated thunk:\n`+e.join(`\n`)+`\n----------`);var f=new Function(\"compile\",e.join(`\n`));return f(yn.bind(void 0,r))}Ur.exports=dn});var Qr=K((kt,Gr)=>{\"use strict\";var vn=Yr();function mn(){this.argTypes=[],this.shimArgs=[],this.arrayArgs=[],this.arrayBlockIndices=[],this.scalarArgs=[],this.offsetArgs=[],this.offsetArgIndex=[],this.indexArgs=[],this.shapeArgs=[],this.funcName=\"\",this.pre=null,this.body=null,this.post=null,this.debug=!1}function un(r){var e=new mn;e.pre=r.pre,e.body=r.body,e.post=r.post;var n=r.args.slice(0);e.argTypes=n;for(var a=0;a<n.length;++a){var t=n[a];if(t===\"array\"||typeof t==\"object\"&&t.blockIndices){if(e.argTypes[a]=\"array\",e.arrayArgs.push(a),e.arrayBlockIndices.push(t.blockIndices?t.blockIndices:0),e.shimArgs.push(\"array\"+a),a<e.pre.args.length&&e.pre.args[a].count>0)throw new Error(\"cwise: pre() block may not reference array args\");if(a<e.post.args.length&&e.post.args[a].count>0)throw new Error(\"cwise: post() block may not reference array args\")}else if(t===\"scalar\")e.scalarArgs.push(a),e.shimArgs.push(\"scalar\"+a);else if(t===\"index\"){if(e.indexArgs.push(a),a<e.pre.args.length&&e.pre.args[a].count>0)throw new Error(\"cwise: pre() block may not reference array index\");if(a<e.body.args.length&&e.body.args[a].lvalue)throw new Error(\"cwise: body() block may not write to array index\");if(a<e.post.args.length&&e.post.args[a].count>0)throw new Error(\"cwise: post() block may not reference array index\")}else if(t===\"shape\"){if(e.shapeArgs.push(a),a<e.pre.args.length&&e.pre.args[a].lvalue)throw new Error(\"cwise: pre() block may not write to array shape\");if(a<e.body.args.length&&e.body.args[a].lvalue)throw new Error(\"cwise: body() block may not write to array shape\");if(a<e.post.args.length&&e.post.args[a].lvalue)throw new Error(\"cwise: post() block may not write to array shape\")}else if(typeof t==\"object\"&&t.offset)e.argTypes[a]=\"offset\",e.offsetArgs.push({array:t.array,offset:t.offset}),e.offsetArgIndex.push(a);else throw new Error(\"cwise: Unknown argument type \"+n[a])}if(e.arrayArgs.length<=0)throw new Error(\"cwise: No array arguments specified\");if(e.pre.args.length>n.length)throw new Error(\"cwise: Too many arguments in pre() block\");if(e.body.args.length>n.length)throw new Error(\"cwise: Too many arguments in body() block\");if(e.post.args.length>n.length)throw new Error(\"cwise: Too many arguments in post() block\");return e.debug=!!r.printCode||!!r.debug,e.funcName=r.funcName||\"cwise\",e.blockSize=r.blockSize||64,vn(e)}Gr.exports=un});var sr=K(I=>{\"use strict\";var B=Qr(),dr={body:\"\",args:[],thisVars:[],localVars:[]};function gr(r){if(!r)return dr;for(var e=0;e<r.args.length;++e){var n=r.args[e];e===0?r.args[e]={name:n,lvalue:!0,rvalue:!!r.rvalue,count:r.count||1}:r.args[e]={name:n,lvalue:!1,rvalue:!0,count:1}}return r.thisVars||(r.thisVars=[]),r.localVars||(r.localVars=[]),r}function xn(r){return B({args:r.args,pre:gr(r.pre),body:gr(r.body),post:gr(r.proc),funcName:r.funcName})}function O(r){for(var e=[],n=0;n<r.args.length;++n)e.push(\"a\"+n);var a=new Function(\"P\",[\"return function \",r.funcName,\"_ndarrayops(\",e.join(\",\"),\") {P(\",e.join(\",\"),\");return a0}\"].join(\"\"));return a(xn(r))}var $r={add:\"+\",sub:\"-\",mul:\"*\",div:\"/\",mod:\"%\",band:\"&\",bor:\"|\",bxor:\"^\",lshift:\"<<\",rshift:\">>\",rrshift:\">>>\"};(function(){for(var r in $r){var e=$r[r];I[r]=O({args:[\"array\",\"array\",\"array\"],body:{args:[\"a\",\"b\",\"c\"],body:\"a=b\"+e+\"c\"},funcName:r}),I[r+\"eq\"]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a\"+e+\"=b\"},rvalue:!0,funcName:r+\"eq\"}),I[r+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],body:{args:[\"a\",\"b\",\"s\"],body:\"a=b\"+e+\"s\"},funcName:r+\"s\"}),I[r+\"seq\"]=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"s\"],body:\"a\"+e+\"=s\"},rvalue:!0,funcName:r+\"seq\"})}})();var Hr={not:\"!\",bnot:\"~\",neg:\"-\",recip:\"1.0/\"};(function(){for(var r in Hr){var e=Hr[r];I[r]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=\"+e+\"b\"},funcName:r}),I[r+\"eq\"]=O({args:[\"array\"],body:{args:[\"a\"],body:\"a=\"+e+\"a\"},rvalue:!0,count:2,funcName:r+\"eq\"})}})();var Wr={and:\"&&\",or:\"||\",eq:\"===\",neq:\"!==\",lt:\"<\",gt:\">\",leq:\"<=\",geq:\">=\"};(function(){for(var r in Wr){var e=Wr[r];I[r]=O({args:[\"array\",\"array\",\"array\"],body:{args:[\"a\",\"b\",\"c\"],body:\"a=b\"+e+\"c\"},funcName:r}),I[r+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],body:{args:[\"a\",\"b\",\"s\"],body:\"a=b\"+e+\"s\"},funcName:r+\"s\"}),I[r+\"eq\"]=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=a\"+e+\"b\"},rvalue:!0,count:2,funcName:r+\"eq\"}),I[r+\"seq\"]=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"s\"],body:\"a=a\"+e+\"s\"},rvalue:!0,count:2,funcName:r+\"seq\"})}})();var Zr=[\"abs\",\"acos\",\"asin\",\"atan\",\"ceil\",\"cos\",\"exp\",\"floor\",\"log\",\"round\",\"sin\",\"sqrt\",\"tan\"];(function(){for(var r=0;r<Zr.length;++r){var e=Zr[r];I[e]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b)\",thisVars:[\"this_f\"]},funcName:e}),I[e+\"eq\"]=O({args:[\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\"],body:\"a=this_f(a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"eq\"})}})();var Xr=[\"max\",\"min\",\"atan2\",\"pow\"];(function(){for(var r=0;r<Xr.length;++r){var e=Xr[r];I[e]=O({args:[\"array\",\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(b,c)\",thisVars:[\"this_f\"]},funcName:e}),I[e+\"s\"]=O({args:[\"array\",\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(b,c)\",thisVars:[\"this_f\"]},funcName:e+\"s\"}),I[e+\"eq\"]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(a,b)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"eq\"}),I[e+\"seq\"]=O({args:[\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(a,b)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"seq\"})}})();var Kr=[\"atan2\",\"pow\"];(function(){for(var r=0;r<Kr.length;++r){var e=Kr[r];I[e+\"op\"]=O({args:[\"array\",\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(c,b)\",thisVars:[\"this_f\"]},funcName:e+\"op\"}),I[e+\"ops\"]=O({args:[\"array\",\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\",\"c\"],body:\"a=this_f(c,b)\",thisVars:[\"this_f\"]},funcName:e+\"ops\"}),I[e+\"opeq\"]=O({args:[\"array\",\"array\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b,a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"opeq\"}),I[e+\"opseq\"]=O({args:[\"array\",\"scalar\"],pre:{args:[],body:\"this_f=Math.\"+e,thisVars:[\"this_f\"]},body:{args:[\"a\",\"b\"],body:\"a=this_f(b,a)\",thisVars:[\"this_f\"]},rvalue:!0,count:2,funcName:e+\"opseq\"})}})();I.any=B({args:[\"array\"],pre:dr,body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"if(a){return true}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return false\"},funcName:\"any\"});I.all=B({args:[\"array\"],pre:dr,body:{args:[{name:\"x\",lvalue:!1,rvalue:!0,count:1}],body:\"if(!x){return false}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return true\"},funcName:\"all\"});I.sum=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"this_s+=a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"sum\"});I.prod=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=1\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:1}],body:\"this_s*=a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"prod\"});I.norm2squared=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:2}],body:\"this_s+=a*a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norm2squared\"});I.norm2=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:2}],body:\"this_s+=a*a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return Math.sqrt(this_s)\"},funcName:\"norm2\"});I.norminf=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:4}],body:\"if(-a>this_s){this_s=-a}else if(a>this_s){this_s=a}\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norminf\"});I.norm1=B({args:[\"array\"],pre:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"this_s=0\"},body:{args:[{name:\"a\",lvalue:!1,rvalue:!0,count:3}],body:\"this_s+=a<0?-a:a\",localVars:[],thisVars:[\"this_s\"]},post:{args:[],localVars:[],thisVars:[\"this_s\"],body:\"return this_s\"},funcName:\"norm1\"});I.sup=B({args:[\"array\"],pre:{body:\"this_h=-Infinity\",args:[],thisVars:[\"this_h\"],localVars:[]},body:{body:\"if(_inline_1_arg0_>this_h)this_h=_inline_1_arg0_\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_h\"],localVars:[]},post:{body:\"return this_h\",args:[],thisVars:[\"this_h\"],localVars:[]}});I.inf=B({args:[\"array\"],pre:{body:\"this_h=Infinity\",args:[],thisVars:[\"this_h\"],localVars:[]},body:{body:\"if(_inline_1_arg0_<this_h)this_h=_inline_1_arg0_\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_h\"],localVars:[]},post:{body:\"return this_h\",args:[],thisVars:[\"this_h\"],localVars:[]}});I.argmin=B({args:[\"index\",\"array\",\"shape\"],pre:{body:\"{this_v=Infinity;this_i=_inline_0_arg2_.slice(0)}\",args:[{name:\"_inline_0_arg0_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg1_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg2_\",lvalue:!1,rvalue:!0,count:1}],thisVars:[\"this_i\",\"this_v\"],localVars:[]},body:{body:\"{if(_inline_1_arg1_<this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2},{name:\"_inline_1_arg1_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_i\",\"this_v\"],localVars:[\"_inline_1_k\"]},post:{body:\"{return this_i}\",args:[],thisVars:[\"this_i\"],localVars:[]}});I.argmax=B({args:[\"index\",\"array\",\"shape\"],pre:{body:\"{this_v=-Infinity;this_i=_inline_0_arg2_.slice(0)}\",args:[{name:\"_inline_0_arg0_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg1_\",lvalue:!1,rvalue:!1,count:0},{name:\"_inline_0_arg2_\",lvalue:!1,rvalue:!0,count:1}],thisVars:[\"this_i\",\"this_v\"],localVars:[]},body:{body:\"{if(_inline_1_arg1_>this_v){this_v=_inline_1_arg1_;for(var _inline_1_k=0;_inline_1_k<_inline_1_arg0_.length;++_inline_1_k){this_i[_inline_1_k]=_inline_1_arg0_[_inline_1_k]}}}\",args:[{name:\"_inline_1_arg0_\",lvalue:!1,rvalue:!0,count:2},{name:\"_inline_1_arg1_\",lvalue:!1,rvalue:!0,count:2}],thisVars:[\"this_i\",\"this_v\"],localVars:[\"_inline_1_k\"]},post:{body:\"{return this_i}\",args:[],thisVars:[\"this_i\"],localVars:[]}});I.random=O({args:[\"array\"],pre:{args:[],body:\"this_f=Math.random\",thisVars:[\"this_f\"]},body:{args:[\"a\"],body:\"a=this_f()\",thisVars:[\"this_f\"]},funcName:\"random\"});I.assign=O({args:[\"array\",\"array\"],body:{args:[\"a\",\"b\"],body:\"a=b\"},funcName:\"assign\"});I.assigns=O({args:[\"array\",\"scalar\"],body:{args:[\"a\",\"b\"],body:\"a=b\"},funcName:\"assigns\"});I.equals=B({args:[\"array\",\"array\"],pre:dr,body:{args:[{name:\"x\",lvalue:!1,rvalue:!0,count:1},{name:\"y\",lvalue:!1,rvalue:!0,count:1}],body:\"if(x!==y){return false}\",localVars:[],thisVars:[]},post:{args:[],localVars:[],thisVars:[],body:\"return true\"},funcName:\"equals\"})});var G=Q(tr(),1),H=Q(sr(),1);var j=Q(tr(),1),A=Q(sr(),1),J=class{constructor(e,n,a){this._dataNormalized=e,this._minD=n,this._maxD=a}static createFromUnnormalized(e){let n=A.sup(e),a=A.inf(e),t=(0,j.default)(new Float32Array(e.size),e.shape),s=n-a;return s<1e-4?A.assigns(t,0):(A.subs(t,e,a),A.divs(t,t,s)),new J(t,a,n)}get data(){return this._dataNormalized}get minD(){return this._minD}get maxD(){return this._maxD}denormalize(){let e=(0,j.default)(new Float32Array(this._dataNormalized.size),this._dataNormalized.shape);return A.muls(e,this._dataNormalized,this._maxD-this._minD),A.adds(e,e,this._minD),e}},D=class{constructor(e,n){this._quantized=e,this._method=n}get quantized(){return this._quantized}static maxIntBits(e){return 2**e-1}static fromNormalized(e,n){let a=e.data,t;if(n===\"norm8x\"){let s=D.maxIntBits(8),i=(0,j.default)(new Float32Array(a.size),a.shape);A.muls(i,a,s),A.roundeq(i),t=(0,j.default)(new Uint8Array(i.data),a.shape)}else if(n===\"norm565\"){let s=(0,j.default)(new Float32Array(a.size),a.shape);A.assign(s,a),A.mulseq(s.pick(null,0),D.maxIntBits(5)),A.mulseq(s.pick(null,1),D.maxIntBits(6)),A.mulseq(s.pick(null,2),D.maxIntBits(5)),A.roundeq(s);let i=(0,j.default)(new Uint16Array(s.data),a.shape),h=(0,j.default)(new Uint16Array(a.shape[0]),[a.shape[0]]),o=(0,j.default)(new Uint16Array(a.shape[0]),[a.shape[0]]);A.lshifts(h,i.pick(null,0),11),A.lshifts(o,i.pick(null,1),5),A.boreq(h,o),A.boreq(h,i.pick(null,2)),t=h}else{let s=(0,j.default)(new Float32Array(a.size),a.shape);A.assign(s,a),A.mulseq(s.pick(null,0),D.maxIntBits(11)),A.mulseq(s.pick(null,1),D.maxIntBits(10)),A.mulseq(s.pick(null,2),D.maxIntBits(11)),A.roundeq(s);let i=(0,j.default)(new Uint32Array(s.data),a.shape),h=(0,j.default)(new Uint32Array(a.shape[0]),[a.shape[0]]),o=(0,j.default)(new Uint32Array(a.shape[0]),[a.shape[0]]);A.lshifts(h,i.pick(null,0),21),A.lshifts(o,i.pick(null,1),11),A.boreq(h,o),A.boreq(h,i.pick(null,2)),t=h}return new D(t,n)}dequantize(e,n){let a=this._method,t,s=this._quantized;if(a===\"norm8x\"){let i=D.maxIntBits(8);t=(0,j.default)(new Float32Array(s.size),s.shape),A.muls(t,s,1/i)}else if(a===\"norm565\"){let i=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]),h=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]),o=(0,j.default)(new Uint8Array(s.shape[0]),[s.shape[0]]);A.rrshifts(i,s,11),A.rrshifts(h,s,5),A.bandseq(h,D.maxIntBits(6)),A.bands(o,s,D.maxIntBits(5)),t=(0,j.default)(new Float32Array(s.shape[0]*3),[s.shape[0],3]),A.muls(t.pick(null,0),i,1/D.maxIntBits(5)),A.muls(t.pick(null,1),h,1/D.maxIntBits(6)),A.muls(t.pick(null,2),o,1/D.maxIntBits(5))}else{let i=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]),h=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]),o=(0,j.default)(new Uint16Array(s.shape[0]),[s.shape[0]]);A.rrshifts(i,s,21),A.rrshifts(h,s,11),A.bandseq(h,D.maxIntBits(10)),A.bands(o,s,D.maxIntBits(11)),t=(0,j.default)(new Float32Array(s.shape[0]*3),[s.shape[0],3]),A.muls(t.pick(null,0),i,1/D.maxIntBits(11)),A.muls(t.pick(null,1),h,1/D.maxIntBits(10)),A.muls(t.pick(null,2),o,1/D.maxIntBits(11))}return new J(t,e,n)}};var E=class{constructor(e,n,a,t,s,i=!1){this._quantized=e,this._minMaxMatrix=n,this._chunkSize=a,this._quantizationMethod=t,this._variableChunkSize=s,this._isDynamicChunks=i}get length(){return this._quantized.shape[0]}get nchunks(){return this._minMaxMatrix.shape[0]}get quantized(){return this._quantized}get method(){return this._quantizationMethod}get minmaxMatrix(){return this._minMaxMatrix}_createPrunedMinMax(e){let n=e.length,a=this.minmaxMatrix.shape[0]-n,t=(0,G.default)(new Float32Array(a*2),[a,2]),s=0,i=a,h=0,o=this.minmaxMatrix.shape[0];for(let c=0;c<e.length;c++)o=e[c],i=o-h+s,i>s&&H.assign(t.hi(i,2).lo(s,0),this.minmaxMatrix.hi(o,2).lo(h,0)),s=i,h=o+1;return s<a&&H.assign(t.lo(s,0),this.minmaxMatrix.lo(h,0)),t}_createPrunedQuantized(e){let n=e.length,a=this.quantized.shape[0]-n,t=this._quantizationMethod,s,i;if(t===\"norm8x\"){i=this._quantized.shape[1];let f=i?a*i:a;s=(0,G.default)(new Uint8Array(f),i?[a,i]:[a,1])}else t===\"norm565\"?s=(0,G.default)(new Uint16Array(a),[a]):s=(0,G.default)(new Uint32Array(a),[a]);let h=0,o=a,c=0,l=s.shape[0];for(let f=0;f<e.length;f++)l=e[f],o=l-c+h,o>h&&(i?H.assign(s.hi(o,i).lo(h,0),this._quantized.hi(l,i).lo(c,0)):H.assign(s.hi(o).lo(h),this._quantized.hi(l).lo(c))),h=o,c=l+1;return h<a&&(i?H.assign(s.lo(h,0),this._quantized.lo(c,0)):H.assign(s.lo(h),this._quantized.lo(c))),s}pruneFeature(e,n,a){let t=this._createPrunedQuantized(e),s=this._createPrunedMinMax(n);return new E(t,s,this._chunkSize,this._quantizationMethod,a,!0)}static getRequiredNChunks(e,n){return Math.floor(e/n)}static fromArray(e,n,a){let t=e.shape[0],s=Math.floor(t/a),i=(0,G.default)(new Float32Array(s*2),[s,2],[2,1]),h;n===\"norm8x\"?h=(0,G.default)(new Uint8Array(e.size),e.shape):n===\"norm565\"?h=(0,G.default)(new Uint16Array(e.shape[0]),[e.shape[0]]):h=(0,G.default)(new Uint32Array(e.shape[0]),[e.shape[0]]);for(let o=0;o<s;o++){let c=o*a,l=o+1<s?(o+1)*a:t,f;e.shape.length>1?f=J.createFromUnnormalized(e.hi(l,e.shape[1]).lo(c,0)):f=J.createFromUnnormalized(e.hi(l).lo(c)),i.set(o,0,f.minD),i.set(o,1,f.maxD),h.shape.length>1?H.assign(h.hi(l,h.shape[1]).lo(c,0),D.fromNormalized(f,n).quantized):H.assign(h.hi(l).lo(c),D.fromNormalized(f,n).quantized)}return new E(h,i,a,n)}denormDequant(){let e=this._minMaxMatrix.shape[0],n=this._quantized,a=n.shape[0],t=this._quantizationMethod,s=this._chunkSize,i;if(this._isDynamicChunks){if(!this._variableChunkSize)throw new Error(\"variable chunk must exists if chunkSize isDynamic\");i=this._variableChunkSize}let h;t===\"norm8x\"?h=(0,G.default)(new Float32Array(n.size),n.shape):h=(0,G.default)(new Float32Array(a*3),[a,3]);let o=0,c=s;for(let l=0;l<e;l++){let[f,p]=[this._minMaxMatrix.get(l,0),this._minMaxMatrix.get(l,1)];this._isDynamicChunks&&(c=i[l]);let v=l+1<e?o+c:a,m;n.shape.length>1?m=new D(n.hi(v,n.shape[1]).lo(o,0),t):m=new D(n.hi(v).lo(o),t),H.assign(h.hi(v,h.shape[1]).lo(o,0),m.dequantize(f,p).denormalize()),o=v}return h}static async fetchArrayBuffer(e){return await(await fetch(e,{mode:\"cors\"})).arrayBuffer()}};var Z=Q(tr(),1),k=Q(sr(),1);var Jr=\"http://127.0.0.1:8000\";var er=Q(tr(),1),C=Q(sr(),1);var re=[1,10,100,1e3,1e4,1e5,1e6,1e7,1e8,1e9];function ee(r){return r<1e5?r<100?r<10?0:1:r<1e4?r<1e3?2:3:4:r<1e7?r<1e6?5:6:r<1e9?r<1e8?7:8:9}function ne(r,e){if(r===e)return 0;if(~~r===r&&~~e===e){if(r===0||e===0)return r<e?-1:1;if(r<0||e<0){if(e>=0)return-1;if(r>=0)return 1;r=-r,e=-e}let t=ee(r),s=ee(e),i=0;return t<s?(r*=re[s-t-1],e/=10,i=-1):t>s&&(e*=re[t-s-1],r/=10,i=1),r===e?i:r<e?-1:1}let n=String(r),a=String(e);return n===a?0:n<a?-1:1}function gn(r){let e=0;for(;r>=32;)e|=r&1,r>>=1;return r+e}function ae(r,e,n,a){let t=e+1;if(t===n)return 1;if(a(r[t++],r[e])<0){for(;t<n&&a(r[t],r[t-1])<0;)t++;Mn(r,e,t)}else for(;t<n&&a(r[t],r[t-1])>=0;)t++;return t-e}function Mn(r,e,n){for(n--;e<n;){let a=r[e];r[e++]=r[n],r[n--]=a}}function te(r,e,n,a,t){for(a===e&&a++;a<n;a++){let s=r[a],i=e,h=a;for(;i<h;){let c=i+h>>>1;t(s,r[c])<0?h=c:i=c+1}let o=a-i;switch(o){case 3:r[i+3]=r[i+2];case 2:r[i+2]=r[i+1];case 1:r[i+1]=r[i];break;default:for(;o>0;)r[i+o]=r[i+o-1],o--}r[i]=s}}function Mr(r,e,n,a,t,s){let i=0,h=0,o=1;if(s(r,e[n+t])>0){for(h=a-t;o<h&&s(r,e[n+t+o])>0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h),i+=t,o+=t}else{for(h=t+1;o<h&&s(r,e[n+t-o])<=0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h);let c=i;i=t-o,o=t-c}for(i++;i<o;){let c=i+(o-i>>>1);s(r,e[n+c])>0?i=c+1:o=c}return o}function _r(r,e,n,a,t,s){let i=0,h=0,o=1;if(s(r,e[n+t])<0){for(h=t+1;o<h&&s(r,e[n+t-o])<0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h);let c=i;i=t-o,o=t-c}else{for(h=a-t;o<h&&s(r,e[n+t+o])>=0;)i=o,o=(o<<1)+1,o<=0&&(o=h);o>h&&(o=h),i+=t,o+=t}for(i++;i<o;){let c=i+(o-i>>>1);s(r,e[n+c])<0?o=c:i=c+1}return o}var br=class{constructor(e,n){$(this,\"array\",null);$(this,\"compare\",null);$(this,\"minGallop\",7);$(this,\"length\",0);$(this,\"tmpStorageLength\",256);$(this,\"stackLength\",0);$(this,\"runStart\",null);$(this,\"runLength\",null);$(this,\"stackSize\",0);this.array=e,this.compare=n,this.length=e.length,this.length<2*256&&(this.tmpStorageLength=this.length>>>1),this.tmp=new Array(this.tmpStorageLength),this.stackLength=this.length<120?5:this.length<1542?10:this.length<119151?19:40,this.runStart=new Array(this.stackLength),this.runLength=new Array(this.stackLength)}pushRun(e,n){this.runStart[this.stackSize]=e,this.runLength[this.stackSize]=n,this.stackSize+=1}mergeRuns(){for(;this.stackSize>1;){let e=this.stackSize-2;if(e>=1&&this.runLength[e-1]<=this.runLength[e]+this.runLength[e+1]||e>=2&&this.runLength[e-2]<=this.runLength[e]+this.runLength[e-1])this.runLength[e-1]<this.runLength[e+1]&&e--;else if(this.runLength[e]>this.runLength[e+1])break;this.mergeAt(e)}}forceMergeRuns(){for(;this.stackSize>1;){let e=this.stackSize-2;e>0&&this.runLength[e-1]<this.runLength[e+1]&&e--,this.mergeAt(e)}}mergeAt(e){let n=this.compare,a=this.array,t=this.runStart[e],s=this.runLength[e],i=this.runStart[e+1],h=this.runLength[e+1];this.runLength[e]=s+h,e===this.stackSize-3&&(this.runStart[e+1]=this.runStart[e+2],this.runLength[e+1]=this.runLength[e+2]),this.stackSize--;let o=_r(a[i],a,t,s,0,n);t+=o,s-=o,s!==0&&(h=Mr(a[t+s-1],a,i,h,h-1,n),h!==0&&(s<=h?this.mergeLow(t,s,i,h):this.mergeHigh(t,s,i,h)))}mergeLow(e,n,a,t){let s=this.compare,i=this.array,h=this.tmp,o=0;for(o=0;o<n;o++)h[o]=i[e+o];let c=0,l=a,f=e;if(i[f++]=i[l++],--t===0){for(o=0;o<n;o++)i[f+o]=h[c+o];return}if(n===1){for(o=0;o<t;o++)i[f+o]=i[l+o];i[f+t]=h[c];return}let p=this.minGallop;for(;;){let v=0,m=0,d=!1;do if(s(i[l],h[c])<0){if(i[f++]=i[l++],m++,v=0,--t===0){d=!0;break}}else if(i[f++]=h[c++],v++,m=0,--n===1){d=!0;break}while((v|m)<p);if(d)break;do{if(v=_r(i[l],h,c,n,0,s),v!==0){for(o=0;o<v;o++)i[f+o]=h[c+o];if(f+=v,c+=v,n-=v,n<=1){d=!0;break}}if(i[f++]=i[l++],--t===0){d=!0;break}if(m=Mr(h[c],i,l,t,0,s),m!==0){for(o=0;o<m;o++)i[f+o]=i[l+o];if(f+=m,l+=m,t-=m,t===0){d=!0;break}}if(i[f++]=h[c++],--n===1){d=!0;break}p--}while(v>=7||m>=7);if(d)break;p<0&&(p=0),p+=2}if(this.minGallop=p,p<1&&(this.minGallop=1),n===1){for(o=0;o<t;o++)i[f+o]=i[l+o];i[f+t]=h[c]}else{if(n===0)throw new Error(\"mergeLow preconditions were not respected\");for(o=0;o<n;o++)i[f+o]=h[c+o]}}mergeHigh(e,n,a,t){let s=this.compare,i=this.array,h=this.tmp,o=0;for(o=0;o<t;o++)h[o]=i[a+o];let c=e+n-1,l=t-1,f=a+t-1,p=0,v=0;if(i[f--]=i[c--],--n===0){for(p=f-(t-1),o=0;o<t;o++)i[p+o]=h[o];return}if(t===1){for(f-=n,c-=n,v=f+1,p=c+1,o=n-1;o>=0;o--)i[v+o]=i[p+o];i[f]=h[l];return}let m=this.minGallop;for(;;){let d=0,y=0,u=!1;do if(s(h[l],i[c])<0){if(i[f--]=i[c--],d++,y=0,--n===0){u=!0;break}}else if(i[f--]=h[l--],y++,d=0,--t===1){u=!0;break}while((d|y)<m);if(u)break;do{if(d=n-_r(h[l],i,e,n,n-1,s),d!==0){for(f-=d,c-=d,n-=d,v=f+1,p=c+1,o=d-1;o>=0;o--)i[v+o]=i[p+o];if(n===0){u=!0;break}}if(i[f--]=h[l--],--t===1){u=!0;break}if(y=t-Mr(i[c],h,0,t,t-1,s),y!==0){for(f-=y,l-=y,t-=y,v=f+1,p=l+1,o=0;o<y;o++)i[v+o]=h[p+o];if(t<=1){u=!0;break}}if(i[f--]=i[c--],--n===0){u=!0;break}m--}while(d>=7||y>=7);if(u)break;m<0&&(m=0),m+=2}if(this.minGallop=m,m<1&&(this.minGallop=1),t===1){for(f-=n,c-=n,v=f+1,p=c+1,o=n-1;o>=0;o--)i[v+o]=i[p+o];i[f]=h[l]}else{if(t===0)throw new Error(\"mergeHigh preconditions were not respected\");for(p=f-(t-1),o=0;o<t;o++)i[p+o]=h[o]}}};function se(r,e,n,a){if(!Array.isArray(r))throw new TypeError(\"Can only sort arrays\");e?typeof e!=\"function\"&&(a=n,n=e,e=ne):e=ne,n||(n=0),a||(a=r.length);let t=a-n;if(t<2)return;let s=0;if(t<32){s=ae(r,n,a,e),te(r,n,a,n+s,e);return}let i=new br(r,e),h=gn(t);do{if(s=ae(r,n,a,e),s<h){let o=t;o>h&&(o=h),te(r,n,n+o,n+s,e),s=o}i.pushRun(n,s),i.mergeRuns(),t-=s,n+=s}while(t!==0);i.forceMergeRuns()}function Ar(r){let e=(0,er.default)(new Int32Array(r.shape[0]),[r.shape[0]]),n=(0,er.default)(new Int32Array(r.shape[0]),[r.shape[0]]);return C.bands(e,r,1023),C.lshifts(n,e,16),C.bxoreq(e,n),C.bandseq(e,4278190335),C.lshifts(n,e,8),C.bxoreq(e,n),C.bandseq(e,50393103),C.lshifts(n,e,4),C.bxoreq(e,n),C.bandseq(e,51130563),C.lshifts(n,e,2),C.bxoreq(e,n),C.bandseq(e,153391689),e}function _n(r){let e=Ar(r.pick(null,0)),n=Ar(r.pick(null,1));C.lshiftseq(n,1);let a=Ar(r.pick(null,2));return C.lshiftseq(a,2),C.boreq(e,n),C.boreq(e,a),e}function rr(r,e){if(r.shape[0]!==e.shape[0])throw new Error(\"wrong length\");let n=(0,er.default)(new Float32Array(r.size),r.shape,r.stride,r.offset);for(let a=0;a<e.shape[0];a++){let t=e.get(a);if(r.shape.length>1)for(let s=0;s<r.shape[1];s++)n.set(a,s,r.get(t,s));else n.set(a,r.get(t))}return n}function wr(r){let e=C.sup(r),n=C.inf(r),a=1e3/Math.min(1e3,e-n),t=(0,er.default)(new Float32Array(r.data),r.shape);C.mulseq(t,a);let s=(0,er.default)(new Int32Array(t.data),r.shape),i=_n(s),o=Array.from(i.data).map((f,p)=>[f,p]);se(o,(f,p)=>f[0]-p[0]);let c=o.map(([f,p])=>p);return(0,er.default)(Uint32Array.from(c))}var U=class{constructor(e,n,a,t,s,i,h,o,c,l){this.propertyDescs=e,this.format=n,this.nsplats=a,this.xyz=t,this.colors=s,this.harmonics=i,this.opacity=h,this.scaling=o,this.rotation=c,this.maxSHDegree=l}getPlyBinary(){let e=U._generateHeaderString(this.propertyDescs,this.format,this.nsplats),n=new TextEncoder().encode(e),a=Object.keys(this.propertyDescs).length,t=(0,Z.default)(new Float32Array(this.nsplats*a),[this.nsplats,a]);if(k.assign(t.pick(null,this.propertyDescs.x.index),this.xyz.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.y.index),this.xyz.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.z.index),this.xyz.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.f_dc_0.index),this.colors.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.f_dc_1.index),this.colors.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.f_dc_2.index),this.colors.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.opacity.index),this.opacity.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.scale_0.index),this.scaling.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.scale_1.index),this.scaling.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.scale_2.index),this.scaling.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.rot_0.index),this.rotation.pick(null,0)),k.assign(t.pick(null,this.propertyDescs.rot_1.index),this.rotation.pick(null,1)),k.assign(t.pick(null,this.propertyDescs.rot_2.index),this.rotation.pick(null,2)),k.assign(t.pick(null,this.propertyDescs.rot_3.index),this.rotation.pick(null,3)),this.harmonics&&this.harmonics.length>0)for(let h=0;h<this.harmonics.length;h++){let o=h*3;k.assign(t.pick(null,this.propertyDescs[`f_rest_${o}`].index),this.harmonics[h].pick(null,0)),k.assign(t.pick(null,this.propertyDescs[`f_rest_${o+1}`].index),this.harmonics[h].pick(null,1)),k.assign(t.pick(null,this.propertyDescs[`f_rest_${o+2}`].index),this.harmonics[h].pick(null,2))}let s=new Uint8Array(t.data.buffer),i=new Uint8Array(s.length+n.length);return i.set(n),i.set(s,n.length),i.buffer}save(e,n){let a=this.getPlyBinary(),t=new Blob([a],{type:\"application/octet-stream\"}),s=new File([t],e),i=new FormData;i.append(\"file\",s),i.append(\"filename\",e),i.append(\"basedir\",n),fetch(`${Jr}/push_file`,{method:\"POST\",body:i})}static async loadFile(e){return await(await fetch(e)).arrayBuffer()}mortonPositionSplatsSort(){let e=wr(this.xyz),n=rr(this.xyz,e),a=rr(this.colors,e),t=rr(this.opacity,e),s=rr(this.scaling,e),i=rr(this.rotation,e),h=[];for(let o=0;o<this.harmonics.length;o++)h.push(rr(this.harmonics[o],e));return new U(this.propertyDescs,this.format,this.nsplats,n,a,h,t,s,i,this.maxSHDegree)}static _generateHeaderString(e,n,a){let t=`ply\nformat ${n.format} ${n.version}\nelement vertex ${a}`,s=Object.keys(e).length,i=Array(s);for(let h in e){let o=e[h];i[o.index]={name:h,dtype:o.dtype}}for(let h=0;h<i.length;h++)t=`${t}\nproperty ${i[h].dtype} ${i[h].name}`;return`${t}\nend_header\n`}static fromArrayBuffer(e,n=3){let{splatCount:a,vertexData:t,propertiesDesc:s,format:i}=U.decodeHeader(e),h=t.buffer.slice(t.byteOffset),o=Object.keys(s).length,c=(0,Z.default)(new Float32Array(h),[a,o]),l=0,f={},p={double:8,int:4,uint:4,float:4,short:2,ushort:2,uchar:1,char:1};for(let _ in s)if(s.hasOwnProperty(_)){let g=s[_].dtype;f[_]=l,l+=p[g]}let v=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(v.pick(null,0),c.pick(null,f.x/4)),k.assign(v.pick(null,1),c.pick(null,f.y/4)),k.assign(v.pick(null,2),c.pick(null,f.z/4));let m=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(m.pick(null,0),c.pick(null,f.scale_0/4)),k.assign(m.pick(null,1),c.pick(null,f.scale_1/4)),k.assign(m.pick(null,2),c.pick(null,f.scale_2/4));let d=(0,Z.default)(new Float32Array(a*3),[a,3]);k.assign(d.pick(null,0),c.pick(null,f.f_dc_0/4)),k.assign(d.pick(null,1),c.pick(null,f.f_dc_1/4)),k.assign(d.pick(null,2),c.pick(null,f.f_dc_2/4));let y=(0,Z.default)(new Float32Array(a*4),[a,4]);k.assign(y.pick(null,0),c.pick(null,f.rot_1/4)),k.assign(y.pick(null,1),c.pick(null,f.rot_2/4)),k.assign(y.pick(null,2),c.pick(null,f.rot_3/4)),k.assign(y.pick(null,3),c.pick(null,f.rot_0/4));for(let _=0;_<a;_++){let g=y.pick(_,null),N=Math.sqrt(g.get(0)**2+g.get(1)**2+g.get(2)**2+g.get(3)**2);k.divseq(g,N)}let u=(0,Z.default)(new Float32Array(a*1),[a,1]);k.assign(u.pick(null,0),c.pick(null,f.opacity/4)),k.negeq(u),k.expeq(u),k.addseq(u,1),k.recipeq(u),k.mulseq(u,255);let M=(Math.min(Math.max(n,0),3)+1)**2-1,b=[];for(let _=0;_<M;_++){let g=(0,Z.default)(new Float32Array(a*3),[a,3]),N=_*3;k.assign(g.pick(null,0),c.pick(null,f[`f_rest_${N}`]/4)),k.assign(g.pick(null,1),c.pick(null,f[`f_rest_${N+1}`]/4)),k.assign(g.pick(null,2),c.pick(null,f[`f_rest_${N+2}`]/4)),b.push(g)}return new U(s,i,a,v,d,b,u,m,y,n)}static async fromPLYFile(e,n=3){let a=await U.loadFile(e);return U.fromArrayBuffer(a,n)}static decodeHeader(e){let n=new TextDecoder,a=0,t=\"\",s=100;for(;;){if(a+s>=e.byteLength)throw new Error(\"End of file reached while searching for end of header\");let m=new Uint8Array(e,a,s);t+=n.decode(m),a+=s;let d=a-s*2,y=new Uint8Array(e,Math.max(0,d),d>0?s*2:s);if(n.decode(y).includes(\"end_header\"))break}let i=t.split(`\n`),h=0,o={},c={},l=0,f;for(let m=0;m<i.length;m++){let d=i[m].trim();if(d.startsWith(\"element vertex\")){let y=d.match(/\\d+/);y&&(h=parseInt(y[0]))}else if(d.startsWith(\"property\")){let y=d.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);if(y){let u=y[2],x=y[3];o[x]=l,c[x]={dtype:u,index:l},l++}}else if(d.startsWith(\"format\")){let y=d.match(/(\\w+)\\s+(\\w+)\\s+(\\d+\\.?\\d*)/);y&&(f={format:y[2],version:y[3]})}else if(d===\"end_header\")break}let p=t.indexOf(\"end_header\")+10+1,v=new DataView(e,p);return{splatCount:h,vertexData:v,headerOffset:a,propertiesDesc:c,format:f}}};var X=class{constructor(e,n,a,t,s,i,h,o){this.config=e,this.xyz=n,this.scaling=a,this.color=t,this.opacity=s,this.harmonics=h,this.quaternion=i,this.variableChunkSize=o}get isDynamicChunks(){return this.variableChunkSize&&this.variableChunkSize.length>0}get nchunks(){return this.xyz.nchunks}get nsplats(){return this.xyz.length}get chunkSize(){return this.config.chunkSize}static compressFromGaussianData(e,n){let a=E.fromArray(e.xyz,n.xyz,n.chunkSize),t=E.fromArray(e.scaling,n.scaling,n.chunkSize),s=E.fromArray(e.colors,n.color,n.chunkSize),i=E.fromArray(e.opacity,n.opacity,n.chunkSize),h=E.fromArray(e.rotation,n.quaternion,n.chunkSize),o=e.harmonics,c=[];if(n.harmonics)for(let l=0;l<o.length;l++){let f=E.fromArray(o[l],n.harmonics,n.chunkSize);c.push(f)}return new X(n,a,t,s,i,h,c)}_countIndexesInChunks(e){let n=[],a=this.nchunks,t=this.chunkSize,s=this.nsplats,i=E.getRequiredNChunks(s,t);if(a===i)for(let h=0;h<e.length;h++){let o=e[h],c=Math.floor(o/this.chunkSize);c in n?n[c].push(o):n[c]=[o]}else{let h=this.variableChunkSize,o={},c=0;for(let l=0;l<a;l++)o[l]=c,c+=h[l];for(let l=0;l<e.length;l++){let f=e[l],p=Math.min(Math.floor(f/t),a-1);for(;f>=o[p]+h[p];)p++;p in n?n[p].push(f):n[p]=[f]}}return n}pruneSplats(e){let n=this._countIndexesInChunks(e),a,t=[];return n.length>0&&(a=this.variableChunkSize?[...this.variableChunkSize]:Array(this.nchunks).fill(this.chunkSize),n.forEach((s,i)=>{a[i]-=s.length,a[i]<=0&&t.push(i)}),a=a.filter(s=>s>0)),new X(this.config,this.xyz.pruneFeature(e,t,a),this.scaling.pruneFeature(e,t,a),this.color.pruneFeature(e,t,a),this.opacity.pruneFeature(e,t,a),this.quaternion.pruneFeature(e,t,a),this.harmonics?this.harmonics.map(s=>s.pruneFeature(e,t,this.variableChunkSize)):void 0,a)}static async loadConfig(e){return await(await fetch(e,{method:\"GET\",mode:\"cors\",headers:{Accept:\"application/json\"}})).json()}toGaussians(){let e={format:\"binary_little_endian\",version:\"1.0\"},n={},a=0;if(n.x={dtype:\"float\",index:a},a++,n.y={dtype:\"float\",index:a},a++,n.z={dtype:\"float\",index:a},a++,n.f_dc_0={dtype:\"float\",index:a},a++,n.f_dc_1={dtype:\"float\",index:a},a++,n.f_dc_2={dtype:\"float\",index:a},a++,this.harmonics&&this.harmonics.length>0)for(let i=0;i<this.harmonics.length;i++)n[`f_rest_${i}`]={dtype:\"float\",index:a},a++,n[`f_rest_${i+1}`]={dtype:\"float\",index:a},a++,n[`f_rest_${i+2}`]={dtype:\"float\",index:a},a++;n.opacity={dtype:\"float\",index:a},a++,n.scale_0={dtype:\"float\",index:a},a++,n.scale_1={dtype:\"float\",index:a},a++,n.scale_2={dtype:\"float\",index:a},a++,n.rot_0={dtype:\"float\",index:a},a++,n.rot_1={dtype:\"float\",index:a},a++,n.rot_2={dtype:\"float\",index:a},a++,n.rot_3={dtype:\"float\",index:a},a++;let t=this.harmonics?.map(i=>i.denormDequant());return new U(n,e,this.xyz.length,this.xyz.denormDequant(),this.color.denormDequant(),t||[],this.opacity.denormDequant(),this.scaling.denormDequant(),this.quaternion.denormDequant(),3)}};var nr=Q(tr(),1),R=Q(sr(),1);var S=1e-6,P=typeof Float32Array<\"u\"?Float32Array:Array,ir=Math.random;var Wt=Math.PI/180;Math.hypot||(Math.hypot=function(){for(var r=0,e=arguments.length;e--;)r+=arguments[e]*arguments[e];return Math.sqrt(r)});var W={};ur(W,{add:()=>Yn,adjoint:()=>Sn,clone:()=>An,copy:()=>wn,create:()=>kr,determinant:()=>In,equals:()=>Hn,exactEquals:()=>$n,frob:()=>Un,fromMat2d:()=>Vn,fromMat4:()=>bn,fromQuat:()=>On,fromRotation:()=>Tn,fromScaling:()=>Pn,fromTranslation:()=>En,fromValues:()=>kn,identity:()=>qn,invert:()=>Fn,mul:()=>Wn,multiply:()=>ie,multiplyScalar:()=>Gn,multiplyScalarAndAdd:()=>Qn,normalFromMat4:()=>jn,projection:()=>Rn,rotate:()=>Ln,scale:()=>Dn,set:()=>zn,str:()=>Bn,sub:()=>Zn,subtract:()=>oe,translate:()=>Cn,transpose:()=>Nn});function kr(){var r=new P(9);return P!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[5]=0,r[6]=0,r[7]=0),r[0]=1,r[4]=1,r[8]=1,r}function bn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[4],r[4]=e[5],r[5]=e[6],r[6]=e[8],r[7]=e[9],r[8]=e[10],r}function An(r){var e=new P(9);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e[4]=r[4],e[5]=r[5],e[6]=r[6],e[7]=r[7],e[8]=r[8],e}function wn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function kn(r,e,n,a,t,s,i,h,o){var c=new P(9);return c[0]=r,c[1]=e,c[2]=n,c[3]=a,c[4]=t,c[5]=s,c[6]=i,c[7]=h,c[8]=o,c}function zn(r,e,n,a,t,s,i,h,o,c){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r[4]=s,r[5]=i,r[6]=h,r[7]=o,r[8]=c,r}function qn(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Nn(r,e){if(r===e){var n=e[1],a=e[2],t=e[5];r[1]=e[3],r[2]=e[6],r[3]=n,r[5]=e[7],r[6]=a,r[7]=t}else r[0]=e[0],r[1]=e[3],r[2]=e[6],r[3]=e[1],r[4]=e[4],r[5]=e[7],r[6]=e[2],r[7]=e[5],r[8]=e[8];return r}function Fn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=l*i-h*c,p=-l*s+h*o,v=c*s-i*o,m=n*f+a*p+t*v;return m?(m=1/m,r[0]=f*m,r[1]=(-l*a+t*c)*m,r[2]=(h*a-t*i)*m,r[3]=p*m,r[4]=(l*n-t*o)*m,r[5]=(-h*n+t*s)*m,r[6]=v*m,r[7]=(-c*n+a*o)*m,r[8]=(i*n-a*s)*m,r):null}function Sn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8];return r[0]=i*l-h*c,r[1]=t*c-a*l,r[2]=a*h-t*i,r[3]=h*o-s*l,r[4]=n*l-t*o,r[5]=t*s-n*h,r[6]=s*c-i*o,r[7]=a*o-n*c,r[8]=n*i-a*s,r}function In(r){var e=r[0],n=r[1],a=r[2],t=r[3],s=r[4],i=r[5],h=r[6],o=r[7],c=r[8];return e*(c*s-i*o)+n*(-c*t+i*h)+a*(o*t-s*h)}function ie(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=n[0],v=n[1],m=n[2],d=n[3],y=n[4],u=n[5],x=n[6],M=n[7],b=n[8];return r[0]=p*a+v*i+m*c,r[1]=p*t+v*h+m*l,r[2]=p*s+v*o+m*f,r[3]=d*a+y*i+u*c,r[4]=d*t+y*h+u*l,r[5]=d*s+y*o+u*f,r[6]=x*a+M*i+b*c,r[7]=x*t+M*h+b*l,r[8]=x*s+M*o+b*f,r}function Cn(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=n[0],v=n[1];return r[0]=a,r[1]=t,r[2]=s,r[3]=i,r[4]=h,r[5]=o,r[6]=p*a+v*i+c,r[7]=p*t+v*h+l,r[8]=p*s+v*o+f,r}function Ln(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=Math.sin(n),v=Math.cos(n);return r[0]=v*a+p*i,r[1]=v*t+p*h,r[2]=v*s+p*o,r[3]=v*i-p*a,r[4]=v*h-p*t,r[5]=v*o-p*s,r[6]=c,r[7]=l,r[8]=f,r}function Dn(r,e,n){var a=n[0],t=n[1];return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=t*e[3],r[4]=t*e[4],r[5]=t*e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r}function En(r,e){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=1,r[5]=0,r[6]=e[0],r[7]=e[1],r[8]=1,r}function Tn(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=n,r[2]=0,r[3]=-n,r[4]=a,r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Pn(r,e){return r[0]=e[0],r[1]=0,r[2]=0,r[3]=0,r[4]=e[1],r[5]=0,r[6]=0,r[7]=0,r[8]=1,r}function Vn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=0,r[3]=e[2],r[4]=e[3],r[5]=0,r[6]=e[4],r[7]=e[5],r[8]=1,r}function On(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n+n,h=a+a,o=t+t,c=n*i,l=a*i,f=a*h,p=t*i,v=t*h,m=t*o,d=s*i,y=s*h,u=s*o;return r[0]=1-f-m,r[3]=l-u,r[6]=p+y,r[1]=l+u,r[4]=1-c-m,r[7]=v-d,r[2]=p-y,r[5]=v+d,r[8]=1-c-f,r}function jn(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15],x=n*h-a*i,M=n*o-t*i,b=n*c-s*i,_=a*o-t*h,g=a*c-s*h,N=t*c-s*o,q=l*d-f*m,w=l*y-p*m,F=l*u-v*m,L=f*y-p*d,T=f*u-v*d,V=p*u-v*y,z=x*V-M*T+b*L+_*F-g*w+N*q;return z?(z=1/z,r[0]=(h*V-o*T+c*L)*z,r[1]=(o*F-i*V-c*w)*z,r[2]=(i*T-h*F+c*q)*z,r[3]=(t*T-a*V-s*L)*z,r[4]=(n*V-t*F+s*w)*z,r[5]=(a*F-n*T-s*q)*z,r[6]=(d*N-y*g+u*_)*z,r[7]=(y*b-m*N-u*M)*z,r[8]=(m*g-d*b+u*x)*z,r):null}function Rn(r,e,n){return r[0]=2/e,r[1]=0,r[2]=0,r[3]=0,r[4]=-2/n,r[5]=0,r[6]=-1,r[7]=1,r[8]=1,r}function Bn(r){return\"mat3(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\", \"+r[4]+\", \"+r[5]+\", \"+r[6]+\", \"+r[7]+\", \"+r[8]+\")\"}function Un(r){return Math.hypot(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8])}function Yn(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r}function oe(r,e,n){return r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r}function Gn(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r}function Qn(r,e,n,a){return r[0]=e[0]+n[0]*a,r[1]=e[1]+n[1]*a,r[2]=e[2]+n[2]*a,r[3]=e[3]+n[3]*a,r[4]=e[4]+n[4]*a,r[5]=e[5]+n[5]*a,r[6]=e[6]+n[6]*a,r[7]=e[7]+n[7]*a,r[8]=e[8]+n[8]*a,r}function $n(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]&&r[4]===e[4]&&r[5]===e[5]&&r[6]===e[6]&&r[7]===e[7]&&r[8]===e[8]}function Hn(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=r[4],h=r[5],o=r[6],c=r[7],l=r[8],f=e[0],p=e[1],v=e[2],m=e[3],d=e[4],y=e[5],u=e[6],x=e[7],M=e[8];return Math.abs(n-f)<=S*Math.max(1,Math.abs(n),Math.abs(f))&&Math.abs(a-p)<=S*Math.max(1,Math.abs(a),Math.abs(p))&&Math.abs(t-v)<=S*Math.max(1,Math.abs(t),Math.abs(v))&&Math.abs(s-m)<=S*Math.max(1,Math.abs(s),Math.abs(m))&&Math.abs(i-d)<=S*Math.max(1,Math.abs(i),Math.abs(d))&&Math.abs(h-y)<=S*Math.max(1,Math.abs(h),Math.abs(y))&&Math.abs(o-u)<=S*Math.max(1,Math.abs(o),Math.abs(u))&&Math.abs(c-x)<=S*Math.max(1,Math.abs(c),Math.abs(x))&&Math.abs(l-M)<=S*Math.max(1,Math.abs(l),Math.abs(M))}var Wn=ie,Zn=oe;var lr={};ur(lr,{add:()=>Da,adjoint:()=>ta,clone:()=>Kn,copy:()=>Jn,create:()=>Xn,determinant:()=>sa,equals:()=>Va,exactEquals:()=>Pa,frob:()=>La,fromQuat:()=>Aa,fromQuat2:()=>xa,fromRotation:()=>da,fromRotationTranslation:()=>le,fromRotationTranslationScale:()=>_a,fromRotationTranslationScaleOrigin:()=>ba,fromScaling:()=>ya,fromTranslation:()=>fa,fromValues:()=>ra,fromXRotation:()=>va,fromYRotation:()=>ma,fromZRotation:()=>ua,frustum:()=>wa,getRotation:()=>Ma,getScaling:()=>pe,getTranslation:()=>ga,identity:()=>he,invert:()=>aa,lookAt:()=>Sa,mul:()=>Oa,multiply:()=>ce,multiplyScalar:()=>Ea,multiplyScalarAndAdd:()=>Ta,ortho:()=>Na,orthoNO:()=>ye,orthoZO:()=>Fa,perspective:()=>ka,perspectiveFromFieldOfView:()=>qa,perspectiveNO:()=>fe,perspectiveZO:()=>za,rotate:()=>ha,rotateX:()=>ca,rotateY:()=>la,rotateZ:()=>pa,scale:()=>oa,set:()=>ea,str:()=>Ca,sub:()=>ja,subtract:()=>de,targetTo:()=>Ia,translate:()=>ia,transpose:()=>na});function Xn(){var r=new P(16);return P!=Float32Array&&(r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=0,r[12]=0,r[13]=0,r[14]=0),r[0]=1,r[5]=1,r[10]=1,r[15]=1,r}function Kn(r){var e=new P(16);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e[4]=r[4],e[5]=r[5],e[6]=r[6],e[7]=r[7],e[8]=r[8],e[9]=r[9],e[10]=r[10],e[11]=r[11],e[12]=r[12],e[13]=r[13],e[14]=r[14],e[15]=r[15],e}function Jn(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r}function ra(r,e,n,a,t,s,i,h,o,c,l,f,p,v,m,d){var y=new P(16);return y[0]=r,y[1]=e,y[2]=n,y[3]=a,y[4]=t,y[5]=s,y[6]=i,y[7]=h,y[8]=o,y[9]=c,y[10]=l,y[11]=f,y[12]=p,y[13]=v,y[14]=m,y[15]=d,y}function ea(r,e,n,a,t,s,i,h,o,c,l,f,p,v,m,d,y){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r[4]=s,r[5]=i,r[6]=h,r[7]=o,r[8]=c,r[9]=l,r[10]=f,r[11]=p,r[12]=v,r[13]=m,r[14]=d,r[15]=y,r}function he(r){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function na(r,e){if(r===e){var n=e[1],a=e[2],t=e[3],s=e[6],i=e[7],h=e[11];r[1]=e[4],r[2]=e[8],r[3]=e[12],r[4]=n,r[6]=e[9],r[7]=e[13],r[8]=a,r[9]=s,r[11]=e[14],r[12]=t,r[13]=i,r[14]=h}else r[0]=e[0],r[1]=e[4],r[2]=e[8],r[3]=e[12],r[4]=e[1],r[5]=e[5],r[6]=e[9],r[7]=e[13],r[8]=e[2],r[9]=e[6],r[10]=e[10],r[11]=e[14],r[12]=e[3],r[13]=e[7],r[14]=e[11],r[15]=e[15];return r}function aa(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15],x=n*h-a*i,M=n*o-t*i,b=n*c-s*i,_=a*o-t*h,g=a*c-s*h,N=t*c-s*o,q=l*d-f*m,w=l*y-p*m,F=l*u-v*m,L=f*y-p*d,T=f*u-v*d,V=p*u-v*y,z=x*V-M*T+b*L+_*F-g*w+N*q;return z?(z=1/z,r[0]=(h*V-o*T+c*L)*z,r[1]=(t*T-a*V-s*L)*z,r[2]=(d*N-y*g+u*_)*z,r[3]=(p*g-f*N-v*_)*z,r[4]=(o*F-i*V-c*w)*z,r[5]=(n*V-t*F+s*w)*z,r[6]=(y*b-m*N-u*M)*z,r[7]=(l*N-p*b+v*M)*z,r[8]=(i*T-h*F+c*q)*z,r[9]=(a*F-n*T-s*q)*z,r[10]=(m*g-d*b+u*x)*z,r[11]=(f*b-l*g-v*x)*z,r[12]=(h*w-i*L-o*q)*z,r[13]=(n*L-a*w+t*q)*z,r[14]=(d*M-m*_-y*x)*z,r[15]=(l*_-f*M+p*x)*z,r):null}function ta(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=e[4],h=e[5],o=e[6],c=e[7],l=e[8],f=e[9],p=e[10],v=e[11],m=e[12],d=e[13],y=e[14],u=e[15];return r[0]=h*(p*u-v*y)-f*(o*u-c*y)+d*(o*v-c*p),r[1]=-(a*(p*u-v*y)-f*(t*u-s*y)+d*(t*v-s*p)),r[2]=a*(o*u-c*y)-h*(t*u-s*y)+d*(t*c-s*o),r[3]=-(a*(o*v-c*p)-h*(t*v-s*p)+f*(t*c-s*o)),r[4]=-(i*(p*u-v*y)-l*(o*u-c*y)+m*(o*v-c*p)),r[5]=n*(p*u-v*y)-l*(t*u-s*y)+m*(t*v-s*p),r[6]=-(n*(o*u-c*y)-i*(t*u-s*y)+m*(t*c-s*o)),r[7]=n*(o*v-c*p)-i*(t*v-s*p)+l*(t*c-s*o),r[8]=i*(f*u-v*d)-l*(h*u-c*d)+m*(h*v-c*f),r[9]=-(n*(f*u-v*d)-l*(a*u-s*d)+m*(a*v-s*f)),r[10]=n*(h*u-c*d)-i*(a*u-s*d)+m*(a*c-s*h),r[11]=-(n*(h*v-c*f)-i*(a*v-s*f)+l*(a*c-s*h)),r[12]=-(i*(f*y-p*d)-l*(h*y-o*d)+m*(h*p-o*f)),r[13]=n*(f*y-p*d)-l*(a*y-t*d)+m*(a*p-t*f),r[14]=-(n*(h*y-o*d)-i*(a*y-t*d)+m*(a*o-t*h)),r[15]=n*(h*p-o*f)-i*(a*p-t*f)+l*(a*o-t*h),r}function sa(r){var e=r[0],n=r[1],a=r[2],t=r[3],s=r[4],i=r[5],h=r[6],o=r[7],c=r[8],l=r[9],f=r[10],p=r[11],v=r[12],m=r[13],d=r[14],y=r[15],u=e*i-n*s,x=e*h-a*s,M=e*o-t*s,b=n*h-a*i,_=n*o-t*i,g=a*o-t*h,N=c*m-l*v,q=c*d-f*v,w=c*y-p*v,F=l*d-f*m,L=l*y-p*m,T=f*y-p*d;return u*T-x*L+M*F+b*w-_*q+g*N}function ce(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=e[8],p=e[9],v=e[10],m=e[11],d=e[12],y=e[13],u=e[14],x=e[15],M=n[0],b=n[1],_=n[2],g=n[3];return r[0]=M*a+b*h+_*f+g*d,r[1]=M*t+b*o+_*p+g*y,r[2]=M*s+b*c+_*v+g*u,r[3]=M*i+b*l+_*m+g*x,M=n[4],b=n[5],_=n[6],g=n[7],r[4]=M*a+b*h+_*f+g*d,r[5]=M*t+b*o+_*p+g*y,r[6]=M*s+b*c+_*v+g*u,r[7]=M*i+b*l+_*m+g*x,M=n[8],b=n[9],_=n[10],g=n[11],r[8]=M*a+b*h+_*f+g*d,r[9]=M*t+b*o+_*p+g*y,r[10]=M*s+b*c+_*v+g*u,r[11]=M*i+b*l+_*m+g*x,M=n[12],b=n[13],_=n[14],g=n[15],r[12]=M*a+b*h+_*f+g*d,r[13]=M*t+b*o+_*p+g*y,r[14]=M*s+b*c+_*v+g*u,r[15]=M*i+b*l+_*m+g*x,r}function ia(r,e,n){var a=n[0],t=n[1],s=n[2],i,h,o,c,l,f,p,v,m,d,y,u;return e===r?(r[12]=e[0]*a+e[4]*t+e[8]*s+e[12],r[13]=e[1]*a+e[5]*t+e[9]*s+e[13],r[14]=e[2]*a+e[6]*t+e[10]*s+e[14],r[15]=e[3]*a+e[7]*t+e[11]*s+e[15]):(i=e[0],h=e[1],o=e[2],c=e[3],l=e[4],f=e[5],p=e[6],v=e[7],m=e[8],d=e[9],y=e[10],u=e[11],r[0]=i,r[1]=h,r[2]=o,r[3]=c,r[4]=l,r[5]=f,r[6]=p,r[7]=v,r[8]=m,r[9]=d,r[10]=y,r[11]=u,r[12]=i*a+l*t+m*s+e[12],r[13]=h*a+f*t+d*s+e[13],r[14]=o*a+p*t+y*s+e[14],r[15]=c*a+v*t+u*s+e[15]),r}function oa(r,e,n){var a=n[0],t=n[1],s=n[2];return r[0]=e[0]*a,r[1]=e[1]*a,r[2]=e[2]*a,r[3]=e[3]*a,r[4]=e[4]*t,r[5]=e[5]*t,r[6]=e[6]*t,r[7]=e[7]*t,r[8]=e[8]*s,r[9]=e[9]*s,r[10]=e[10]*s,r[11]=e[11]*s,r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15],r}function ha(r,e,n,a){var t=a[0],s=a[1],i=a[2],h=Math.hypot(t,s,i),o,c,l,f,p,v,m,d,y,u,x,M,b,_,g,N,q,w,F,L,T,V,z,Y;return h<S?null:(h=1/h,t*=h,s*=h,i*=h,o=Math.sin(n),c=Math.cos(n),l=1-c,f=e[0],p=e[1],v=e[2],m=e[3],d=e[4],y=e[5],u=e[6],x=e[7],M=e[8],b=e[9],_=e[10],g=e[11],N=t*t*l+c,q=s*t*l+i*o,w=i*t*l-s*o,F=t*s*l-i*o,L=s*s*l+c,T=i*s*l+t*o,V=t*i*l+s*o,z=s*i*l-t*o,Y=i*i*l+c,r[0]=f*N+d*q+M*w,r[1]=p*N+y*q+b*w,r[2]=v*N+u*q+_*w,r[3]=m*N+x*q+g*w,r[4]=f*F+d*L+M*T,r[5]=p*F+y*L+b*T,r[6]=v*F+u*L+_*T,r[7]=m*F+x*L+g*T,r[8]=f*V+d*z+M*Y,r[9]=p*V+y*z+b*Y,r[10]=v*V+u*z+_*Y,r[11]=m*V+x*z+g*Y,e!==r&&(r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r)}function ca(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[4],i=e[5],h=e[6],o=e[7],c=e[8],l=e[9],f=e[10],p=e[11];return e!==r&&(r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[4]=s*t+c*a,r[5]=i*t+l*a,r[6]=h*t+f*a,r[7]=o*t+p*a,r[8]=c*t-s*a,r[9]=l*t-i*a,r[10]=f*t-h*a,r[11]=p*t-o*a,r}function la(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[0],i=e[1],h=e[2],o=e[3],c=e[8],l=e[9],f=e[10],p=e[11];return e!==r&&(r[4]=e[4],r[5]=e[5],r[6]=e[6],r[7]=e[7],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[0]=s*t-c*a,r[1]=i*t-l*a,r[2]=h*t-f*a,r[3]=o*t-p*a,r[8]=s*a+c*t,r[9]=i*a+l*t,r[10]=h*a+f*t,r[11]=o*a+p*t,r}function pa(r,e,n){var a=Math.sin(n),t=Math.cos(n),s=e[0],i=e[1],h=e[2],o=e[3],c=e[4],l=e[5],f=e[6],p=e[7];return e!==r&&(r[8]=e[8],r[9]=e[9],r[10]=e[10],r[11]=e[11],r[12]=e[12],r[13]=e[13],r[14]=e[14],r[15]=e[15]),r[0]=s*t+c*a,r[1]=i*t+l*a,r[2]=h*t+f*a,r[3]=o*t+p*a,r[4]=c*t-s*a,r[5]=l*t-i*a,r[6]=f*t-h*a,r[7]=p*t-o*a,r}function fa(r,e){return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=e[0],r[13]=e[1],r[14]=e[2],r[15]=1,r}function ya(r,e){return r[0]=e[0],r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=e[1],r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=e[2],r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function da(r,e,n){var a=n[0],t=n[1],s=n[2],i=Math.hypot(a,t,s),h,o,c;return i<S?null:(i=1/i,a*=i,t*=i,s*=i,h=Math.sin(e),o=Math.cos(e),c=1-o,r[0]=a*a*c+o,r[1]=t*a*c+s*h,r[2]=s*a*c-t*h,r[3]=0,r[4]=a*t*c-s*h,r[5]=t*t*c+o,r[6]=s*t*c+a*h,r[7]=0,r[8]=a*s*c+t*h,r[9]=t*s*c-a*h,r[10]=s*s*c+o,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r)}function va(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=1,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=a,r[6]=n,r[7]=0,r[8]=0,r[9]=-n,r[10]=a,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function ma(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=0,r[2]=-n,r[3]=0,r[4]=0,r[5]=1,r[6]=0,r[7]=0,r[8]=n,r[9]=0,r[10]=a,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function ua(r,e){var n=Math.sin(e),a=Math.cos(e);return r[0]=a,r[1]=n,r[2]=0,r[3]=0,r[4]=-n,r[5]=a,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=1,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function le(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=a+a,o=t+t,c=s+s,l=a*h,f=a*o,p=a*c,v=t*o,m=t*c,d=s*c,y=i*h,u=i*o,x=i*c;return r[0]=1-(v+d),r[1]=f+x,r[2]=p-u,r[3]=0,r[4]=f-x,r[5]=1-(l+d),r[6]=m+y,r[7]=0,r[8]=p+u,r[9]=m-y,r[10]=1-(l+v),r[11]=0,r[12]=n[0],r[13]=n[1],r[14]=n[2],r[15]=1,r}function xa(r,e){var n=new P(3),a=-e[0],t=-e[1],s=-e[2],i=e[3],h=e[4],o=e[5],c=e[6],l=e[7],f=a*a+t*t+s*s+i*i;return f>0?(n[0]=(h*i+l*a+o*s-c*t)*2/f,n[1]=(o*i+l*t+c*a-h*s)*2/f,n[2]=(c*i+l*s+h*t-o*a)*2/f):(n[0]=(h*i+l*a+o*s-c*t)*2,n[1]=(o*i+l*t+c*a-h*s)*2,n[2]=(c*i+l*s+h*t-o*a)*2),le(r,e,n),r}function ga(r,e){return r[0]=e[12],r[1]=e[13],r[2]=e[14],r}function pe(r,e){var n=e[0],a=e[1],t=e[2],s=e[4],i=e[5],h=e[6],o=e[8],c=e[9],l=e[10];return r[0]=Math.hypot(n,a,t),r[1]=Math.hypot(s,i,h),r[2]=Math.hypot(o,c,l),r}function Ma(r,e){var n=new P(3);pe(n,e);var a=1/n[0],t=1/n[1],s=1/n[2],i=e[0]*a,h=e[1]*t,o=e[2]*s,c=e[4]*a,l=e[5]*t,f=e[6]*s,p=e[8]*a,v=e[9]*t,m=e[10]*s,d=i+l+m,y=0;return d>0?(y=Math.sqrt(d+1)*2,r[3]=.25*y,r[0]=(f-v)/y,r[1]=(p-o)/y,r[2]=(h-c)/y):i>l&&i>m?(y=Math.sqrt(1+i-l-m)*2,r[3]=(f-v)/y,r[0]=.25*y,r[1]=(h+c)/y,r[2]=(p+o)/y):l>m?(y=Math.sqrt(1+l-i-m)*2,r[3]=(p-o)/y,r[0]=(h+c)/y,r[1]=.25*y,r[2]=(f+v)/y):(y=Math.sqrt(1+m-i-l)*2,r[3]=(h-c)/y,r[0]=(p+o)/y,r[1]=(f+v)/y,r[2]=.25*y),r}function _a(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3],o=t+t,c=s+s,l=i+i,f=t*o,p=t*c,v=t*l,m=s*c,d=s*l,y=i*l,u=h*o,x=h*c,M=h*l,b=a[0],_=a[1],g=a[2];return r[0]=(1-(m+y))*b,r[1]=(p+M)*b,r[2]=(v-x)*b,r[3]=0,r[4]=(p-M)*_,r[5]=(1-(f+y))*_,r[6]=(d+u)*_,r[7]=0,r[8]=(v+x)*g,r[9]=(d-u)*g,r[10]=(1-(f+m))*g,r[11]=0,r[12]=n[0],r[13]=n[1],r[14]=n[2],r[15]=1,r}function ba(r,e,n,a,t){var s=e[0],i=e[1],h=e[2],o=e[3],c=s+s,l=i+i,f=h+h,p=s*c,v=s*l,m=s*f,d=i*l,y=i*f,u=h*f,x=o*c,M=o*l,b=o*f,_=a[0],g=a[1],N=a[2],q=t[0],w=t[1],F=t[2],L=(1-(d+u))*_,T=(v+b)*_,V=(m-M)*_,z=(v-b)*g,Y=(1-(p+u))*g,or=(y+x)*g,hr=(m+M)*N,Sr=(y-x)*N,Ir=(1-(p+d))*N;return r[0]=L,r[1]=T,r[2]=V,r[3]=0,r[4]=z,r[5]=Y,r[6]=or,r[7]=0,r[8]=hr,r[9]=Sr,r[10]=Ir,r[11]=0,r[12]=n[0]+q-(L*q+z*w+hr*F),r[13]=n[1]+w-(T*q+Y*w+Sr*F),r[14]=n[2]+F-(V*q+or*w+Ir*F),r[15]=1,r}function Aa(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n+n,h=a+a,o=t+t,c=n*i,l=a*i,f=a*h,p=t*i,v=t*h,m=t*o,d=s*i,y=s*h,u=s*o;return r[0]=1-f-m,r[1]=l+u,r[2]=p-y,r[3]=0,r[4]=l-u,r[5]=1-c-m,r[6]=v+d,r[7]=0,r[8]=p+y,r[9]=v-d,r[10]=1-c-f,r[11]=0,r[12]=0,r[13]=0,r[14]=0,r[15]=1,r}function wa(r,e,n,a,t,s,i){var h=1/(n-e),o=1/(t-a),c=1/(s-i);return r[0]=s*2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s*2*o,r[6]=0,r[7]=0,r[8]=(n+e)*h,r[9]=(t+a)*o,r[10]=(i+s)*c,r[11]=-1,r[12]=0,r[13]=0,r[14]=i*s*2*c,r[15]=0,r}function fe(r,e,n,a,t){var s=1/Math.tan(e/2),i;return r[0]=s/n,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=-1,r[12]=0,r[13]=0,r[15]=0,t!=null&&t!==1/0?(i=1/(a-t),r[10]=(t+a)*i,r[14]=2*t*a*i):(r[10]=-1,r[14]=-2*a),r}var ka=fe;function za(r,e,n,a,t){var s=1/Math.tan(e/2),i;return r[0]=s/n,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=s,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[11]=-1,r[12]=0,r[13]=0,r[15]=0,t!=null&&t!==1/0?(i=1/(a-t),r[10]=t*i,r[14]=t*a*i):(r[10]=-1,r[14]=-a),r}function qa(r,e,n,a){var t=Math.tan(e.upDegrees*Math.PI/180),s=Math.tan(e.downDegrees*Math.PI/180),i=Math.tan(e.leftDegrees*Math.PI/180),h=Math.tan(e.rightDegrees*Math.PI/180),o=2/(i+h),c=2/(t+s);return r[0]=o,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=c,r[6]=0,r[7]=0,r[8]=-((i-h)*o*.5),r[9]=(t-s)*c*.5,r[10]=a/(n-a),r[11]=-1,r[12]=0,r[13]=0,r[14]=a*n/(n-a),r[15]=0,r}function ye(r,e,n,a,t,s,i){var h=1/(e-n),o=1/(a-t),c=1/(s-i);return r[0]=-2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=-2*o,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=2*c,r[11]=0,r[12]=(e+n)*h,r[13]=(t+a)*o,r[14]=(i+s)*c,r[15]=1,r}var Na=ye;function Fa(r,e,n,a,t,s,i){var h=1/(e-n),o=1/(a-t),c=1/(s-i);return r[0]=-2*h,r[1]=0,r[2]=0,r[3]=0,r[4]=0,r[5]=-2*o,r[6]=0,r[7]=0,r[8]=0,r[9]=0,r[10]=c,r[11]=0,r[12]=(e+n)*h,r[13]=(t+a)*o,r[14]=s*c,r[15]=1,r}function Sa(r,e,n,a){var t,s,i,h,o,c,l,f,p,v,m=e[0],d=e[1],y=e[2],u=a[0],x=a[1],M=a[2],b=n[0],_=n[1],g=n[2];return Math.abs(m-b)<S&&Math.abs(d-_)<S&&Math.abs(y-g)<S?he(r):(l=m-b,f=d-_,p=y-g,v=1/Math.hypot(l,f,p),l*=v,f*=v,p*=v,t=x*p-M*f,s=M*l-u*p,i=u*f-x*l,v=Math.hypot(t,s,i),v?(v=1/v,t*=v,s*=v,i*=v):(t=0,s=0,i=0),h=f*i-p*s,o=p*t-l*i,c=l*s-f*t,v=Math.hypot(h,o,c),v?(v=1/v,h*=v,o*=v,c*=v):(h=0,o=0,c=0),r[0]=t,r[1]=h,r[2]=l,r[3]=0,r[4]=s,r[5]=o,r[6]=f,r[7]=0,r[8]=i,r[9]=c,r[10]=p,r[11]=0,r[12]=-(t*m+s*d+i*y),r[13]=-(h*m+o*d+c*y),r[14]=-(l*m+f*d+p*y),r[15]=1,r)}function Ia(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=a[0],o=a[1],c=a[2],l=t-n[0],f=s-n[1],p=i-n[2],v=l*l+f*f+p*p;v>0&&(v=1/Math.sqrt(v),l*=v,f*=v,p*=v);var m=o*p-c*f,d=c*l-h*p,y=h*f-o*l;return v=m*m+d*d+y*y,v>0&&(v=1/Math.sqrt(v),m*=v,d*=v,y*=v),r[0]=m,r[1]=d,r[2]=y,r[3]=0,r[4]=f*y-p*d,r[5]=p*m-l*y,r[6]=l*d-f*m,r[7]=0,r[8]=l,r[9]=f,r[10]=p,r[11]=0,r[12]=t,r[13]=s,r[14]=i,r[15]=1,r}function Ca(r){return\"mat4(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\", \"+r[4]+\", \"+r[5]+\", \"+r[6]+\", \"+r[7]+\", \"+r[8]+\", \"+r[9]+\", \"+r[10]+\", \"+r[11]+\", \"+r[12]+\", \"+r[13]+\", \"+r[14]+\", \"+r[15]+\")\"}function La(r){return Math.hypot(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15])}function Da(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r[4]=e[4]+n[4],r[5]=e[5]+n[5],r[6]=e[6]+n[6],r[7]=e[7]+n[7],r[8]=e[8]+n[8],r[9]=e[9]+n[9],r[10]=e[10]+n[10],r[11]=e[11]+n[11],r[12]=e[12]+n[12],r[13]=e[13]+n[13],r[14]=e[14]+n[14],r[15]=e[15]+n[15],r}function de(r,e,n){return r[0]=e[0]-n[0],r[1]=e[1]-n[1],r[2]=e[2]-n[2],r[3]=e[3]-n[3],r[4]=e[4]-n[4],r[5]=e[5]-n[5],r[6]=e[6]-n[6],r[7]=e[7]-n[7],r[8]=e[8]-n[8],r[9]=e[9]-n[9],r[10]=e[10]-n[10],r[11]=e[11]-n[11],r[12]=e[12]-n[12],r[13]=e[13]-n[13],r[14]=e[14]-n[14],r[15]=e[15]-n[15],r}function Ea(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r[4]=e[4]*n,r[5]=e[5]*n,r[6]=e[6]*n,r[7]=e[7]*n,r[8]=e[8]*n,r[9]=e[9]*n,r[10]=e[10]*n,r[11]=e[11]*n,r[12]=e[12]*n,r[13]=e[13]*n,r[14]=e[14]*n,r[15]=e[15]*n,r}function Ta(r,e,n,a){return r[0]=e[0]+n[0]*a,r[1]=e[1]+n[1]*a,r[2]=e[2]+n[2]*a,r[3]=e[3]+n[3]*a,r[4]=e[4]+n[4]*a,r[5]=e[5]+n[5]*a,r[6]=e[6]+n[6]*a,r[7]=e[7]+n[7]*a,r[8]=e[8]+n[8]*a,r[9]=e[9]+n[9]*a,r[10]=e[10]+n[10]*a,r[11]=e[11]+n[11]*a,r[12]=e[12]+n[12]*a,r[13]=e[13]+n[13]*a,r[14]=e[14]+n[14]*a,r[15]=e[15]+n[15]*a,r}function Pa(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]&&r[4]===e[4]&&r[5]===e[5]&&r[6]===e[6]&&r[7]===e[7]&&r[8]===e[8]&&r[9]===e[9]&&r[10]===e[10]&&r[11]===e[11]&&r[12]===e[12]&&r[13]===e[13]&&r[14]===e[14]&&r[15]===e[15]}function Va(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=r[4],h=r[5],o=r[6],c=r[7],l=r[8],f=r[9],p=r[10],v=r[11],m=r[12],d=r[13],y=r[14],u=r[15],x=e[0],M=e[1],b=e[2],_=e[3],g=e[4],N=e[5],q=e[6],w=e[7],F=e[8],L=e[9],T=e[10],V=e[11],z=e[12],Y=e[13],or=e[14],hr=e[15];return Math.abs(n-x)<=S*Math.max(1,Math.abs(n),Math.abs(x))&&Math.abs(a-M)<=S*Math.max(1,Math.abs(a),Math.abs(M))&&Math.abs(t-b)<=S*Math.max(1,Math.abs(t),Math.abs(b))&&Math.abs(s-_)<=S*Math.max(1,Math.abs(s),Math.abs(_))&&Math.abs(i-g)<=S*Math.max(1,Math.abs(i),Math.abs(g))&&Math.abs(h-N)<=S*Math.max(1,Math.abs(h),Math.abs(N))&&Math.abs(o-q)<=S*Math.max(1,Math.abs(o),Math.abs(q))&&Math.abs(c-w)<=S*Math.max(1,Math.abs(c),Math.abs(w))&&Math.abs(l-F)<=S*Math.max(1,Math.abs(l),Math.abs(F))&&Math.abs(f-L)<=S*Math.max(1,Math.abs(f),Math.abs(L))&&Math.abs(p-T)<=S*Math.max(1,Math.abs(p),Math.abs(T))&&Math.abs(v-V)<=S*Math.max(1,Math.abs(v),Math.abs(V))&&Math.abs(m-z)<=S*Math.max(1,Math.abs(m),Math.abs(z))&&Math.abs(d-Y)<=S*Math.max(1,Math.abs(d),Math.abs(Y))&&Math.abs(y-or)<=S*Math.max(1,Math.abs(y),Math.abs(or))&&Math.abs(u-hr)<=S*Math.max(1,Math.abs(u),Math.abs(hr))}var Oa=ce,ja=de;var pr={};ur(pr,{add:()=>ht,calculateW:()=>Xa,clone:()=>tt,conjugate:()=>et,copy:()=>it,create:()=>Nr,dot:()=>Pe,equals:()=>dt,exactEquals:()=>yt,exp:()=>Le,fromEuler:()=>nt,fromMat3:()=>Ee,fromValues:()=>st,getAngle:()=>$a,getAxisAngle:()=>Qa,identity:()=>Ga,invert:()=>rt,len:()=>pt,length:()=>Ve,lerp:()=>lt,ln:()=>De,mul:()=>ct,multiply:()=>Ce,normalize:()=>Fr,pow:()=>Ka,random:()=>Ja,rotateX:()=>Ha,rotateY:()=>Wa,rotateZ:()=>Za,rotationTo:()=>vt,scale:()=>Te,set:()=>ot,setAxes:()=>ut,setAxisAngle:()=>Ie,slerp:()=>mr,sqlerp:()=>mt,sqrLen:()=>ft,squaredLength:()=>Oe,str:()=>at});function zr(){var r=new P(3);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0),r}function Ra(r){var e=r[0],n=r[1],a=r[2];return Math.hypot(e,n,a)}function qr(r,e,n){var a=new P(3);return a[0]=r,a[1]=e,a[2]=n,a}function ve(r,e){var n=e[0],a=e[1],t=e[2],s=n*n+a*a+t*t;return s>0&&(s=1/Math.sqrt(s)),r[0]=e[0]*s,r[1]=e[1]*s,r[2]=e[2]*s,r}function me(r,e){return r[0]*e[0]+r[1]*e[1]+r[2]*e[2]}function vr(r,e,n){var a=e[0],t=e[1],s=e[2],i=n[0],h=n[1],o=n[2];return r[0]=t*o-s*h,r[1]=s*i-a*o,r[2]=a*h-t*i,r}var ue=Ra;var Zt=function(){var r=zr();return function(e,n,a,t,s,i){var h,o;for(n||(n=3),a||(a=0),t?o=Math.min(t*n+a,e.length):o=e.length,h=a;h<o;h+=n)r[0]=e[h],r[1]=e[h+1],r[2]=e[h+2],s(r,r,i),e[h]=r[0],e[h+1]=r[1],e[h+2]=r[2];return e}}();function Ua(){var r=new P(4);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0,r[3]=0),r}function xe(r){var e=new P(4);return e[0]=r[0],e[1]=r[1],e[2]=r[2],e[3]=r[3],e}function ge(r,e,n,a){var t=new P(4);return t[0]=r,t[1]=e,t[2]=n,t[3]=a,t}function Me(r,e){return r[0]=e[0],r[1]=e[1],r[2]=e[2],r[3]=e[3],r}function _e(r,e,n,a,t){return r[0]=e,r[1]=n,r[2]=a,r[3]=t,r}function be(r,e,n){return r[0]=e[0]+n[0],r[1]=e[1]+n[1],r[2]=e[2]+n[2],r[3]=e[3]+n[3],r}function Ae(r,e,n){return r[0]=e[0]*n,r[1]=e[1]*n,r[2]=e[2]*n,r[3]=e[3]*n,r}function we(r){var e=r[0],n=r[1],a=r[2],t=r[3];return Math.hypot(e,n,a,t)}function ke(r){var e=r[0],n=r[1],a=r[2],t=r[3];return e*e+n*n+a*a+t*t}function ze(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n*n+a*a+t*t+s*s;return i>0&&(i=1/Math.sqrt(i)),r[0]=n*i,r[1]=a*i,r[2]=t*i,r[3]=s*i,r}function qe(r,e){return r[0]*e[0]+r[1]*e[1]+r[2]*e[2]+r[3]*e[3]}function Ne(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3];return r[0]=t+a*(n[0]-t),r[1]=s+a*(n[1]-s),r[2]=i+a*(n[2]-i),r[3]=h+a*(n[3]-h),r}function Fe(r,e){return r[0]===e[0]&&r[1]===e[1]&&r[2]===e[2]&&r[3]===e[3]}function Se(r,e){var n=r[0],a=r[1],t=r[2],s=r[3],i=e[0],h=e[1],o=e[2],c=e[3];return Math.abs(n-i)<=S*Math.max(1,Math.abs(n),Math.abs(i))&&Math.abs(a-h)<=S*Math.max(1,Math.abs(a),Math.abs(h))&&Math.abs(t-o)<=S*Math.max(1,Math.abs(t),Math.abs(o))&&Math.abs(s-c)<=S*Math.max(1,Math.abs(s),Math.abs(c))}var Xt=function(){var r=Ua();return function(e,n,a,t,s,i){var h,o;for(n||(n=4),a||(a=0),t?o=Math.min(t*n+a,e.length):o=e.length,h=a;h<o;h+=n)r[0]=e[h],r[1]=e[h+1],r[2]=e[h+2],r[3]=e[h+3],s(r,r,i),e[h]=r[0],e[h+1]=r[1],e[h+2]=r[2],e[h+3]=r[3];return e}}();function Nr(){var r=new P(4);return P!=Float32Array&&(r[0]=0,r[1]=0,r[2]=0),r[3]=1,r}function Ga(r){return r[0]=0,r[1]=0,r[2]=0,r[3]=1,r}function Ie(r,e,n){n=n*.5;var a=Math.sin(n);return r[0]=a*e[0],r[1]=a*e[1],r[2]=a*e[2],r[3]=Math.cos(n),r}function Qa(r,e){var n=Math.acos(e[3])*2,a=Math.sin(n/2);return a>S?(r[0]=e[0]/a,r[1]=e[1]/a,r[2]=e[2]/a):(r[0]=1,r[1]=0,r[2]=0),n}function $a(r,e){var n=Pe(r,e);return Math.acos(2*n*n-1)}function Ce(r,e,n){var a=e[0],t=e[1],s=e[2],i=e[3],h=n[0],o=n[1],c=n[2],l=n[3];return r[0]=a*l+i*h+t*c-s*o,r[1]=t*l+i*o+s*h-a*c,r[2]=s*l+i*c+a*o-t*h,r[3]=i*l-a*h-t*o-s*c,r}function Ha(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o+i*h,r[1]=t*o+s*h,r[2]=s*o-t*h,r[3]=i*o-a*h,r}function Wa(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o-s*h,r[1]=t*o+i*h,r[2]=s*o+a*h,r[3]=i*o-t*h,r}function Za(r,e,n){n*=.5;var a=e[0],t=e[1],s=e[2],i=e[3],h=Math.sin(n),o=Math.cos(n);return r[0]=a*o+t*h,r[1]=t*o-a*h,r[2]=s*o+i*h,r[3]=i*o-s*h,r}function Xa(r,e){var n=e[0],a=e[1],t=e[2];return r[0]=n,r[1]=a,r[2]=t,r[3]=Math.sqrt(Math.abs(1-n*n-a*a-t*t)),r}function Le(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=Math.sqrt(n*n+a*a+t*t),h=Math.exp(s),o=i>0?h*Math.sin(i)/i:0;return r[0]=n*o,r[1]=a*o,r[2]=t*o,r[3]=h*Math.cos(i),r}function De(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=Math.sqrt(n*n+a*a+t*t),h=i>0?Math.atan2(i,s)/i:0;return r[0]=n*h,r[1]=a*h,r[2]=t*h,r[3]=.5*Math.log(n*n+a*a+t*t+s*s),r}function Ka(r,e,n){return De(r,e),Te(r,r,n),Le(r,r),r}function mr(r,e,n,a){var t=e[0],s=e[1],i=e[2],h=e[3],o=n[0],c=n[1],l=n[2],f=n[3],p,v,m,d,y;return v=t*o+s*c+i*l+h*f,v<0&&(v=-v,o=-o,c=-c,l=-l,f=-f),1-v>S?(p=Math.acos(v),m=Math.sin(p),d=Math.sin((1-a)*p)/m,y=Math.sin(a*p)/m):(d=1-a,y=a),r[0]=d*t+y*o,r[1]=d*s+y*c,r[2]=d*i+y*l,r[3]=d*h+y*f,r}function Ja(r){var e=ir(),n=ir(),a=ir(),t=Math.sqrt(1-e),s=Math.sqrt(e);return r[0]=t*Math.sin(2*Math.PI*n),r[1]=t*Math.cos(2*Math.PI*n),r[2]=s*Math.sin(2*Math.PI*a),r[3]=s*Math.cos(2*Math.PI*a),r}function rt(r,e){var n=e[0],a=e[1],t=e[2],s=e[3],i=n*n+a*a+t*t+s*s,h=i?1/i:0;return r[0]=-n*h,r[1]=-a*h,r[2]=-t*h,r[3]=s*h,r}function et(r,e){return r[0]=-e[0],r[1]=-e[1],r[2]=-e[2],r[3]=e[3],r}function Ee(r,e){var n=e[0]+e[4]+e[8],a;if(n>0)a=Math.sqrt(n+1),r[3]=.5*a,a=.5/a,r[0]=(e[5]-e[7])*a,r[1]=(e[6]-e[2])*a,r[2]=(e[1]-e[3])*a;else{var t=0;e[4]>e[0]&&(t=1),e[8]>e[t*3+t]&&(t=2);var s=(t+1)%3,i=(t+2)%3;a=Math.sqrt(e[t*3+t]-e[s*3+s]-e[i*3+i]+1),r[t]=.5*a,a=.5/a,r[3]=(e[s*3+i]-e[i*3+s])*a,r[s]=(e[s*3+t]+e[t*3+s])*a,r[i]=(e[i*3+t]+e[t*3+i])*a}return r}function nt(r,e,n,a){var t=.5*Math.PI/180;e*=t,n*=t,a*=t;var s=Math.sin(e),i=Math.cos(e),h=Math.sin(n),o=Math.cos(n),c=Math.sin(a),l=Math.cos(a);return r[0]=s*o*l-i*h*c,r[1]=i*h*l+s*o*c,r[2]=i*o*c-s*h*l,r[3]=i*o*l+s*h*c,r}function at(r){return\"quat(\"+r[0]+\", \"+r[1]+\", \"+r[2]+\", \"+r[3]+\")\"}var tt=xe,st=ge,it=Me,ot=_e,ht=be,ct=Ce,Te=Ae,Pe=qe,lt=Ne,Ve=we,pt=Ve,Oe=ke,ft=Oe,Fr=ze,yt=Fe,dt=Se,vt=function(){var r=zr(),e=qr(1,0,0),n=qr(0,1,0);return function(a,t,s){var i=me(t,s);return i<-.999999?(vr(r,e,t),ue(r)<1e-6&&vr(r,n,t),ve(r,r),Ie(a,r,Math.PI),a):i>.999999?(a[0]=0,a[1]=0,a[2]=0,a[3]=1,a):(vr(r,t,s),a[0]=r[0],a[1]=r[1],a[2]=r[2],a[3]=1+i,Fr(a,a))}}(),mt=function(){var r=Nr(),e=Nr();return function(n,a,t,s,i,h){return mr(r,a,i,h),mr(e,t,s,h),mr(n,r,e,2*h*(1-h)),n}}(),ut=function(){var r=kr();return function(e,n,a,t){return r[0]=a[0],r[3]=a[1],r[6]=a[2],r[1]=t[0],r[4]=t[1],r[7]=t[2],r[2]=-n[0],r[5]=-n[1],r[8]=-n[2],Fr(e,Ee(e,r))}}();var je={xyz:3,color:3,opacity:1,scaling:3,quaternion:4,harmonics:3},ar=class{constructor(e){this.version=\"\";this._buffer=e}get buffer(){return this._buffer}get decoded(){return this._decoded||(this._decoded=this.decodeBuffer()),this._decoded}get colorsA(){let e=.28209479177387814,n=this.decoded.color.denormDequant(),a=this.decoded.opacity.denormDequant(),t=(0,nr.default)(new Float32Array(n.shape[0]*4),[n.shape[0],4]);return R.mulseq(n,e),R.addseq(n,.5),R.mulseq(n,255),R.maxseq(n,0),R.minseq(n,255),this.version===\"\"&&(R.negeq(a),R.expeq(a),R.addseq(a,1),R.recipeq(a),R.mulseq(a,255)),R.assign(t.hi(n.shape[0],3).lo(0,0),n),R.assign(t.hi(n.shape[0],4).lo(0,3),a),(0,nr.default)(new Uint8Array(t.data),[n.shape[0],4]).data}get nsplats(){return this.decoded.nsplats}getSplatCount(){return this.decoded.nsplats}get precomputedCovarianceBufferData(){return this._precomputedCovarianceBufferData}decodeBuffer(){let{splatCount:e,chunkCount:n,chunkSize:a,typeChunks:t,vertexData:s,propertiesDesc:i,version:h}=this.decodeHeader();this.version=h;let o={xyz:i.xyz.compressionMethod,color:i.color.compressionMethod,opacity:i.opacity.compressionMethod,scaling:i.scaling.compressionMethod,quaternion:i.quaternion.compressionMethod,chunkSize:a};i.harmonics_0&&(o.harmonics=i.harmonics_0.compressionMethod);let c=s.byteOffset,l=Array(Object.keys(i).length);for(let x in i)l[i[x].index]={name:x,method:i[x].compressionMethod};let f=n*2*4,p=c,v=t===\"dynamic\"?n*2:0,m,d=!1;if(v>0){let x=new Uint16Array(s.buffer.slice(p,p+v));p+=v,m=Array.from(x),d=!0}let y={};for(let x of l){let M=0,b=!0;if(x.method===\"norm8x\")M=e*1*je[x.name];else if(x.method===\"norm11\")M=e*4;else if(x.method===\"norm565\")M=e*2;else throw b=!1,new Error(`Not Implemented format: ${x.method}`);let _;if(b){let q=s.buffer.slice(p,p+f);_=(0,nr.default)(new Float32Array(q),[n,2]),p+=f}else throw new Error(\"loading chunk byt hasnot minmax!\");let g=s.buffer.slice(p,p+M);p+=M;let N;if(x.method===\"norm8x\")N=(0,nr.default)(new Uint8Array(g),[e,je[x.name]]);else if(x.method===\"norm11\")N=(0,nr.default)(new Uint32Array(g));else if(x.method===\"norm565\")N=(0,nr.default)(new Uint16Array(g));else throw new Error(`Not Implemented format: ${x.method}`);y[x.name]=new E(N,_,a,x.method,m,d)}let u=[];for(let x=0;x<15;x++){let M=y[`harmonics_${x}`];M&&(u.push(M),delete y[`harmonics_${x}`])}return u.length>0&&(y.harmonics=u),new X(o,y.xyz,y.scaling,y.color,y.opacity,y.quaternion,y.harmonics,m)}buildPreComputedBuffers(){let a=this.decoded,t=a.nsplats,s=new ArrayBuffer(24*t),i=new Float32Array(s),h=a.scaling.denormDequant(),o=a.quaternion.denormDequant(),c=pr.create(),l=W.create(),f=W.create(),p=W.create(),v=lr.create();for(let m=0;m<t;m++){lr.fromScaling(v,[Math.exp(h.get(m,0)),Math.exp(h.get(m,1)),Math.exp(h.get(m,2))]),W.fromMat4(f,v),pr.set(c,o.get(m,0),o.get(m,1),o.get(m,2),o.get(m,3)),W.fromQuat(l,c),W.multiply(p,l,f);let d=p;i[6*m]=d[0]*d[0]+d[3]*d[3]+d[6]*d[6],i[6*m+1]=d[0]*d[1]+d[3]*d[4]+d[6]*d[7],i[6*m+2]=d[0]*d[2]+d[3]*d[5]+d[6]*d[8],i[6*m+3]=d[1]*d[1]+d[4]*d[4]+d[7]*d[7],i[6*m+4]=d[1]*d[2]+d[4]*d[5]+d[7]*d[8],i[6*m+5]=d[2]*d[2]+d[5]*d[5]+d[8]*d[8]}this._precomputedCovarianceBufferData=s}decodeHeader(){let e=this._buffer,n=new TextDecoder,a=0,t=\"\",s=100;for(;;){if(a+s>=e.byteLength)throw new Error(\"End of file reached while searching for end of header\");let y=new Uint8Array(e,a,s);t+=n.decode(y),a+=s;let u=a-s*2,x=new Uint8Array(e,Math.max(0,u),u>=0?s*2:s);if(n.decode(x).includes(\"end_header\"))break}let i=t.split(`\n`),h=0,o=0,c=0,l=0,f=\"\",p=\"\",v={};for(let y=0;y<i.length;y++){let u=i[y].trim();if(u.startsWith(\"version\"))p=u.split(\" \")[1]??\"\";else if(u.startsWith(\"element vertex\")){let x=u.match(/\\d+/);x&&(h=parseInt(x[0]))}else if(u.startsWith(\"property\")){let x=u.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);if(x){let M=x[2],b=x[3];v[M]={compressionMethod:b,index:l},l++}}else if(u.startsWith(\"element chunks\")){let x=u.match(/\\d+/);x&&(o=parseInt(x[0]))}else if(u.startsWith(\"element chunkSize\")){let x=u.match(/\\d+/);x&&(c=parseInt(x[0]))}else if(u.startsWith(\"element typeChunks\")){let x=u.match(/(\\w+)\\s+(\\w+)\\s+(\\w+)/);x&&(f=x[3])}else if(u===\"end_header\")break}let m=t.indexOf(\"end_header\")+10+1,d=new DataView(e,m);return{splatCount:h,chunkCount:o,chunkSize:c,typeChunks:f,vertexData:d,propertiesDesc:v,version:p}}pruneSplats(e){let a=this.decodeBuffer().pruneSplats(e);return ar.fromCompressedGaussianSplats(a,this.version)}static fromCompressedGaussianSplats(e,n){let a=e.xyz.length,t=e.xyz.nchunks,s=`gspline\nversion ${n}\nelement vertex ${a}\nelement chunks ${t}\nelement chunkSize ${e.chunkSize}\nelement typeChunks ${e.isDynamicChunks?\"dynamic\":\"static\"}\nproperty xyz ${e.xyz.method}\nproperty color ${e.color.method}\nproperty opacity ${e.opacity.method}\nproperty scaling ${e.scaling.method}\nproperty quaternion ${e.quaternion.method}`;if(e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++)s=`${s}\nproperty harmonics_${F} ${e.harmonics[F].method}`;s=`${s}\nend_header\n`;let h=new TextEncoder().encode(s),o=t*2*4,c=e.xyz.quantized.data.buffer.byteLength,l=e.xyz instanceof E?o:0,f=e.color.quantized.data.buffer.byteLength,p=e.color instanceof E?o:0,v=e.opacity.quantized.data.buffer.byteLength,m=e.opacity instanceof E?o:0,d=e.scaling.quantized.data.buffer.byteLength,y=e.scaling instanceof E?o:0,u=e.quaternion.quantized.data.buffer.byteLength,x=e.quaternion instanceof E?o:0,M=e.variableChunkSize?Uint16Array.from(e.variableChunkSize):void 0,b=M?M.byteLength:0,_=h.byteLength+b+c+l+f+p+v+m+d+y+u+x,g=0,N=0;if(e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++)g+=e.harmonics[F].quantized.data.buffer.byteLength,N+=e.harmonics[F]instanceof E?o:0;g=0,N=0,_+=g+N;let q=new Uint8Array(_),w=0;if(q.set(h,w),w+=h.byteLength,b>0&&(q.set(new Uint8Array(M.buffer),w),w+=b),e.xyz instanceof E&&(q.set(new Uint8Array(e.xyz.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.xyz.quantized.data.buffer),w),w+=c,e.color instanceof E&&(q.set(new Uint8Array(e.color.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.color.quantized.data.buffer),w),w+=f,e.opacity instanceof E&&(q.set(new Uint8Array(e.opacity.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.opacity.quantized.data.buffer),w),w+=v,e.scaling instanceof E&&(q.set(new Uint8Array(e.scaling.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.scaling.quantized.data.buffer),w),w+=d,e.quaternion instanceof E&&(q.set(new Uint8Array(e.quaternion.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(e.quaternion.quantized.data.buffer),w),w+=u,g>0&&e.harmonics&&e.harmonics.length>0)for(let F=0;F<e.harmonics.length;F++){let L=e.harmonics[F];L instanceof E&&(q.set(new Uint8Array(L.minmaxMatrix.data.buffer),w),w+=o),q.set(new Uint8Array(L.quantized.data.buffer),w),w+=L.quantized.data.byteLength}return new ar(q.buffer)}};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-browser)/./node_modules/@splinetool/runtime/build/gaussian-splat-compression.js\n"));

/***/ })

}]);