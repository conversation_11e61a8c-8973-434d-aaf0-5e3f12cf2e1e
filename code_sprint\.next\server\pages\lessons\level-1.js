/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "pages/lessons/level-1";
exports.ids = ["pages/lessons/level-1"];
exports.modules = {

/***/ "(pages-dir-node)/./data/level1Data.js":
/*!****************************!*\
  !*** ./data/level1Data.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _pages_lessons_level1_1_1WhatIsComputer__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../pages/lessons/level1/1.1WhatIsComputer */ \"(pages-dir-node)/./pages/lessons/level1/1.1WhatIsComputer.js\");\n/* harmony import */ var _pages_lessons_level1_1_2HowComputersWork__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../pages/lessons/level1/1.2HowComputersWork */ \"(pages-dir-node)/./pages/lessons/level1/1.2HowComputersWork.js\");\n/* harmony import */ var _pages_lessons_level1_1_3WhatIsAProgram__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../pages/lessons/level1/1.3WhatIsAProgram */ \"(pages-dir-node)/./pages/lessons/level1/1.3WhatIsAProgram.js\");\n/* harmony import */ var _pages_lessons_level1_1_4TypesOfComputers__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ../pages/lessons/level1/1.4TypesOfComputers */ \"(pages-dir-node)/./pages/lessons/level1/1.4TypesOfComputers.js\");\n/* harmony import */ var _pages_lessons_level1_1_5HardwareAndSoftware__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ../pages/lessons/level1/1.5HardwareAndSoftware */ \"(pages-dir-node)/./pages/lessons/level1/1.5HardwareAndSoftware.js\");\n/* harmony import */ var _pages_lessons_level1_1_6OperatingSystem__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! ../pages/lessons/level1/1.6OperatingSystem */ \"(pages-dir-node)/./pages/lessons/level1/1.6OperatingSystem.js\");\n/* harmony import */ var _pages_lessons_level1_2_1WhatIsScratch__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! ../pages/lessons/level1/2.1WhatIsScratch */ \"(pages-dir-node)/./pages/lessons/level1/2.1WhatIsScratch.js\");\n/* harmony import */ var _pages_lessons_level1_3_1WhatIsIoT__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! ../pages/lessons/level1/3.1WhatIsIoT */ \"(pages-dir-node)/./pages/lessons/level1/3.1WhatIsIoT.js\");\n/* harmony import */ var _pages_lessons_level1_3_2HowIoTWorks__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! ../pages/lessons/level1/3.2HowIoTWorks */ \"(pages-dir-node)/./pages/lessons/level1/3.2HowIoTWorks.js\");\n/* harmony import */ var _pages_lessons_level1_3_3IoTExamples__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! ../pages/lessons/level1/3.3IoTExamples */ \"(pages-dir-node)/./pages/lessons/level1/3.3IoTExamples.js\");\n/* harmony import */ var _pages_lessons_level1_3_4IoTBenefits__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! ../pages/lessons/level1/3.4IoTBenefits */ \"(pages-dir-node)/./pages/lessons/level1/3.4IoTBenefits.js\");\n/* harmony import */ var _pages_lessons_level1_4_1WhatIsComputerVision__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! ../pages/lessons/level1/4.1WhatIsComputerVision */ \"(pages-dir-node)/./pages/lessons/level1/4.1WhatIsComputerVision.js\");\n/* harmony import */ var _pages_lessons_level1_4_2HowComputerVisionWorks__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! ../pages/lessons/level1/4.2HowComputerVisionWorks */ \"(pages-dir-node)/./pages/lessons/level1/4.2HowComputerVisionWorks.js\");\n/* harmony import */ var _pages_lessons_level1_4_3ComputerVisionApplications__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! ../pages/lessons/level1/4.3ComputerVisionApplications */ \"(pages-dir-node)/./pages/lessons/level1/4.3ComputerVisionApplications.js\");\n/* harmony import */ var _pages_lessons_level1_5_1Summary__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! ../pages/lessons/level1/5.1Summary */ \"(pages-dir-node)/./pages/lessons/level1/5.1Summary.js\");\n/* harmony import */ var _pages_lessons_level1_5_3CompletionMessage__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! ../pages/lessons/level1/5.3CompletionMessage */ \"(pages-dir-node)/./pages/lessons/level1/5.3CompletionMessage.js\");\n\n\n\n\n\n\n\n\n\n// import NavigatingScratch from \"../pages/lessons/level1/2.2NavigatingScratch\";\n// import FirstScratchProject from \"../pages/lessons/level1/2.3FirstScratchProject\";\n\n\n\n\n\n\n\n\n\nconst level1Data = {\n    majorTopics: [\n        {\n            title: \"Introduction to Computers\",\n            minorTopics: [\n                {\n                    title: \"What is a Computer?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_1WhatIsComputer__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 26,\n                        columnNumber: 52\n                    }, undefined)\n                },\n                {\n                    title: \"How Computers Work\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_2HowComputersWork__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 27,\n                        columnNumber: 51\n                    }, undefined)\n                },\n                {\n                    title: \"What is a Computer Program?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_3WhatIsAProgram__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 28,\n                        columnNumber: 60\n                    }, undefined)\n                },\n                {\n                    title: \"Different Types of Computers\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_4TypesOfComputers__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 29,\n                        columnNumber: 61\n                    }, undefined)\n                },\n                {\n                    title: \"Hardware vs. Software\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_5HardwareAndSoftware__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 30,\n                        columnNumber: 54\n                    }, undefined)\n                },\n                {\n                    title: \"What is an Operating System?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_1_6OperatingSystem__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 31,\n                        columnNumber: 61\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Getting Started with Scratch\",\n            minorTopics: [\n                {\n                    title: \"What is Scratch?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_2_1WhatIsScratch__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 37,\n                        columnNumber: 49\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Introduction to IoT\",\n            minorTopics: [\n                {\n                    title: \"What is IoT?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_1WhatIsIoT__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 45,\n                        columnNumber: 45\n                    }, undefined)\n                },\n                {\n                    title: \"How IoT Works\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_2HowIoTWorks__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 46,\n                        columnNumber: 46\n                    }, undefined)\n                },\n                {\n                    title: \"Examples of IoT Devices\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_3IoTExamples__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 47,\n                        columnNumber: 56\n                    }, undefined)\n                },\n                {\n                    title: \"Benefits of IoT\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_3_4IoTBenefits__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 48,\n                        columnNumber: 48\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Introduction to Computer Vision\",\n            minorTopics: [\n                {\n                    title: \"What is Computer Vision?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_1WhatIsComputerVision__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 54,\n                        columnNumber: 57\n                    }, undefined)\n                },\n                {\n                    title: \"How Does Computer Vision Work?\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_2HowComputerVisionWorks__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 55,\n                        columnNumber: 63\n                    }, undefined)\n                },\n                {\n                    title: \"Applications of Computer Vision\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_4_3ComputerVisionApplications__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 56,\n                        columnNumber: 64\n                    }, undefined)\n                }\n            ]\n        },\n        {\n            title: \"Conclusion and Review\",\n            minorTopics: [\n                {\n                    title: \"Summary of Key Concepts\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_5_1Summary__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 62,\n                        columnNumber: 58\n                    }, undefined)\n                },\n                {\n                    title: \"Course Completion Message\",\n                    component: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_pages_lessons_level1_5_3CompletionMessage__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/data/level1Data.js\",\n                        lineNumber: 63,\n                        columnNumber: 60\n                    }, undefined)\n                }\n            ]\n        }\n    ]\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (level1Data);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./data/level1Data.js\n");

/***/ }),

/***/ "(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getServerSideProps: () => (/* binding */ getServerSideProps),\n/* harmony export */   getStaticPaths: () => (/* binding */ getStaticPaths),\n/* harmony export */   getStaticProps: () => (/* binding */ getStaticProps),\n/* harmony export */   reportWebVitals: () => (/* binding */ reportWebVitals),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   unstable_getServerProps: () => (/* binding */ unstable_getServerProps),\n/* harmony export */   unstable_getServerSideProps: () => (/* binding */ unstable_getServerSideProps),\n/* harmony export */   unstable_getStaticParams: () => (/* binding */ unstable_getStaticParams),\n/* harmony export */   unstable_getStaticPaths: () => (/* binding */ unstable_getStaticPaths),\n/* harmony export */   unstable_getStaticProps: () => (/* binding */ unstable_getStaticProps)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/pages/module.compiled */ \"(pages-dir-node)/./node_modules/next/dist/server/route-modules/pages/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(pages-dir-node)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/build/templates/helpers */ \"(pages-dir-node)/./node_modules/next/dist/build/templates/helpers.js\");\n/* harmony import */ var private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! private-next-pages/_document */ \"(pages-dir-node)/./pages/_document.js\");\n/* harmony import */ var private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! private-next-pages/_app */ \"(pages-dir-node)/./pages/_app.js\");\n/* harmony import */ var _pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./pages/lessons/level-1.js */ \"(pages-dir-node)/./pages/lessons/level-1.js\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__]);\n_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n\n// Import the app and document modules.\n\n\n// Import the userland code.\n\n// Re-export the component (should be the default export).\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'default'));\n// Re-export methods.\nconst getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticProps');\nconst getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getStaticPaths');\nconst getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'getServerSideProps');\nconst config = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'config');\nconst reportWebVitals = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'reportWebVitals');\n// Re-export legacy methods.\nconst unstable_getStaticProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticProps');\nconst unstable_getStaticPaths = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticPaths');\nconst unstable_getStaticParams = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getStaticParams');\nconst unstable_getServerProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerProps');\nconst unstable_getServerSideProps = (0,next_dist_build_templates_helpers__WEBPACK_IMPORTED_MODULE_2__.hoist)(_pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__, 'unstable_getServerSideProps');\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_route_modules_pages_module_compiled__WEBPACK_IMPORTED_MODULE_0__.PagesRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.PAGES,\n        page: \"/lessons/level-1\",\n        pathname: \"/lessons/level-1\",\n        // The following aren't used in production.\n        bundlePath: '',\n        filename: ''\n    },\n    components: {\n        // default export might not exist when optimized for data only\n        App: private_next_pages_app__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        Document: private_next_pages_document__WEBPACK_IMPORTED_MODULE_3__[\"default\"]\n    },\n    userland: _pages_lessons_level_1_js__WEBPACK_IMPORTED_MODULE_5__\n});\n\n//# sourceMappingURL=pages.js.map\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtcm91dGUtbG9hZGVyL2luZGV4LmpzP2tpbmQ9UEFHRVMmcGFnZT0lMkZsZXNzb25zJTJGbGV2ZWwtMSZwcmVmZXJyZWRSZWdpb249JmFic29sdXRlUGFnZVBhdGg9LiUyRnBhZ2VzJTJGbGVzc29ucyUyRmxldmVsLTEuanMmYWJzb2x1dGVBcHBQYXRoPXByaXZhdGUtbmV4dC1wYWdlcyUyRl9hcHAmYWJzb2x1dGVEb2N1bWVudFBhdGg9cHJpdmF0ZS1uZXh0LXBhZ2VzJTJGX2RvY3VtZW50Jm1pZGRsZXdhcmVDb25maWdCYXNlNjQ9ZTMwJTNEISIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQXdGO0FBQ2hDO0FBQ0U7QUFDMUQ7QUFDeUQ7QUFDVjtBQUMvQztBQUN1RDtBQUN2RDtBQUNBLGlFQUFlLHdFQUFLLENBQUMsc0RBQVEsWUFBWSxFQUFDO0FBQzFDO0FBQ08sdUJBQXVCLHdFQUFLLENBQUMsc0RBQVE7QUFDckMsdUJBQXVCLHdFQUFLLENBQUMsc0RBQVE7QUFDckMsMkJBQTJCLHdFQUFLLENBQUMsc0RBQVE7QUFDekMsZUFBZSx3RUFBSyxDQUFDLHNEQUFRO0FBQzdCLHdCQUF3Qix3RUFBSyxDQUFDLHNEQUFRO0FBQzdDO0FBQ08sZ0NBQWdDLHdFQUFLLENBQUMsc0RBQVE7QUFDOUMsZ0NBQWdDLHdFQUFLLENBQUMsc0RBQVE7QUFDOUMsaUNBQWlDLHdFQUFLLENBQUMsc0RBQVE7QUFDL0MsZ0NBQWdDLHdFQUFLLENBQUMsc0RBQVE7QUFDOUMsb0NBQW9DLHdFQUFLLENBQUMsc0RBQVE7QUFDekQ7QUFDTyx3QkFBd0Isa0dBQWdCO0FBQy9DO0FBQ0EsY0FBYyxrRUFBUztBQUN2QjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxhQUFhLDhEQUFXO0FBQ3hCLGtCQUFrQixtRUFBZ0I7QUFDbEMsS0FBSztBQUNMLFlBQVk7QUFDWixDQUFDOztBQUVELGlDIiwic291cmNlcyI6WyIiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgUGFnZXNSb3V0ZU1vZHVsZSB9IGZyb20gXCJuZXh0L2Rpc3Qvc2VydmVyL3JvdXRlLW1vZHVsZXMvcGFnZXMvbW9kdWxlLmNvbXBpbGVkXCI7XG5pbXBvcnQgeyBSb3V0ZUtpbmQgfSBmcm9tIFwibmV4dC9kaXN0L3NlcnZlci9yb3V0ZS1raW5kXCI7XG5pbXBvcnQgeyBob2lzdCB9IGZyb20gXCJuZXh0L2Rpc3QvYnVpbGQvdGVtcGxhdGVzL2hlbHBlcnNcIjtcbi8vIEltcG9ydCB0aGUgYXBwIGFuZCBkb2N1bWVudCBtb2R1bGVzLlxuaW1wb3J0ICogYXMgZG9jdW1lbnQgZnJvbSBcInByaXZhdGUtbmV4dC1wYWdlcy9fZG9jdW1lbnRcIjtcbmltcG9ydCAqIGFzIGFwcCBmcm9tIFwicHJpdmF0ZS1uZXh0LXBhZ2VzL19hcHBcIjtcbi8vIEltcG9ydCB0aGUgdXNlcmxhbmQgY29kZS5cbmltcG9ydCAqIGFzIHVzZXJsYW5kIGZyb20gXCIuL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtMS5qc1wiO1xuLy8gUmUtZXhwb3J0IHRoZSBjb21wb25lbnQgKHNob3VsZCBiZSB0aGUgZGVmYXVsdCBleHBvcnQpLlxuZXhwb3J0IGRlZmF1bHQgaG9pc3QodXNlcmxhbmQsICdkZWZhdWx0Jyk7XG4vLyBSZS1leHBvcnQgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAnZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCBnZXRTZXJ2ZXJTaWRlUHJvcHMgPSBob2lzdCh1c2VybGFuZCwgJ2dldFNlcnZlclNpZGVQcm9wcycpO1xuZXhwb3J0IGNvbnN0IGNvbmZpZyA9IGhvaXN0KHVzZXJsYW5kLCAnY29uZmlnJyk7XG5leHBvcnQgY29uc3QgcmVwb3J0V2ViVml0YWxzID0gaG9pc3QodXNlcmxhbmQsICdyZXBvcnRXZWJWaXRhbHMnKTtcbi8vIFJlLWV4cG9ydCBsZWdhY3kgbWV0aG9kcy5cbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUHJvcHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXRocyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U3RhdGljUGF0aHMnKTtcbmV4cG9ydCBjb25zdCB1bnN0YWJsZV9nZXRTdGF0aWNQYXJhbXMgPSBob2lzdCh1c2VybGFuZCwgJ3Vuc3RhYmxlX2dldFN0YXRpY1BhcmFtcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclByb3BzID0gaG9pc3QodXNlcmxhbmQsICd1bnN0YWJsZV9nZXRTZXJ2ZXJQcm9wcycpO1xuZXhwb3J0IGNvbnN0IHVuc3RhYmxlX2dldFNlcnZlclNpZGVQcm9wcyA9IGhvaXN0KHVzZXJsYW5kLCAndW5zdGFibGVfZ2V0U2VydmVyU2lkZVByb3BzJyk7XG4vLyBDcmVhdGUgYW5kIGV4cG9ydCB0aGUgcm91dGUgbW9kdWxlIHRoYXQgd2lsbCBiZSBjb25zdW1lZC5cbmV4cG9ydCBjb25zdCByb3V0ZU1vZHVsZSA9IG5ldyBQYWdlc1JvdXRlTW9kdWxlKHtcbiAgICBkZWZpbml0aW9uOiB7XG4gICAgICAgIGtpbmQ6IFJvdXRlS2luZC5QQUdFUyxcbiAgICAgICAgcGFnZTogXCIvbGVzc29ucy9sZXZlbC0xXCIsXG4gICAgICAgIHBhdGhuYW1lOiBcIi9sZXNzb25zL2xldmVsLTFcIixcbiAgICAgICAgLy8gVGhlIGZvbGxvd2luZyBhcmVuJ3QgdXNlZCBpbiBwcm9kdWN0aW9uLlxuICAgICAgICBidW5kbGVQYXRoOiAnJyxcbiAgICAgICAgZmlsZW5hbWU6ICcnXG4gICAgfSxcbiAgICBjb21wb25lbnRzOiB7XG4gICAgICAgIC8vIGRlZmF1bHQgZXhwb3J0IG1pZ2h0IG5vdCBleGlzdCB3aGVuIG9wdGltaXplZCBmb3IgZGF0YSBvbmx5XG4gICAgICAgIEFwcDogYXBwLmRlZmF1bHQsXG4gICAgICAgIERvY3VtZW50OiBkb2N1bWVudC5kZWZhdWx0XG4gICAgfSxcbiAgICB1c2VybGFuZFxufSk7XG5cbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXBhZ2VzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_app.js":
/*!***********************!*\
  !*** ./pages/_app.js ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ App)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/styles/globals.css */ \"(pages-dir-node)/./styles/globals.css\");\n/* harmony import */ var _styles_globals_css__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(_styles_globals_css__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./lessons/level-4/Section1/animations.module.css */ \"(pages-dir-node)/./pages/lessons/level-4/Section1/animations.module.css\");\n/* harmony import */ var _lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_lessons_level_4_Section1_animations_module_css__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! prop-types */ \"prop-types\");\n/* harmony import */ var prop_types__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(prop_types__WEBPACK_IMPORTED_MODULE_3__);\n// pages/_app.js\n\n // Global styles\n // Animations CSS\n\nfunction App({ Component, pageProps }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n        ...pageProps\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_app.js\",\n        lineNumber: 7,\n        columnNumber: 10\n    }, this);\n}\n// Add PropTypes for App\nApp.propTypes = {\n    Component: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().elementType).isRequired,\n    pageProps: (prop_types__WEBPACK_IMPORTED_MODULE_3___default().object).isRequired\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19hcHAuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7O0FBQUEsZ0JBQWdCOztBQUNjLENBQUMsZ0JBQWdCO0FBQ1csQ0FBQyxpQkFBaUI7QUFDekM7QUFFcEIsU0FBU0MsSUFBSSxFQUFFQyxTQUFTLEVBQUVDLFNBQVMsRUFBRTtJQUNsRCxxQkFBTyw4REFBQ0Q7UUFBVyxHQUFHQyxTQUFTOzs7Ozs7QUFDakM7QUFFQSx3QkFBd0I7QUFDeEJGLElBQUlHLFNBQVMsR0FBRztJQUNkRixXQUFXRiwrREFBcUIsQ0FBQ00sVUFBVTtJQUMzQ0gsV0FBV0gsMERBQWdCLENBQUNNLFVBQVU7QUFDeEMiLCJzb3VyY2VzIjpbIi9tbnQvZC9BYW1pci9jb2Rlc3ByaW50L2NvZGVfc3ByaW50L3BhZ2VzL19hcHAuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gcGFnZXMvX2FwcC5qc1xuaW1wb3J0IFwiQC9zdHlsZXMvZ2xvYmFscy5jc3NcIjsgLy8gR2xvYmFsIHN0eWxlc1xuaW1wb3J0IFwiLi9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzXCI7IC8vIEFuaW1hdGlvbnMgQ1NTXG5pbXBvcnQgUHJvcFR5cGVzIGZyb20gXCJwcm9wLXR5cGVzXCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEFwcCh7IENvbXBvbmVudCwgcGFnZVByb3BzIH0pIHtcbiAgcmV0dXJuIDxDb21wb25lbnQgey4uLnBhZ2VQcm9wc30gLz47XG59XG5cbi8vIEFkZCBQcm9wVHlwZXMgZm9yIEFwcFxuQXBwLnByb3BUeXBlcyA9IHtcbiAgQ29tcG9uZW50OiBQcm9wVHlwZXMuZWxlbWVudFR5cGUuaXNSZXF1aXJlZCwgLy8gQ29tcG9uZW50IG11c3QgYmUgYSB2YWxpZCBSZWFjdCBjb21wb25lbnRcbiAgcGFnZVByb3BzOiBQcm9wVHlwZXMub2JqZWN0LmlzUmVxdWlyZWQsIC8vIHBhZ2VQcm9wcyBpcyBhbiBvYmplY3Rcbn07XG4iXSwibmFtZXMiOlsiUHJvcFR5cGVzIiwiQXBwIiwiQ29tcG9uZW50IiwicGFnZVByb3BzIiwicHJvcFR5cGVzIiwiZWxlbWVudFR5cGUiLCJpc1JlcXVpcmVkIiwib2JqZWN0Il0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_app.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/_document.js":
/*!****************************!*\
  !*** ./pages/_document.js ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Document)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/document */ \"(pages-dir-node)/./node_modules/next/document.js\");\n/* harmony import */ var next_document__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_document__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction Document() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Html, {\n        lang: \"en\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Head, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_document.js\",\n                lineNumber: 6,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.Main, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_document.js\",\n                        lineNumber: 8,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_document__WEBPACK_IMPORTED_MODULE_1__.NextScript, {}, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_document.js\",\n                        lineNumber: 9,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_document.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/_document.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL19kb2N1bWVudC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBNkQ7QUFFOUMsU0FBU0k7SUFDdEIscUJBQ0UsOERBQUNKLCtDQUFJQTtRQUFDSyxNQUFLOzswQkFDVCw4REFBQ0osK0NBQUlBOzs7OzswQkFDTCw4REFBQ0s7O2tDQUNDLDhEQUFDSiwrQ0FBSUE7Ozs7O2tDQUNMLDhEQUFDQyxxREFBVUE7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBSW5CIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9wYWdlcy9fZG9jdW1lbnQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgSHRtbCwgSGVhZCwgTWFpbiwgTmV4dFNjcmlwdCB9IGZyb20gXCJuZXh0L2RvY3VtZW50XCI7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIERvY3VtZW50KCkge1xuICByZXR1cm4gKFxuICAgIDxIdG1sIGxhbmc9XCJlblwiPlxuICAgICAgPEhlYWQgLz5cbiAgICAgIDxib2R5PlxuICAgICAgICA8TWFpbiAvPlxuICAgICAgICA8TmV4dFNjcmlwdCAvPlxuICAgICAgPC9ib2R5PlxuICAgIDwvSHRtbD5cbiAgKTtcbn1cbiJdLCJuYW1lcyI6WyJIdG1sIiwiSGVhZCIsIk1haW4iLCJOZXh0U2NyaXB0IiwiRG9jdW1lbnQiLCJsYW5nIiwiYm9keSJdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/_document.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level-1.js":
/*!**********************************!*\
  !*** ./pages/lessons/level-1.js ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.a(module, async (__webpack_handle_async_dependencies__, __webpack_async_result__) => { try {\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Level1Lessons)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _data_level1Data__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../data/level1Data */ \"(pages-dir-node)/./data/level1Data.js\");\n/* harmony import */ var _splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @splinetool/react-spline */ \"@splinetool/react-spline\");\nvar __webpack_async_dependencies__ = __webpack_handle_async_dependencies__([_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__]);\n_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__ = (__webpack_async_dependencies__.then ? (await __webpack_async_dependencies__)() : __webpack_async_dependencies__)[0];\n\n\n // Adjust the path if needed\n // Import the Spline component\nfunction Level1Lessons() {\n    const [currentMajorTopic, setCurrentMajorTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [currentMinorTopic, setCurrentMinorTopic] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const handleMajorTopicClick = (majorIndex)=>{\n        setCurrentMajorTopic(majorIndex);\n        setCurrentMinorTopic(0); // Automatically select the first sub-topic\n    };\n    const handleBackToMajorTopics = ()=>{\n        setCurrentMajorTopic(null);\n        setCurrentMinorTopic(null);\n    };\n    const currentTopic = currentMajorTopic !== null && currentMinorTopic !== null && _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics[currentMajorTopic]?.minorTopics[currentMinorTopic];\n    const CurrentLessonComponent = currentTopic?.component;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"relative flex min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-1/4 bg-purple-700 text-white p-6 flex flex-col justify-between\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-4xl font-extrabold mb-8 text-center drop-shadow-lg\",\n                                children: \"Course Progress\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 30,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                className: \"space-y-4\",\n                                children: currentMajorTopic === null ? _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics.map((major, majorIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        onClick: ()=>handleMajorTopicClick(majorIndex),\n                                        className: \"text-lg font-semibold cursor-pointer hover:text-yellow-300 transition-all\",\n                                        children: major.title\n                                    }, majorIndex, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                        lineNumber: 37,\n                                        columnNumber: 19\n                                    }, this)) : _data_level1Data__WEBPACK_IMPORTED_MODULE_2__[\"default\"].majorTopics[currentMajorTopic]?.minorTopics.map((minor, minorIndex)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        onClick: ()=>setCurrentMinorTopic(minorIndex),\n                                        className: `text-lg font-semibold cursor-pointer ${currentMinorTopic === minorIndex ? \"text-yellow-300\" : \"hover:text-yellow-300\"} transition-all`,\n                                        children: minor.title\n                                    }, minorIndex, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                        lineNumber: 47,\n                                        columnNumber: 21\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 33,\n                                columnNumber: 11\n                            }, this),\n                            currentMajorTopic !== null && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleBackToMajorTopics,\n                                className: \"mt-6 bg-yellow-500 text-black py-2 px-4 rounded-lg hover:bg-yellow-400 transition-all shadow-md\",\n                                children: \"Back to Topics\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 62,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                        lineNumber: 29,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>alert(\"Back to Dashboard\"),\n                        className: \"mt-6 bg-green-500 text-white py-3 px-6 rounded-lg shadow-lg hover:bg-green-600 transition-all duration-300\",\n                        children: \"Back to Dashboard\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                        lineNumber: 72,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                lineNumber: 28,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 relative bg-gradient-to-b from-blue-400 via-white to-green-600 flex flex-col items-center justify-center p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute inset-0 z-0\",\n                        style: {\n                            width: \"100%\",\n                            height: \"100%\",\n                            pointerEvents: \"none\"\n                        },\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_splinetool_react_spline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                            scene: \"https://prod.spline.design/gUAXNK7hcAGZebnj/scene.splinecode\",\n                            style: {\n                                width: \"100%\",\n                                height: \"100%\"\n                            }\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                            lineNumber: 91,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                        lineNumber: 83,\n                        columnNumber: 9\n                    }, this),\n                    currentMajorTopic === null && currentMinorTopic === null ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-6xl font-extrabold text-purple-700 mb-6 animate-bounce\",\n                                children: \"Welcome Young Developer!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 103,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-3xl text-gray-700 mt-4 mb-6 leading-relaxed\",\n                                children: [\n                                    \"Get ready to embark on an exciting learning journey! \",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                        lineNumber: 107,\n                                        columnNumber: 68\n                                    }, this),\n                                    \"Select a topic from the sidebar and dive into interactive lessons and activities.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 106,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleMajorTopicClick(0),\n                                className: \"mt-8 bg-purple-500 text-white py-3 px-8 rounded-full font-bold shadow-md hover:bg-purple-600 transition-all\",\n                                children: \"Let's Get Started!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                        lineNumber: 102,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative z-10 p-6 w-full\",\n                        children: CurrentLessonComponent || /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-lg text-gray-700\",\n                            children: \"No lesson found for the selected topic.\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                            lineNumber: 121,\n                            columnNumber: 15\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                        lineNumber: 119,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level-1.js\",\n        lineNumber: 26,\n        columnNumber: 5\n    }, this);\n}\n\n__webpack_async_result__();\n} catch(e) { __webpack_async_result__(e); } });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level-1.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level-4/Section1/animations.module.css":
/*!**************************************************************!*\
  !*** ./pages/lessons/level-4/Section1/animations.module.css ***!
  \**************************************************************/
/***/ ((module) => {

eval("// Exports\nmodule.exports = {\n\t\"confetti\": \"animations_confetti__r0_S4\",\n\t\"fadeInUp\": \"animations_fadeInUp__zzoxp\",\n\t\"hidden\": \"animations_hidden__w51ie\"\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHBhZ2VzLWRpci1ub2RlKS8uL3BhZ2VzL2xlc3NvbnMvbGV2ZWwtNC9TZWN0aW9uMS9hbmltYXRpb25zLm1vZHVsZS5jc3MiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvbW50L2QvQWFtaXIvY29kZXNwcmludC9jb2RlX3NwcmludC9wYWdlcy9sZXNzb25zL2xldmVsLTQvU2VjdGlvbjEvYW5pbWF0aW9ucy5tb2R1bGUuY3NzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEV4cG9ydHNcbm1vZHVsZS5leHBvcnRzID0ge1xuXHRcImNvbmZldHRpXCI6IFwiYW5pbWF0aW9uc19jb25mZXR0aV9fcjBfUzRcIixcblx0XCJmYWRlSW5VcFwiOiBcImFuaW1hdGlvbnNfZmFkZUluVXBfX3p6b3hwXCIsXG5cdFwiaGlkZGVuXCI6IFwiYW5pbWF0aW9uc19oaWRkZW5fX3c1MWllXCJcbn07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level-4/Section1/animations.module.css\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.1WhatIsComputer.js":
/*!***************************************************!*\
  !*** ./pages/lessons/level1/1.1WhatIsComputer.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n // Import Image from next/image\n\nconst WhatIsComputer = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \" bg-white text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"bg-white-100 border-l-4 border-blue-500 p-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-bold mb-4 text-purple-700\",\n                    children: \"What is a Computer?\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 8,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-4\",\n                    children: [\n                        \"A computer is a \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"font-bold text-blue-500\",\n                            children: \"smart machine\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 12,\n                            columnNumber: 25\n                        }, undefined),\n                        \" that helps us do many things like play games, draw pictures, learn new things, and talk to our friends. It can follow instructions to do all these tasks very quickly.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 11,\n                    columnNumber: 7\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Microchip on a Fingertip:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Did you know? Computers that used to take up an entire room now fit comfortably on your finger! A microchip is a tiny part inside a computer that helps it do all the amazing things it can do.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-4 flex justify-center items-center\",\n                    children: [\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                            src: \"/lvl1_img/Untitled.png\" // Corrected path\n                            ,\n                            alt: \"A computer illustration\",\n                            width: 500,\n                            height: 300,\n                            className: \"rounded-lg\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n                    lineNumber: 24,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n            lineNumber: 7,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.1WhatIsComputer.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (WhatIsComputer);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.1WhatIsComputer.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.2HowComputersWork.js":
/*!*****************************************************!*\
  !*** ./pages/lessons/level1/1.2HowComputersWork.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n\n // Import Next.js Image component\n\nconst HowComputersWork = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({\n        Input: \"\",\n        Storage: \"\",\n        Processing: \"\",\n        Output: \"\"\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)({});\n    const [isSubmitted, setIsSubmitted] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const options = [\n        \"Processor\",\n        \"Keyboard\",\n        \"Monitor\",\n        \"Hard Drive\",\n        \"Meow🐱\"\n    ];\n    const correctAnswers = {\n        Input: \"Keyboard\",\n        Storage: \"Hard Drive\",\n        Processing: \"Processor\",\n        Output: \"Monitor\"\n    };\n    const handleMatch = (part, value)=>{\n        setMatches((prevMatches)=>({\n                ...prevMatches,\n                [part]: value\n            }));\n    };\n    const checkAnswers = ()=>{\n        const newFeedback = {};\n        let allCorrect = true;\n        for (const [part, correct] of Object.entries(correctAnswers)){\n            if (matches[part] === correct) {\n                newFeedback[part] = \"🌟 Correct!\";\n            } else {\n                newFeedback[part] = \"🚫 Try again!\";\n                allCorrect = false;\n            }\n        }\n        setFeedback(newFeedback);\n        setIsSubmitted(true);\n        if (allCorrect) {\n            alert(\"🎉 Great job! You matched all the parts correctly!\");\n        }\n    };\n    const resetActivity = ()=>{\n        setMatches({\n            Input: \"\",\n            Storage: \"\",\n            Processing: \"\",\n            Output: \"\"\n        });\n        setFeedback({});\n        setIsSubmitted(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-200 via-purple-50 to-pink-100 p-8 min-h-screen rounded-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce\",\n                children: \"How Computers Work\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 64,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-8 flex justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_1___default()), {\n                    src: \"/lvl1_img/a-clean-and-modern-educational-illustrat_5raWjKvJSMK6ZvBRud3Q7w_FWwn2-2QTKurLKSC523P9g.jpeg\",\n                    alt: \"Fun computer illustration\",\n                    width: 400,\n                    height: 250,\n                    className: \"rounded-lg shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                    lineNumber: 69,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mb-6 text-center\",\n                children: [\n                    \"A computer combines \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"input\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 78,\n                        columnNumber: 29\n                    }, undefined),\n                    \", \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"storage\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 78,\n                        columnNumber: 53\n                    }, undefined),\n                    \",\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"processing\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined),\n                    \", and \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"output\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 79,\n                        columnNumber: 42\n                    }, undefined),\n                    \" to function.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 77,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-8 mb-8 text-lg text-purple-900\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Input:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Devices like a keyboard, mouse, or microphone give the computer information.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Memory/Storage:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 87,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Computers store your files on a hard drive or memory card.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 86,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Processing:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 91,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The processor, a microchip, processes the data with help from a cooling fan.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 90,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Output:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 95,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Results are shown using devices like screens, speakers, or printers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 94,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-50 p-6 rounded-xl shadow-lg border-l-4 border-blue-400\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-center font-semibold text-blue-800 mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Match the computer parts with their functions by selecting the correct option below!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-2 gap-6\",\n                        children: [\n                            \"Input\",\n                            \"Storage\",\n                            \"Processing\",\n                            \"Output\"\n                        ].map((part, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"bg-white p-4 rounded-xl shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-xl font-semibold mb-4 text-purple-600\",\n                                        children: [\n                                            index + 1,\n                                            \". \",\n                                            part\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 108,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        className: \"p-3 border border-gray-300 rounded-xl bg-white shadow-inner w-full\",\n                                        value: matches[part],\n                                        onChange: (e)=>handleMatch(part, e.target.value),\n                                        disabled: isSubmitted,\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Select an option\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                lineNumber: 117,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            options.map((option)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: option,\n                                                    children: option\n                                                }, option, false, {\n                                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 19\n                                                }, undefined))\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 111,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    isSubmitted && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: `mt-4 text-center font-bold ${feedback[part] === \"🌟 Correct!\" ? \"text-green-600\" : \"text-red-600\"}`,\n                                        children: feedback[part]\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                        lineNumber: 125,\n                                        columnNumber: 17\n                                    }, undefined)\n                                ]\n                            }, part, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                                lineNumber: 107,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-6 flex justify-center gap-6\",\n                        children: !isSubmitted ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-xl shadow-md hover:bg-purple-700 transition-all\",\n                            onClick: checkAnswers,\n                            children: \"Check Answers\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 141,\n                            columnNumber: 13\n                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            className: \"px-6 py-3 bg-yellow-500 text-white font-bold rounded-xl shadow-md hover:bg-yellow-600 transition-all\",\n                            onClick: resetActivity,\n                            children: \"Retry\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                            lineNumber: 148,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                        lineNumber: 139,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.2HowComputersWork.js\",\n        lineNumber: 63,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowComputersWork);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.2HowComputersWork.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.3WhatIsAProgram.js":
/*!***************************************************!*\
  !*** ./pages/lessons/level1/1.3WhatIsAProgram.js ***!
  \***************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst WhatIsAProgram = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-100 via-purple-50 to-green-100 p-6 rounded-lg shadow-md\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center animate-bounce\",\n                children: \"What is a Computer Program?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 7,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mb-4 text-center\",\n                children: [\n                    \"A \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"computer program\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 11,\n                        columnNumber: 11\n                    }, undefined),\n                    \" is like a recipe! It's a set of instructions that tells the computer how to do cool things like playing a game, drawing pictures, or solving problems.\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 10,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (3).png\",\n                    alt: \"A fun computer illustration\",\n                    width: 400,\n                    height: 250,\n                    className: \"rounded-lg shadow-lg\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 16,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 15,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mt-6 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 26,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Long ago, people had to write programs to perform even simple tasks! Now, we have apps like\",\n                        \" \",\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"em\", {\n                            children: \"Microsoft Word\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        \" and games that make things easier and more fun!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl mt-6 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Programs vs. Calculators:\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 33,\n                        columnNumber: 9\n                    }, undefined),\n                    \" Programs are like smart helpers. Calculators only do math, but programs can play music, draw, and even play chess!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 p-4 border-l-4 border-green-500 mt-6 rounded\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Activity:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                            lineNumber: 39,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Think about the programs or apps you use daily. What do they do for you?\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                    lineNumber: 38,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 6,\n        columnNumber: 5\n    }, undefined);\n};\nconst DragDropActivity = ()=>{\n    const items = [\n        {\n            id: 1,\n            name: \"Microsoft Word\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 2,\n            name: \"Calculator\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 3,\n            name: \"Notebook (Paper)\",\n            correctCategory: \"Not Programs\"\n        },\n        {\n            id: 4,\n            name: \"Chess App\",\n            correctCategory: \"Programs\"\n        },\n        {\n            id: 5,\n            name: \"Pen\",\n            correctCategory: \"Not Programs\"\n        }\n    ];\n    const [droppedItems, setDroppedItems] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        Programs: [],\n        \"Not Programs\": []\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const handleDrop = (category, item)=>{\n        setDroppedItems((prev)=>{\n            if (prev[category].some((i)=>i.id === item.id)) return prev;\n            return {\n                ...prev,\n                [category]: [\n                    ...prev[category],\n                    item\n                ]\n            };\n        });\n    };\n    const checkAnswers = ()=>{\n        let allCorrect = true;\n        for (const [category, itemsInCategory] of Object.entries(droppedItems)){\n            for (const item of itemsInCategory){\n                if (item.correctCategory !== category) {\n                    allCorrect = false;\n                }\n            }\n        }\n        setFeedback(allCorrect ? \"🎉 Great job! All answers are correct!\" : \"❌ Some answers are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setDroppedItems({\n            Programs: [],\n            \"Not Programs\": []\n        });\n        setFeedback(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-6 bg-gradient-to-b from-yellow-100 via-white to-blue-100 rounded-lg shadow-md mt-8\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-4xl font-bold text-purple-700 text-center mb-6\",\n                children: \"Fun Activity: Programs or Not?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 99,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6 text-black\",\n                children: [\n                    \"Drag and drop the items into the correct category:\",\n                    \" \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Programs\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 104,\n                        columnNumber: 9\n                    }, undefined),\n                    \" or \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Not Programs\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 104,\n                        columnNumber: 38\n                    }, undefined),\n                    \".\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-wrap justify-center text-black\",\n                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        draggable: true,\n                        onDragStart: (e)=>e.dataTransfer.setData(\"item\", JSON.stringify(item)),\n                        className: \"p-3 bg-white border border-gray-300 rounded-lg shadow-md cursor-pointer hover:shadow-lg transition-all\",\n                        children: item.name\n                    }, item.id, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 108,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 grid grid-cols-2 gap-6\",\n                children: [\n                    \"Programs\",\n                    \"Not Programs\"\n                ].map((category)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onDragOver: (e)=>e.preventDefault(),\n                        onDrop: (e)=>{\n                            const data = e.dataTransfer.getData(\"item\");\n                            const item = JSON.parse(data);\n                            handleDrop(category, item);\n                        },\n                        className: \"p-6 bg-blue-50 border border-blue-300 rounded-lg min-h-[150px] shadow-inner flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"text-2xl font-bold text-purple-600 mb-4\",\n                                children: category\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                lineNumber: 136,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                children: droppedItems[category].map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                        className: \"text-lg text-gray-700\",\n                                        children: item.name\n                                    }, item.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                        lineNumber: 141,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                                lineNumber: 139,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, category, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 126,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 124,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 flex justify-center gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all\",\n                        onClick: checkAnswers,\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 152,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        className: \"px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all\",\n                        onClick: resetActivity,\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 151,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"mt-6 text-lg font-semibold text-center text-black\",\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 166,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 98,\n        columnNumber: 5\n    }, undefined);\n};\nconst App = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"p-8 bg-gradient-to-b from-blue-100 via-white to-green-100\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatIsAProgram, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 175,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(DragDropActivity, {}, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.3WhatIsAProgram.js\",\n        lineNumber: 174,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (App);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.3WhatIsAProgram.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.4TypesOfComputers.js":
/*!*****************************************************!*\
  !*** ./pages/lessons/level1/1.4TypesOfComputers.js ***!
  \*****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst TypesOfComputers = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [isDragging, setIsDragging] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const computerTypes = [\n        {\n            id: 1,\n            name: \"Desktop Computers\",\n            image: \"/lvl1_img/desktop.webp\"\n        },\n        {\n            id: 2,\n            name: \"Laptop Computers\",\n            image: \"/lvl1_img/laptop.webp\"\n        },\n        {\n            id: 3,\n            name: \"Tablet Computers\",\n            image: \"/lvl1_img/tablet.webp\"\n        },\n        {\n            id: 4,\n            name: \"Smartphones\",\n            image: \"/lvl1_img/phone.webp\"\n        }\n    ];\n    const purposes = [\n        {\n            id: \"a\",\n            label: \"Large-screen productivity\"\n        },\n        {\n            id: \"b\",\n            label: \"Portable computing\"\n        },\n        {\n            id: \"c\",\n            label: \"Mobile communication and gaming\"\n        },\n        {\n            id: \"d\",\n            label: \"Touchscreen convenience\"\n        }\n    ];\n    const correctMatches = {\n        a: 1,\n        b: 2,\n        c: 4,\n        d: 3\n    };\n    const handleDragStart = (id)=>{\n        setIsDragging(id);\n    };\n    const handleDrop = (purposeId)=>{\n        if (isDragging) {\n            setMatches((prev)=>({\n                    ...prev,\n                    [purposeId]: isDragging\n                }));\n            setIsDragging(null);\n        }\n    };\n    const checkAnswers = ()=>{\n        const results = Object.keys(correctMatches).map((purposeId)=>({\n                purpose: purposes.find((purpose)=>purpose.id === purposeId).label,\n                isCorrect: correctMatches[purposeId] === matches[purposeId]\n            }));\n        const allCorrect = results.every((result)=>result.isCorrect);\n        setFeedback({\n            message: allCorrect ? \"🎉 Great job! All matches are correct!\" : \"❌ Some matches are incorrect. Try again!\",\n            results,\n            allCorrect\n        });\n    };\n    const retry = ()=>{\n        setMatches({});\n        setFeedback(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-100 p-6 rounded-lg shadow-lg\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Different Types of Computers\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6\",\n                children: \"Computers come in many shapes and sizes, each designed for specific tasks. Let’s learn about them and match them to their purposes!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-4 flex justify-center items-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (4).png\",\n                    alt: \"A fun computer illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                    lineNumber: 73,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 72,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-10 grid grid-cols-2 gap-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4 text-center\",\n                                children: \"Computer Types\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 flex flex-col items-center\",\n                                children: computerTypes.map((type)=>!Object.values(matches).includes(type.id) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(type.id),\n                                        className: \"flex flex-col items-center p-4 border border-gray-300 bg-white rounded-md shadow-md hover:shadow-lg cursor-pointer\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: type.image,\n                                                alt: type.name,\n                                                className: \"w-32 h-32 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 21\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-center font-semibold\",\n                                                children: type.name\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 104,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        ]\n                                    }, type.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                        lineNumber: 93,\n                                        columnNumber: 19\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4 text-center\",\n                                children: \"Computer Purposes\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 113,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6 flex flex-col items-center\",\n                                children: purposes.map((purpose)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(purpose.id),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"flex flex-col items-center justify-center w-36 h-36 border-2 border-dashed border-blue-500 rounded-md bg-blue-50 shadow-inner\",\n                                        children: matches[purpose.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-col items-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: computerTypes.find((type)=>type.id === matches[purpose.id]).image,\n                                                alt: \"Matched Computer\",\n                                                className: \"w-28 h-28 mb-2\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                                lineNumber: 126,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                            lineNumber: 125,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-blue-700 font-semibold text-center\",\n                                            children: purpose.label\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                            lineNumber: 137,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, purpose.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                        lineNumber: 118,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 116,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 83,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-purple-600 text-white font-bold rounded-lg shadow-md hover:bg-purple-700 transition-all\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 149,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: retry,\n                        className: \"ml-4 px-6 py-3 bg-gray-600 text-white font-bold rounded-lg shadow-md hover:bg-gray-700 transition-all\",\n                        children: \"Retry\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 148,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-8 p-4 rounded-lg shadow-md ${feedback.allCorrect ? \"bg-green-100\" : \"bg-yellow-100\"}`,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `text-lg font-bold text-center ${feedback.allCorrect ? \"text-green-700\" : \"text-yellow-700\"}`,\n                        children: feedback.message\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 169,\n                        columnNumber: 11\n                    }, undefined),\n                    !feedback.allCorrect && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 mt-4\",\n                        children: feedback.results.map((result, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: `${result.isCorrect ? \"text-green-700\" : \"text-red-700\"}`,\n                                children: [\n                                    result.purpose,\n                                    \":\",\n                                    \" \",\n                                    result.isCorrect ? \"Correct!\" : \"Incorrect\"\n                                ]\n                            }, index, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                                lineNumber: 179,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                        lineNumber: 177,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n                lineNumber: 164,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.4TypesOfComputers.js\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (TypesOfComputers);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.4TypesOfComputers.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.5HardwareAndSoftware.js":
/*!********************************************************!*\
  !*** ./pages/lessons/level1/1.5HardwareAndSoftware.js ***!
  \********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst HardwareAndSoftware = ()=>{\n    const items = [\n        {\n            id: 1,\n            name: \"Mouse\",\n            type: \"Hardware\",\n            image: \"/lvl1_img/download.jpeg\"\n        },\n        {\n            id: 2,\n            name: \"Keyboard\",\n            type: \"Hardware\",\n            image: \"/lvl1_img/a-children-s-storybook-illustration-of-a_TWRewv6OSx2ZXmbs_7GdPQ_ZjKDda0IR8mrk4f58u1Wfw.jpeg\"\n        },\n        {\n            id: 3,\n            name: \"Web Browser\",\n            type: \"Software\",\n            image: \"/lvl1_img/download (2).jpeg\"\n        },\n        {\n            id: 4,\n            name: \"Video Game\",\n            type: \"Software\",\n            image: \"/tetris.png\"\n        }\n    ];\n    const [currentItemIndex, setCurrentItemIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [score, setScore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [completed, setCompleted] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleAnswer = (answer)=>{\n        const currentItem = items[currentItemIndex];\n        const isCorrect = answer === currentItem.type;\n        if (isCorrect) {\n            setScore((prevScore)=>prevScore + 1);\n            setFeedback(\"🎉 Correct! Great job!\");\n        } else {\n            setFeedback(\"❌ Oops, that's not correct. Try again!\");\n        }\n        setTimeout(()=>{\n            if (currentItemIndex + 1 < items.length) {\n                setCurrentItemIndex((prevIndex)=>prevIndex + 1);\n                setFeedback(null);\n            } else {\n                setCompleted(true);\n            }\n        }, 1000);\n    };\n    const resetQuiz = ()=>{\n        setCurrentItemIndex(0);\n        setScore(0);\n        setFeedback(null);\n        setCompleted(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Hardware vs. Software\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/a-children-s-storybook-illustration-of-a_J3vVFxGhRDS1Kmx2vm5HWw_0yQljxkMRMGI2wW4RGanxA.jpeg\" // Replace with your image path\n                    ,\n                    alt: \"Operating System Illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                    lineNumber: 51,\n                    columnNumber: 15\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 50,\n                columnNumber: 10\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4 text-center\",\n                children: [\n                    \"Computers are made up of \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"hardware\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 60,\n                        columnNumber: 34\n                    }, undefined),\n                    \" and \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"software\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 60,\n                        columnNumber: 64\n                    }, undefined),\n                    \". Let's learn the difference!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 59,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Hardware:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 64,\n                                columnNumber: 11\n                            }, undefined),\n                            \" These are the physical parts of a computer that you can see and touch, like a mouse or keyboard.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 63,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Software:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            \" These are the programs that make the computer do cool stuff, like games or web browsers!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 62,\n                columnNumber: 7\n            }, undefined),\n            !completed ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: items[currentItemIndex].image,\n                            alt: items[currentItemIndex].name,\n                            className: \"w-48 h-48 border border-gray-300 rounded-lg shadow-md\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                            lineNumber: 76,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 75,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-bold mb-4\",\n                        children: items[currentItemIndex].name\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 82,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-x-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(\"Hardware\"),\n                                className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                                children: \"Hardware\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 84,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(\"Software\"),\n                                className: \"px-6 py-3 bg-purple-500 text-white rounded-lg hover:bg-purple-600\",\n                                children: \"Software\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 83,\n                        columnNumber: 11\n                    }, undefined),\n                    feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: `mt-4 text-lg font-bold ${feedback.includes(\"Correct\") ? \"text-green-600\" : \"text-red-600\"}`,\n                        children: feedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 98,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 73,\n                columnNumber: 9\n            }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Quiz Completed!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 109,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-4\",\n                        children: [\n                            \"Your Score: \",\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: [\n                                    score,\n                                    \"/\",\n                                    items.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                                lineNumber: 111,\n                                columnNumber: 25\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 110,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetQuiz,\n                        className: \"px-6 py-3 bg-green-500 text-white rounded-lg hover:bg-green-600\",\n                        children: \"Retry Quiz\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                        lineNumber: 113,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n                lineNumber: 108,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.5HardwareAndSoftware.js\",\n        lineNumber: 45,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HardwareAndSoftware);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.5HardwareAndSoftware.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/1.6OperatingSystem.js":
/*!****************************************************!*\
  !*** ./pages/lessons/level1/1.6OperatingSystem.js ***!
  \****************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/image */ \"(pages-dir-node)/./node_modules/next/image.js\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_image__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst OperatingSystem = ()=>{\n    const [quizAnswer, setQuizAnswer] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [quizFeedback, setQuizFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [dragFeedback, setDragFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const correctAnswer = \"Windows\";\n    const options = [\n        \"Windows\",\n        \"Google Chrome\",\n        \"Microsoft Word\",\n        \"Safari\"\n    ];\n    const correctMatches = {\n        Desktop: \"Windows\",\n        Mobile: \"Android\"\n    };\n    const handleQuizSubmit = ()=>{\n        if (quizAnswer === correctAnswer) {\n            setQuizFeedback(\"🎉 Correct! Windows is an operating system.\");\n        } else {\n            setQuizFeedback(\"❌ Incorrect. Try again!\");\n        }\n    };\n    const handleDragStart = (item)=>{\n        setDraggedItem(item);\n    };\n    const handleDrop = (target)=>{\n        setMatches((prev)=>({\n                ...prev,\n                [target]: draggedItem\n            }));\n        if (correctMatches[target] === draggedItem) {\n            setDragFeedback(`🎉 Correct! ${draggedItem} is the right match for ${target}.`);\n        } else {\n            setDragFeedback(`❌ Incorrect. ${draggedItem} does not match with ${target}.`);\n        }\n        setDraggedItem(null);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 p-6 bg-gradient-to-b from-blue-50 via-white to-green-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"What is an Operating System?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 43,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_image__WEBPACK_IMPORTED_MODULE_2___default()), {\n                    src: \"/lvl1_img/Untitled (6).png\" // Replace with your image path\n                    ,\n                    alt: \"Operating System Illustration\",\n                    width: 500,\n                    height: 300,\n                    className: \"rounded-lg shadow-md\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 48,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4 text-center\",\n                children: [\n                    \"An \",\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Operating System (OS)\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 59,\n                        columnNumber: 12\n                    }, undefined),\n                    \" is like the boss of a computer. It tells all the parts of the computer how to work together. Without an OS, your computer wouldn’t know how to show your games, apps, or even turn on properly!\"\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-4 border-l-4 border-blue-500 mb-4 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Example:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                            lineNumber: 63,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Imagine you have a robot that can dance, sing, and clean. The operating system is like the robot’s brain—it gives instructions so everything works smoothly.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 61,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: \"There are many types of operating systems:\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 66,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-6 text-lg mb-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Windows:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined),\n                            \" A popular OS for laptops and desktop computers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 70,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"MacOS:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 74,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The OS used by Apple computers.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Android:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Used in most smartphones and tablets.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"iOS:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 80,\n                                columnNumber: 11\n                            }, undefined),\n                            \" The OS for iPhones and iPads.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 79,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 69,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 mb-6 rounded-lg\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Without an operating system, your computer would be like a car without a driver—it has all the parts but doesn’t know how to move or what to do!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                    lineNumber: 85,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-green-100 p-4 border-l-4 border-green-500 mb-6 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity 1:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Which of the following is an operating system?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 92,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mt-4\",\n                        children: options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                    className: \"flex items-center space-x-2\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"radio\",\n                                            name: \"quiz\",\n                                            value: option,\n                                            onChange: (e)=>setQuizAnswer(e.target.value)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            children: option\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, index, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 97,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleQuizSubmit,\n                        className: \"mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                        children: \"Submit Answer\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 110,\n                        columnNumber: 9\n                    }, undefined),\n                    quizFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg font-bold\",\n                        children: quizFeedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 116,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 91,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-xl font-semibold\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Activity 2:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 122,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Match the operating systems to the devices they work best on.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 121,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-row mt-6 space-x-6 justify-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(\"Windows\"),\n                                        className: \"w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50\",\n                                        children: \"Windows\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 127,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(\"Android\"),\n                                        className: \"w-32 h-16 bg-white border border-gray-400 shadow-md flex items-center justify-center cursor-pointer hover:bg-gray-50\",\n                                        children: \"Android\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 126,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col items-center space-y-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(\"Desktop\"),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md\",\n                                        children: matches.Desktop || \"Desktop\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 145,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(\"Mobile\"),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-32 h-16 bg-gray-100 border-2 border-dashed border-gray-400 flex items-center justify-center rounded-md\",\n                                        children: matches.Mobile || \"Mobile\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                        lineNumber: 152,\n                                        columnNumber: 13\n                                    }, undefined)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                                lineNumber: 144,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 124,\n                        columnNumber: 9\n                    }, undefined),\n                    dragFeedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4 text-lg font-bold\",\n                        children: dragFeedback\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                        lineNumber: 161,\n                        columnNumber: 26\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n                lineNumber: 120,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/1.6OperatingSystem.js\",\n        lineNumber: 42,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (OperatingSystem);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/1.6OperatingSystem.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/2.1WhatIsScratch.js":
/*!**************************************************!*\
  !*** ./pages/lessons/level1/2.1WhatIsScratch.js ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/link */ \"(pages-dir-node)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst LandingPage = ()=>{\n    const steps = [\n        {\n            id: 1,\n            title: \"What is Scratch?\",\n            link: \"/lessons/level1/step-1\"\n        },\n        {\n            id: 2,\n            title: \"Set Up Scratch\",\n            link: \"/lessons/level1/step-2\"\n        },\n        {\n            id: 3,\n            title: \"Create Your First Project\",\n            link: \"/lessons/level1/step-3\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-purple-600 text-white py-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                        className: \"text-4xl font-bold text-center\",\n                        children: \"Getting Started with Scratch\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-center mt-2\",\n                        children: \"Learn Scratch step by step and start creating amazing projects today!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-4xl mx-auto mt-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4\",\n                        children: \"Learn Step by Step\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-4\",\n                        children: steps.map((step)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"p-4 bg-white rounded-lg shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                        className: \"text-lg font-semibold\",\n                                        children: step.title\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                        lineNumber: 25,\n                                        columnNumber: 15\n                                    }, undefined),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_2___default()), {\n                                        href: step.link,\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            className: \"mt-2 inline-block px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600\",\n                                            children: [\n                                                \"Go to Step \",\n                                                step.id\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                            lineNumber: 27,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                        lineNumber: 26,\n                                        columnNumber: 15\n                                    }, undefined)\n                                ]\n                            }, step.id, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                                lineNumber: 24,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                        lineNumber: 22,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 20,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-100 mt-12 py-4 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        \"\\xa9 \",\n                        new Date().getFullYear(),\n                        \" Getting Started with Scratch. All Rights Reserved.\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                    lineNumber: 37,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n                lineNumber: 36,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/2.1WhatIsScratch.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (LandingPage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/2.1WhatIsScratch.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/3.1WhatIsIoT.js":
/*!**********************************************!*\
  !*** ./pages/lessons/level1/3.1WhatIsIoT.js ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTMatchingActivity = ()=>{\n    const canvasRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [connections, setConnections] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [draggedItem, setDraggedItem] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const devices = [\n        {\n            id: \"Smart Fridge\",\n            x: 100,\n            y: 80,\n            label: \"Smart Fridge\",\n            icon: \"🧊\"\n        },\n        {\n            id: \"Smart Bulb\",\n            x: 100,\n            y: 220,\n            label: \"Smart Bulb\",\n            icon: \"💡\"\n        },\n        {\n            id: \"Smart Watch\",\n            x: 100,\n            y: 360,\n            label: \"Smart Watch\",\n            icon: \"⌚\"\n        }\n    ];\n    const correctMatches = {\n        \"Smart Fridge\": \"Internet\",\n        \"Smart Bulb\": \"Internet\",\n        \"Smart Watch\": \"Internet\"\n    };\n    const internet = {\n        id: \"Internet\",\n        x: 550,\n        y: 220,\n        label: \"Internet\",\n        icon: \"☁️\"\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"IoTMatchingActivity.useEffect\": ()=>{\n            drawAllConnections();\n        }\n    }[\"IoTMatchingActivity.useEffect\"], [\n        connections\n    ]);\n    const startDrag = (device)=>{\n        setDraggedItem(device);\n        setFeedback(\"\"); // Clear feedback while dragging\n    };\n    const handleDrop = (target)=>{\n        if (draggedItem) {\n            const isAlreadyConnected = connections.some((connection)=>connection.from.id === draggedItem.id);\n            if (isAlreadyConnected) {\n                setFeedback(`⚠️ ${draggedItem.label} is already connected!`);\n                setDraggedItem(null);\n                return;\n            }\n            const isCorrect = correctMatches[draggedItem.id] === target.id;\n            setConnections((prev)=>[\n                    ...prev,\n                    {\n                        from: draggedItem,\n                        to: target,\n                        isCorrect\n                    }\n                ]);\n            setFeedback(isCorrect ? `✅ Signal Sent! ${draggedItem.label} connected to the Internet Cloud!` : `❌ Oops! ${draggedItem.label} couldn't connect. Try again!`);\n            setDraggedItem(null); // Reset dragged item\n        }\n    };\n    const drawAllConnections = ()=>{\n        const canvas = canvasRef.current;\n        if (canvas) {\n            const ctx = canvas.getContext(\"2d\");\n            ctx.clearRect(0, 0, canvas.width, canvas.height); // Clear previous lines\n            connections.forEach((connection)=>{\n                drawLine(connection.from, connection.to, connection.isCorrect, ctx);\n            });\n        }\n    };\n    const drawLine = (from, to, isCorrect, ctx)=>{\n        ctx.beginPath();\n        ctx.moveTo(from.x + 50, from.y + 40); // Start position (IoT device)\n        ctx.lineTo(to.x + 60, to.y + 40); // End position (Internet)\n        ctx.strokeStyle = isCorrect ? \"#4CAF50\" : \"#F44336\"; // Green for correct, red for incorrect\n        ctx.lineWidth = 2;\n        ctx.stroke();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 bg-white text-purple-700\",\n                children: \"IoT Signal Matching\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 84,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 w-full max-w-4xl text-left\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"What is the Internet of Things (IoT)?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"The Internet of Things, or IoT, is like a magic network that connects everyday objects to the internet!\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 90,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Imagine if your toys, toothbrush, or even your fridge could talk to each other and to you. With IoT, this is possible because tiny computers called sensors are put into everyday items. These sensors can collect information, like how often you brush your teeth, and send that data to other devices or even your smartphone.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 89,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: \"/lvl1_img/Untitled (7).png\",\n                        alt: \"IoT illustration\",\n                        className: \"w-full mb-4 rounded-lg shadow-md\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 95,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Fun Fact:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined),\n                            ' Thanks to IoT, your home could become a \"smart home\" where lights turn off automatically when you leave the room, or your fridge could remind you when you\\'re out of milk! How cool is that?'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 100,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 87,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mb-6 w-full max-w-4xl text-center bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg shadow-md\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"How to Play\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 108,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: 'Drag and drop each IoT device (e.g., Smart Fridge, Smart Bulb) to the Internet Cloud to connect them. Watch the lines appear between the devices and the cloud! Each correct connection will be marked as \"Correct\" in the history below. Can you connect all the devices?'\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 107,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative p-8 rounded-lg w-full max-w-4xl h-96\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"canvas\", {\n                        ref: canvasRef,\n                        width: 700,\n                        height: 400,\n                        className: \"absolute top-0 left-0 pointer-events-none\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 119,\n                        columnNumber: 9\n                    }, undefined),\n                    devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"w-24 h-24 bg-white rounded-full shadow-md flex flex-col items-center justify-center cursor-pointer border-4 border-yellow-400\",\n                            style: {\n                                position: \"absolute\",\n                                top: device.y,\n                                left: device.x\n                            },\n                            draggable: true,\n                            onDragStart: ()=>startDrag(device),\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-3xl\",\n                                    children: device.icon\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                    lineNumber: 139,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"mt-1 text-xs font-bold text-gray-800\",\n                                    children: device.label\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, device.id, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                            lineNumber: 128,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-48 h-48 bg-white rounded-full shadow-md border-4 border-blue-400 flex flex-col items-center justify-center cursor-pointer\",\n                        style: {\n                            position: \"absolute\",\n                            top: internet.y,\n                            left: internet.x\n                        },\n                        onDrop: ()=>handleDrop(internet),\n                        onDragOver: (e)=>e.preventDefault(),\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-5xl\",\n                                children: internet.icon\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 155,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm font-bold text-gray-800\",\n                                children: internet.label\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 145,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 117,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-4 px-4 py-2 rounded-lg shadow-md w-full max-w-2xl text-center ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : feedback.startsWith(\"⚠️\") ? \"bg-yellow-100 text-yellow-700\" : \"bg-red-100 text-red-700\"}`,\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 162,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 w-full max-w-4xl text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold mb-4\",\n                        children: \"Connections\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 177,\n                        columnNumber: 9\n                    }, undefined),\n                    connections.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"space-y-2\",\n                        children: connections.map((connection, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: `font-bold ${connection.isCorrect ? \"text-green-700\" : \"text-red-700\"}`,\n                                children: [\n                                    connection.from.label,\n                                    \" → \",\n                                    connection.to.label,\n                                    \" (\",\n                                    connection.isCorrect ? \"Correct\" : \"Wrong\",\n                                    \").\"\n                                ]\n                            }, index, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                                lineNumber: 181,\n                                columnNumber: 15\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 179,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-500\",\n                        children: \"No connections made yet.\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                        lineNumber: 193,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n                lineNumber: 176,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.1WhatIsIoT.js\",\n        lineNumber: 83,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTMatchingActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/3.1WhatIsIoT.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/3.2HowIoTWorks.js":
/*!************************************************!*\
  !*** ./pages/lessons/level1/3.2HowIoTWorks.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTHowItWorksActivity = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const items = [\n        {\n            id: \"Smart Devices\",\n            label: \"Smart Devices\",\n            icon: \"📱\"\n        },\n        {\n            id: \"IoT Applications\",\n            label: \"IoT Applications\",\n            icon: \"🧠\"\n        },\n        {\n            id: \"Graphical User Interface\",\n            label: \"Graphical User Interface\",\n            icon: \"💻\"\n        }\n    ];\n    const descriptions = [\n        {\n            id: \"Smart Devices\",\n            label: \"Everyday items like your TV, toys, or even cars are equipped with sensors to collect data.\"\n        },\n        {\n            id: \"IoT Applications\",\n            label: \"Applications analyze the data collected and decide what action to take.\"\n        },\n        {\n            id: \"Graphical User Interface\",\n            label: \"You control everything using an app on your phone or computer.\"\n        }\n    ];\n    const correctMatches = {\n        \"Smart Devices\": \"Smart Devices\",\n        \"IoT Applications\": \"IoT Applications\",\n        \"Graphical User Interface\": \"Graphical User Interface\"\n    };\n    const handleDragStart = (id)=>{\n        setFeedback(\"\"); // Clear feedback while dragging\n        setMatches((prev)=>({\n                ...prev,\n                dragging: id\n            }));\n    };\n    const handleDrop = (descriptionId)=>{\n        const draggingId = matches.dragging;\n        if (draggingId) {\n            setMatches((prev)=>({\n                    ...prev,\n                    [descriptionId]: draggingId,\n                    dragging: null\n                }));\n        }\n    };\n    const checkAnswers = ()=>{\n        const results = Object.keys(correctMatches).map((descriptionId)=>({\n                description: descriptionId,\n                isCorrect: correctMatches[descriptionId] === matches[descriptionId]\n            }));\n        const allCorrect = results.every((result)=>result.isCorrect);\n        setFeedback(allCorrect ? \"🎉 Great job! All matches are correct!\" : \"❌ Some matches are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setMatches({});\n        setFeedback(\"\");\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center text-gray-700 bg-gradient-to-b from-blue-50 via-white to-green-50 p-6 min-h-screen\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700\",\n                children: \"How IoT Works\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-xl text-center mb-4\",\n                children: \"IoT works by connecting devices that communicate with each other over the internet. Let’s explore its components:\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                className: \"list-disc pl-6 mb-6 text-lg text-left max-w-4xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Smart Devices:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 83,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Everyday items like TVs, toys, or cars with sensors that collect data.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 82,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"IoT Applications:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Analyze data and take actions like turning on lights automatically.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 85,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Graphical User Interface:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Lets you control devices using apps on your phone or computer.\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mt-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Example:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 92,\n                                columnNumber: 11\n                            }, undefined),\n                            \" Your smart garden can water plants when it detects they are dry. Amazing, right?\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 81,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-2 gap-8 w-full max-w-5xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-blue-600\",\n                                children: \"Components\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 100,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-6\",\n                                children: items.map((item)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: ()=>handleDragStart(item.id),\n                                        className: \"flex items-center justify-center w-56 h-20 bg-white border-2 border-gray-300 rounded-md shadow-sm cursor-pointer hover:shadow-lg transition\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-2xl mr-2\",\n                                                children: item.icon\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 109,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"text-lg\",\n                                                children: item.label\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 110,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        ]\n                                    }, item.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                        lineNumber: 103,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 101,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-green-600\",\n                                children: \"Descriptions\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 118,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-8\",\n                                children: descriptions.map((description)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDrop: ()=>handleDrop(description.id),\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        className: \"w-full h-24 bg-gray-50 border-2 border-dashed border-blue-500 rounded-md p-2 flex items-center justify-center hover:bg-blue-50 transition\",\n                                        children: matches[description.id] ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-full h-full bg-green-100 rounded-md flex items-center justify-center text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                children: matches[description.id]\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                                lineNumber: 129,\n                                                columnNumber: 21\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                            lineNumber: 128,\n                                            columnNumber: 19\n                                        }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 text-center\",\n                                            children: description.label\n                                        }, void 0, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                            lineNumber: 132,\n                                            columnNumber: 19\n                                        }, undefined)\n                                    }, description.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                        lineNumber: 121,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 flex space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600 transition\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 142,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetActivity,\n                        className: \"px-6 py-3 bg-red-500 text-white rounded-lg shadow-md hover:bg-red-600 transition\",\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                        lineNumber: 148,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 141,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 px-4 py-2 rounded-lg shadow-md ${feedback.includes(\"Great\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                    lineNumber: 165,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n                lineNumber: 158,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.2HowIoTWorks.js\",\n        lineNumber: 71,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTHowItWorksActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/3.2HowIoTWorks.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/3.3IoTExamples.js":
/*!************************************************!*\
  !*** ./pages/lessons/level1/3.3IoTExamples.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst SmartCityActivity = ()=>{\n    const [cityZones, setCityZones] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        traffic: [],\n        parking: [],\n        environment: []\n    });\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const devices = [\n        {\n            id: \"Smart Traffic Light\",\n            label: \"Smart Traffic Light 🚦\",\n            correctZone: \"traffic\"\n        },\n        {\n            id: \"Parking Sensor\",\n            label: \"Parking Sensor 🅿️\",\n            correctZone: \"parking\"\n        },\n        {\n            id: \"Air Quality Monitor\",\n            label: \"Air Quality Monitor 🌫️\",\n            correctZone: \"environment\"\n        },\n        {\n            id: \"Smart Waste Bin\",\n            label: \"Smart Waste Bin 🗑️\",\n            correctZone: \"environment\"\n        },\n        {\n            id: \"Smart Streetlight\",\n            label: \"Smart Streetlight 💡\",\n            correctZone: \"traffic\"\n        }\n    ];\n    const handleDrop = (zone, device)=>{\n        if (device.correctZone === zone) {\n            setCityZones((prev)=>({\n                    ...prev,\n                    [zone]: [\n                        ...prev[zone],\n                        device\n                    ]\n                }));\n            setFeedback(`✅ Correct! ${device.label} belongs in the ${zone} zone.`);\n        } else {\n            setFeedback(`❌ Oops! ${device.label} doesn’t belong in the ${zone} zone. Try again!`);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col items-center min-h-screen bg-gradient-to-b from-blue-50 via-green-50 to-yellow-50 p-6 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold text-blue-700 mb-6\",\n                children: \"Build Your Smart City\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 34,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl text-center mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"A smart city uses IoT devices to improve the quality of life by managing resources and services efficiently.\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 38,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg mt-2\",\n                        children: \"Drag and drop IoT devices into the correct city zones below. You'll receive feedback for each action. Can you complete your smart city?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 41,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 37,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col lg:flex-row gap-8 items-start w-full max-w-6xl\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-1/3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold mb-4 text-green-600\",\n                                children: \"IoT Devices\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 50,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: devices.map((device)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>e.dataTransfer.setData(\"device\", JSON.stringify(device)),\n                                        className: \"p-4 bg-white border border-gray-300 rounded-lg shadow-lg hover:shadow-xl cursor-pointer transition\",\n                                        children: device.label\n                                    }, device.id, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 53,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 51,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 49,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-full lg:w-2/3 grid grid-cols-1 lg:grid-cols-3 gap-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-bold col-span-3 text-blue-700 mb-4 text-center\",\n                                children: \"City Zones\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 67,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"traffic\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-blue-100 border-2 border-dashed border-blue-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-blue-700\",\n                                        children: \"Traffic Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 78,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.traffic.length > 0 && cityZones.traffic.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 81,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 70,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"parking\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-green-100 border-2 border-dashed border-green-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-green-700\",\n                                        children: \"Parking Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 96,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.parking.length > 0 && cityZones.parking.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 99,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 88,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                onDrop: (e)=>{\n                                    const device = JSON.parse(e.dataTransfer.getData(\"device\"));\n                                    handleDrop(\"environment\", device);\n                                },\n                                onDragOver: (e)=>e.preventDefault(),\n                                className: \"h-36 bg-yellow-100 border-2 border-dashed border-yellow-500 rounded-lg flex flex-col items-center justify-center transition hover:shadow-md\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"font-semibold text-yellow-700\",\n                                        children: \"Environment Zone\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 114,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    cityZones.environment.length > 0 && cityZones.environment.map((device, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm mt-1\",\n                                            children: device.label\n                                        }, index, false, {\n                                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                            lineNumber: 117,\n                                            columnNumber: 17\n                                        }, undefined))\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 106,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 66,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 47,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 p-4 rounded-lg shadow-lg text-lg font-semibold transition ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: feedback\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 w-full max-w-4xl bg-white p-6 border border-gray-300 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-gray-800 mb-4\",\n                        children: \"City Summary\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 140,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 space-y-2 text-lg\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Traffic Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 143,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.traffic.length > 0 ? cityZones.traffic.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 142,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Parking Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 149,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.parking.length > 0 ? cityZones.parking.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 148,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: \"Environment Zone:\"\n                                    }, void 0, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                        lineNumber: 155,\n                                        columnNumber: 13\n                                    }, undefined),\n                                    \" \",\n                                    cityZones.environment.length > 0 ? cityZones.environment.map((device)=>device.label).join(\", \") : \"No devices added yet.\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                                lineNumber: 154,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                        lineNumber: 141,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n                lineNumber: 139,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.3IoTExamples.js\",\n        lineNumber: 33,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (SmartCityActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/3.3IoTExamples.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/3.4IoTBenefits.js":
/*!************************************************!*\
  !*** ./pages/lessons/level1/3.4IoTBenefits.js ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst IoTBenefits = ()=>{\n    const [unlockedBenefits, setUnlockedBenefits] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const benefits = [\n        {\n            id: \"Innovation\",\n            label: \"Innovation 💡\",\n            description: \"Businesses can create smarter products based on what customers need.\"\n        },\n        {\n            id: \"Efficiency\",\n            label: \"Efficiency ⚙️\",\n            description: \"IoT helps make tasks faster and more productive, like keeping factories running smoothly.\"\n        },\n        {\n            id: \"Safety\",\n            label: \"Safety 🛡️\",\n            description: \"IoT devices can monitor workplaces and send alerts if something is wrong.\"\n        }\n    ];\n    const handleUnlock = (id)=>{\n        if (!unlockedBenefits.includes(id)) {\n            setUnlockedBenefits((prev)=>[\n                    ...prev,\n                    id\n                ]);\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-50 p-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-5xl font-bold mb-6 text-purple-700 text-center\",\n                children: \"Benefits of IoT\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 32,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-8\",\n                children: \"Discover how IoT makes life better! Click on the colorful icons to unlock and learn about each benefit.\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 33,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 sm:grid-cols-3 gap-8 justify-center items-center mb-12\",\n                children: benefits.map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        onClick: ()=>handleUnlock(benefit.id),\n                        className: `p-6 w-full h-40 flex flex-col items-center justify-center cursor-pointer rounded-lg shadow-lg transform hover:scale-105 transition-transform ${unlockedBenefits.includes(benefit.id) ? \"bg-green-100 border-4 border-green-500\" : \"bg-gray-100 border-4 border-gray-300\"}`,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-5xl\",\n                                children: benefit.label.split(\" \")[1]\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-4 font-bold text-xl text-gray-800\",\n                                children: benefit.label.split(\" \")[0]\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 50,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, benefit.id, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined))\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 38,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-6 border-l-4 border-blue-500 rounded-lg shadow-lg mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-3xl font-semibold mb-4 text-blue-700\",\n                        children: \"Unlocked Benefits\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 57,\n                        columnNumber: 9\n                    }, undefined),\n                    unlockedBenefits.length > 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                        className: \"list-disc pl-6 space-y-4 text-lg\",\n                        children: benefits.filter((benefit)=>unlockedBenefits.includes(benefit.id)).map((benefit)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                className: \"text-gray-800\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                        children: [\n                                            benefit.label.split(\" \")[0],\n                                            \":\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                                        lineNumber: 64,\n                                        columnNumber: 19\n                                    }, undefined),\n                                    \" \",\n                                    benefit.description\n                                ]\n                            }, benefit.id, true, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                                lineNumber: 63,\n                                columnNumber: 17\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 59,\n                        columnNumber: 11\n                    }, undefined) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: \"Click on the icons above to reveal the benefits of IoT!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 69,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 56,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-yellow-100 p-6 border-l-4 border-yellow-500 rounded-lg shadow-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-semibold mb-4 text-yellow-700\",\n                        children: \"Creative Activity\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 75,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"Imagine you could invent an IoT device to help with your daily chores, like cleaning your room or finishing homework. What would it do? Write your idea on paper and share it with your friends or family!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n                lineNumber: 74,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/3.4IoTBenefits.js\",\n        lineNumber: 31,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (IoTBenefits);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/3.4IoTBenefits.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/4.1WhatIsComputerVision.js":
/*!*********************************************************!*\
  !*** ./pages/lessons/level1/4.1WhatIsComputerVision.js ***!
  \*********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst ComputerVisionActivity = ()=>{\n    const [matches, setMatches] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isHappy, setIsHappy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false); // Track computer's mood\n    const images = [\n        {\n            id: \"cat\",\n            label: \"Cat\",\n            src: \"/lvl1_img/cat.jpg\"\n        },\n        {\n            id: \"car\",\n            label: \"Car\",\n            src: \"/lvl1_img/car.jpeg\"\n        },\n        {\n            id: \"apple\",\n            label: \"Apple\",\n            src: \"/lvl1_img/apple.jpeg\"\n        }\n    ];\n    const correctMatches = {\n        cat: \"Cat\",\n        car: \"Car\",\n        apple: \"Apple\"\n    };\n    const handleDrop = (imageId, label)=>{\n        setMatches((prev)=>({\n                ...prev,\n                [imageId]: label\n            }));\n    };\n    const checkAnswers = ()=>{\n        const allCorrect = Object.keys(correctMatches).every((key)=>matches[key] === correctMatches[key]);\n        setIsHappy(allCorrect); // Change mood to happy if all answers are correct\n        setFeedback(allCorrect ? \"🎉 Great job! You've trained the computer successfully!\" : \"❌ Some matches are incorrect. Try again!\");\n    };\n    const resetActivity = ()=>{\n        setMatches({});\n        setFeedback(\"\");\n        setIsHappy(false); // Reset to confused mood\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"text-gray-700 bg-white mt-6 p-4 rounded-lg mb-7\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-bold mb-6 text-purple-700 \",\n                children: \"What is Computer Vision?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                        children: \"Computer Vision\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 52,\n                        columnNumber: 9\n                    }, undefined),\n                    ' is like teaching computers how to \"see\" the world, just like we do with our eyes! Computers use cameras and special programs to look at pictures and understand what they are. It\\'s like magic, but real!'\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 51,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"bg-blue-100 p-4 border-l-5 border-blue-500 mb-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                            children: \"Fun Fact:\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                            lineNumber: 57,\n                            columnNumber: 11\n                        }, undefined),\n                        \" Did you know your phone's camera uses computer vision to recognize your face and unlock? It’s just like how you recognize your friends and family!\"\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 56,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 55,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center mb-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                    src: isHappy ? \"/lvl1_img/happy (1).jpeg\" : \"/lvl1_img/happy (2).jpeg\",\n                    alt: isHappy ? \"Happy Computer\" : \"Confused Computer\",\n                    className: \"w-40 h-40\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 64,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 63,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                className: \"text-2xl font-bold mb-4\",\n                children: \"Activity: Train the Computer\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg mb-4\",\n                children: \"Computers learn to recognize objects by being trained with examples. Drag the correct label to each image to help the computer understand what it is seeing. Let's teach the computer together!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col md:flex-row items-start justify-center gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4\",\n                                children: \"Images\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 85,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: images.map((image)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        onDragOver: (e)=>e.preventDefault(),\n                                        onDrop: (e)=>{\n                                            const label = e.dataTransfer.getData(\"label\");\n                                            handleDrop(image.id, label);\n                                        },\n                                        className: \"w-32 h-32 bg-gray-100 border-2 border-dashed border-gray-400 rounded-md flex items-center justify-center relative\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image.src,\n                                                alt: image.label,\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                                lineNumber: 97,\n                                                columnNumber: 17\n                                            }, undefined),\n                                            matches[image.id] && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"absolute bottom-0 text-sm font-bold bg-green-200 p-1 rounded-md\",\n                                                children: matches[image.id]\n                                            }, void 0, false, {\n                                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                                lineNumber: 99,\n                                                columnNumber: 19\n                                            }, undefined)\n                                        ]\n                                    }, image.id, true, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                        lineNumber: 88,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 86,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 84,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold mb-4\",\n                                children: \"Labels\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 110,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"space-y-4\",\n                                children: Object.values(correctMatches).map((label)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        draggable: true,\n                                        onDragStart: (e)=>e.dataTransfer.setData(\"label\", label),\n                                        className: \"w-32 p-2 bg-white border border-gray-300 rounded-md shadow-sm text-center cursor-pointer hover:shadow-lg\",\n                                        children: label\n                                    }, label, false, {\n                                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                        lineNumber: 113,\n                                        columnNumber: 15\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                                lineNumber: 111,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 109,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 82,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 space-x-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: checkAnswers,\n                        className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                        children: \"Check Answers\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 128,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: resetActivity,\n                        className: \"px-6 py-3 bg-red-500 text-white rounded-lg hover:bg-red-600\",\n                        children: \"Reset\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 127,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `mt-6 p-4 rounded-lg shadow-md ${feedback.includes(\"Great\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                    lineNumber: 151,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 144,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-8 bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold mb-2\",\n                        children: \"What Did We Learn?\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 157,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg\",\n                        children: \"Computer vision works by learning from examples, just like we taught the computer in this activity. By labeling images, we help computers understand objects and make smart decisions. Imagine all the amazing things this technology can do!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                        lineNumber: 158,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n                lineNumber: 156,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.1WhatIsComputerVision.js\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ComputerVisionActivity);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/4.1WhatIsComputerVision.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/4.2HowComputerVisionWorks.js":
/*!***********************************************************!*\
  !*** ./pages/lessons/level1/4.2HowComputerVisionWorks.js ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst HowComputerVisionWorks = ()=>{\n    const [currentStep, setCurrentStep] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const steps = [\n        {\n            id: 1,\n            title: \"Seeing the World\",\n            description: \"Cameras and sensors capture images or videos, just like taking a picture with your phone!\",\n            action: \"Click to see the captured image!\",\n            image: \"/lvl1_img/Smart453ForFour_RVC_Installed.png\"\n        },\n        {\n            id: 2,\n            title: \"Understanding the Image\",\n            description: \"The computer breaks the image into tiny pieces (pixels) and analyzes them. These pixels are just tiny colored dots!\",\n            action: \"Click to see the pixelated version of the image!\",\n            image: \"/lvl1_img/download (1).png\"\n        },\n        {\n            id: 3,\n            title: \"Learning to Recognize\",\n            description: \"The computer compares this image with thousands of examples to understand what it is looking at. It's like studying for a test!\",\n            action: \"Click to see the computer's guess!\",\n            image: \"/lvl1_img/pxArt (1).png\"\n        },\n        {\n            id: 4,\n            title: \"Making Decisions\",\n            description: \"Finally, the computer identifies the object and decides what action to take, like recognizing a stop sign and stopping the car.\",\n            action: \"Click to see the computer's decision!\",\n            image: \"/lvl1_img/stop-stamp-7.png\"\n        }\n    ];\n    const handleNextStep = ()=>{\n        setCurrentStep((prev)=>Math.min(prev + 1, steps.length - 1));\n    };\n    const handleReset = ()=>{\n        setCurrentStep(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-extrabold mb-6 text-center text-purple-700\",\n                children: \"How Does Computer Vision Work?\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 50,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-8\",\n                children: 'Computer Vision works in several steps to \"see\" and understand the world. Let’s explore each step!'\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 53,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-2xl font-bold text-blue-600 mb-4 text-center\",\n                        children: steps[currentStep].title\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 59,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-600 text-center mb-6\",\n                        children: steps[currentStep].description\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 62,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: steps[currentStep].image,\n                                alt: steps[currentStep].title,\n                                className: \"w-48 h-48 mb-6 border-2 border-gray-200 rounded-lg shadow-md\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                                lineNumber: 66,\n                                columnNumber: 11\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleNextStep,\n                                className: `px-6 py-3 rounded-lg text-lg font-semibold ${currentStep === steps.length - 1 ? \"bg-gray-300 text-gray-500 cursor-not-allowed\" : \"bg-blue-500 text-white hover:bg-blue-600\"}`,\n                                disabled: currentStep === steps.length - 1,\n                                children: steps[currentStep].action\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                                lineNumber: 71,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 65,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 58,\n                columnNumber: 7\n            }, undefined),\n            currentStep === steps.length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 text-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: handleReset,\n                    className: \"px-6 py-3 bg-red-500 text-white rounded-lg font-bold hover:bg-red-600\",\n                    children: \"Start Over\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                    lineNumber: 88,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 87,\n                columnNumber: 9\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-12 bg-green-100 p-6 rounded-lg border-l-4 border-green-500 shadow-md max-w-4xl mx-auto\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-bold text-green-800 mb-2\",\n                        children: \"Real-Life Example\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 99,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-gray-700\",\n                        children: \"In a self-driving car, computer vision can recognize a stop sign and tell the car to stop. It goes through all these steps in just seconds!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                        lineNumber: 102,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.2HowComputerVisionWorks.js\",\n        lineNumber: 49,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (HowComputerVisionWorks);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/4.2HowComputerVisionWorks.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/4.3ComputerVisionApplications.js":
/*!***************************************************************!*\
  !*** ./pages/lessons/level1/4.3ComputerVisionApplications.js ***!
  \***************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst GuessTheVision = ()=>{\n    const [currentQuestion, setCurrentQuestion] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [feedback, setFeedback] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const questions = [\n        {\n            id: 1,\n            image: \"/lvl1_img/download (1).png\",\n            question: \"What should the self-driving car do when it sees this?\",\n            options: [\n                \"Stop\",\n                \"Speed Up\",\n                \"Turn\"\n            ],\n            correct: \"Stop\"\n        },\n        {\n            id: 2,\n            image: \"/lvl1_img/caerra.jpeg\",\n            question: \"What is the traffic camera likely analyzing?\",\n            options: [\n                \"Weather\",\n                \"Traffic Flow\",\n                \"Birds\"\n            ],\n            correct: \"Traffic Flow\"\n        },\n        {\n            id: 3,\n            image: \"/lvl1_img/download (5).jpeg\",\n            question: \"What can computer vision help doctors do with this X-ray?\",\n            options: [\n                \"Diagnose Diseases\",\n                \"Take Photos\",\n                \"Print Reports\"\n            ],\n            correct: \"Diagnose Diseases\"\n        },\n        {\n            id: 4,\n            image: \"/lvl1_img/download (6).jpeg\",\n            question: \"What can computer vision do in a factory?\",\n            options: [\n                \"Check Product Quality\",\n                \"Print Instructions\",\n                \"Turn Off Machines\"\n            ],\n            correct: \"Check Product Quality\"\n        },\n        {\n            id: 5,\n            image: \"/lvl1_img/download (7).jpeg\",\n            question: \"What is computer vision likely doing in this store?\",\n            options: [\n                \"Tracking Shopper Behavior\",\n                \"Counting Birds\",\n                \"Watching TV\"\n            ],\n            correct: \"Tracking Shopper Behavior\"\n        }\n    ];\n    const handleAnswer = (option)=>{\n        if (option === questions[currentQuestion].correct) {\n            setFeedback(\"✅ Correct! Great job!\");\n        } else {\n            setFeedback(\"❌ Incorrect. Try again!\");\n        }\n    };\n    const nextQuestion = ()=>{\n        setFeedback(\"\");\n        setCurrentQuestion((prev)=>Math.min(prev + 1, questions.length - 1));\n    };\n    const resetActivity = ()=>{\n        setFeedback(\"\");\n        setCurrentQuestion(0);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-green-100 p-8 text-gray-700\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                className: \"text-4xl font-extrabold text-center mb-6 text-purple-700\",\n                children: \"Guess the Vision\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 65,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                className: \"text-lg text-center mb-6\",\n                children: \"Can you guess what the computer should do in these scenarios? Let’s find out!\"\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 68,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col items-center\",\n                        children: [\n                            questions[currentQuestion].image && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                src: questions[currentQuestion].image,\n                                alt: \"Question Image\",\n                                className: \"w-48 h-48 mb-4 border border-gray-300 rounded-md shadow-md\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 76,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-lg font-semibold text-gray-800 mb-4\",\n                                children: questions[currentQuestion].question\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 82,\n                                columnNumber: 11\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 74,\n                        columnNumber: 9\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 gap-4\",\n                        children: questions[currentQuestion].options.map((option, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>handleAnswer(option),\n                                className: \"w-full px-6 py-3 bg-blue-500 text-white rounded-lg shadow-md hover:bg-blue-600\",\n                                children: option\n                            }, index, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                                lineNumber: 90,\n                                columnNumber: 13\n                            }, undefined))\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 73,\n                columnNumber: 7\n            }, undefined),\n            feedback && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `max-w-4xl mx-auto mt-6 p-4 rounded-lg shadow-md ${feedback.startsWith(\"✅\") ? \"bg-green-100 text-green-700\" : \"bg-red-100 text-red-700\"}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-center text-lg font-bold\",\n                    children: feedback\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                    lineNumber: 110,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 103,\n                columnNumber: 9\n            }, undefined),\n            currentQuestion < questions.length - 1 && feedback.startsWith(\"✅\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mt-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: nextQuestion,\n                    className: \"px-6 py-3 bg-green-500 text-white rounded-lg shadow-md hover:bg-green-600\",\n                    children: \"Next Question\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                    lineNumber: 117,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 116,\n                columnNumber: 9\n            }, undefined),\n            currentQuestion === questions.length - 1 && feedback.startsWith(\"✅\") && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"mt-6 max-w-4xl mx-auto bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg font-bold text-center\",\n                        children: \"\\uD83C\\uDF89 You've completed the activity! Great job!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 129,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center mt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: resetActivity,\n                            className: \"px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600\",\n                            children: \"Play Again\"\n                        }, void 0, false, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                            lineNumber: 133,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n                lineNumber: 128,\n                columnNumber: 9\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/4.3ComputerVisionApplications.js\",\n        lineNumber: 64,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (GuessTheVision);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/4.3ComputerVisionApplications.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/5.1Summary.js":
/*!********************************************!*\
  !*** ./pages/lessons/level1/5.1Summary.js ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/router */ \"(pages-dir-node)/./node_modules/next/router.js\");\n/* harmony import */ var next_router__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_router__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst Summary = ()=>{\n    const router = (0,next_router__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const handleStartQuiz = ()=>{\n        router.push(\"/lessons/quiz\"); // Navigate to the Quiz page\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-50 via-white to-purple-100 p-8 text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto bg-white shadow-lg rounded-lg p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-4xl font-extrabold mb-6 text-purple-700 text-center\",\n                    children: \"Summary of Key Concepts\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6 text-center\",\n                    children: \"Here's a recap of what we've learned in this course:\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 17,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc pl-6 mb-6 text-lg space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Computers:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 23,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Powerful machines that process data using input, memory, processing, and output.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 22,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Programs:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 26,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Sets of instructions that tell computers what to do.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 25,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Internet of Things (IoT):\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 29,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" Connects everyday objects to the internet, creating smart homes, cities, and workplaces.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 28,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Computer Vision:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 32,\n                                    columnNumber: 13\n                                }, undefined),\n                                ' Enables computers to \"see\" and make decisions based on images or videos.'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 31,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Scratch:\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                                    lineNumber: 35,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" A platform for creating fun, interactive games and animations using simple coding blocks.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                            lineNumber: 34,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 21,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-blue-100 p-4 border-l-4 border-blue-500 rounded-lg\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-lg text-center font-semibold\",\n                        children: \"Reflect on these topics to prepare for the quiz!\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                        lineNumber: 40,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 39,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-8 text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: handleStartQuiz,\n                        className: \"px-6 py-3 bg-purple-500 text-white rounded-lg shadow-md hover:bg-purple-600\",\n                        children: \"Take the Quiz\"\n                    }, void 0, false, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                        lineNumber: 46,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n                    lineNumber: 45,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n            lineNumber: 13,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.1Summary.js\",\n        lineNumber: 12,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (Summary);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/5.1Summary.js\n");

/***/ }),

/***/ "(pages-dir-node)/./pages/lessons/level1/5.3CompletionMessage.js":
/*!******************************************************!*\
  !*** ./pages/lessons/level1/5.3CompletionMessage.js ***!
  \******************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"react/jsx-dev-runtime\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"react\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nconst CompletionMessage = ()=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-b from-blue-100 via-white to-green-100 p-8 text-gray-700\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-3xl mx-auto bg-white shadow-lg rounded-lg p-6 text-center\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                    className: \"text-5xl font-extrabold mb-6 text-purple-700 animate-pulse\",\n                    children: \"\\uD83C\\uDF89 Congratulations! \\uD83C\\uDF89\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 7,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6\",\n                    children: \"You’ve successfully completed the Level 1 course! Here’s what you’ve accomplished:\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 10,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                    className: \"list-disc pl-6 mb-6 text-left text-lg space-y-3\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Learned how computers work\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 16,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and the basics of input, processing, and output.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 15,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Explored programming concepts\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 19,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and created fun projects using Scratch.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 18,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Discovered IoT\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 22,\n                                    columnNumber: 13\n                                }, undefined),\n                                \" and how devices communicate over the internet.\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 21,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                    children: \"Understood Computer Vision\"\n                                }, void 0, false, {\n                                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                                    lineNumber: 25,\n                                    columnNumber: 13\n                                }, undefined),\n                                ' and its role in making computers \"see.\"'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                            lineNumber: 24,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 14,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-yellow-100 p-4 border-l-4 border-yellow-500 rounded-lg mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"font-semibold\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                children: \"Creative Activity:\"\n                            }, void 0, false, {\n                                fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                                lineNumber: 31,\n                                columnNumber: 13\n                            }, undefined),\n                            \" Draw or design your own certificate of completion and celebrate your achievement!\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                        lineNumber: 30,\n                        columnNumber: 11\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 29,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                    className: \"text-lg mb-6\",\n                    children: \"Keep practicing and learning to master new skills. The sky's the limit!\"\n                }, void 0, false, {\n                    fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n                    lineNumber: 35,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n            lineNumber: 6,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"/mnt/d/Aamir/codesprint/code_sprint/pages/lessons/level1/5.3CompletionMessage.js\",\n        lineNumber: 5,\n        columnNumber: 5\n    }, undefined);\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CompletionMessage);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(pages-dir-node)/./pages/lessons/level1/5.3CompletionMessage.js\n");

/***/ }),

/***/ "(pages-dir-node)/./styles/globals.css":
/*!****************************!*\
  !*** ./styles/globals.css ***!
  \****************************/
/***/ (() => {



/***/ }),

/***/ "@splinetool/react-spline":
/*!*******************************************!*\
  !*** external "@splinetool/react-spline" ***!
  \*******************************************/
/***/ ((module) => {

"use strict";
module.exports = import("@splinetool/react-spline");;

/***/ }),

/***/ "fs":
/*!*********************!*\
  !*** external "fs" ***!
  \*********************/
/***/ ((module) => {

"use strict";
module.exports = require("fs");

/***/ }),

/***/ "next/dist/compiled/next-server/pages.runtime.dev.js":
/*!**********************************************************************!*\
  !*** external "next/dist/compiled/next-server/pages.runtime.dev.js" ***!
  \**********************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/pages.runtime.dev.js");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "prop-types":
/*!*****************************!*\
  !*** external "prop-types" ***!
  \*****************************/
/***/ ((module) => {

"use strict";
module.exports = require("prop-types");

/***/ }),

/***/ "react":
/*!************************!*\
  !*** external "react" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("react");

/***/ }),

/***/ "react-dom":
/*!****************************!*\
  !*** external "react-dom" ***!
  \****************************/
/***/ ((module) => {

"use strict";
module.exports = require("react-dom");

/***/ }),

/***/ "react/jsx-dev-runtime":
/*!****************************************!*\
  !*** external "react/jsx-dev-runtime" ***!
  \****************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-dev-runtime");

/***/ }),

/***/ "react/jsx-runtime":
/*!************************************!*\
  !*** external "react/jsx-runtime" ***!
  \************************************/
/***/ ((module) => {

"use strict";
module.exports = require("react/jsx-runtime");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@swc"], () => (__webpack_exec__("(pages-dir-node)/./node_modules/next/dist/build/webpack/loaders/next-route-loader/index.js?kind=PAGES&page=%2Flessons%2Flevel-1&preferredRegion=&absolutePagePath=.%2Fpages%2Flessons%2Flevel-1.js&absoluteAppPath=private-next-pages%2F_app&absoluteDocumentPath=private-next-pages%2F_document&middlewareConfigBase64=e30%3D!")));
module.exports = __webpack_exports__;

})();